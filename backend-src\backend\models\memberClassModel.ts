import {
	CreationOptional,
	DataTypes,
	ForeignKey,
	InferAttributes,
	InferCreationAttributes,
	Model,
	Sequelize,
} from 'sequelize';
import { DATABASE_TABLE_NAME } from '~config';
import { ClassCategory } from '~models/classCategoryModel';

export class MemberClass extends Model<InferAttributes<MemberClass>, InferCreationAttributes<MemberClass>> {
	//ATTRIBUTES
	declare memberClassId: CreationOptional<number>;
	declare className: string;
	declare classCategoryId?: ForeignKey<ClassCategory['classCategoryId'] | null>;
	declare minimumFirstRoundInterviews: number;
	//TIMESTAMPS
	declare createdAt: CreationOptional<Date>;
	declare updatedAt: CreationOptional<Date>;
	static initClass = (sequelize: Sequelize) =>
		MemberClass.init(
			{
				memberClassId: {
					type: DataTypes.INTEGER({ unsigned: true }),
					allowNull: false,
					primaryKey: true,
					autoIncrement: true,
				},
				className: { type: DataTypes.STRING, allowNull: false, unique: true },
				minimumFirstRoundInterviews: { type: DataTypes.INTEGER, defaultValue: 0 },
				createdAt: DataTypes.DATE,
				updatedAt: DataTypes.DATE,
			},
			{
				tableName: DATABASE_TABLE_NAME.MEMBER_CLASS,
				timestamps: true,
				paranoid: false,
				sequelize: sequelize,
				charset: 'utf8mb4',
				collate: 'utf8mb4_general_ci',
				name: {
					singular: 'MemberClass',
					plural: DATABASE_TABLE_NAME.MEMBER_CLASS,
				},
			},
		);
}

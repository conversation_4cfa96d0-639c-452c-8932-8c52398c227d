export default {
	HOST: process.env.DB_HOST as string,
	USER: process.env.DB_USER as string,
	PASSWORD: process.env.DB_PASSWORD as string,
	DB: process.env.DB_DB as string,
	DIALECT: process.env.DB_DIALECT as string,
	PORT: process.env.DB_PORT as undefined,
	POOL: {
		max: process.env.DB_MAX_CONNECTION ? parseInt(process.env.DB_MAX_CONNECTION) : 10,
		min: process.env.DB_MIN_CONNECTION ? parseInt(process.env.DB_MIN_CONNECTION) : 0,
		acquire: process.env.DB_TIMEOUT ? parseInt(process.env.DB_TIMEOUT) : 30000,
		idle: process.env.DB_TIME_DISCONECT ? parseInt(process.env.DB_TIME_DISCONECT) : 10000,
	},
	LOGGING: process.env.DB_LOGGING as string,
};

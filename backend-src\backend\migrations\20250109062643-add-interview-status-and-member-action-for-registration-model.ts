import { DATABASE_TABLE_NAME, MEMBER_ACTIONS_STATUS, MEMBER_INTERVIEW_STATUS } from '../config';
import { SequelizeUtility } from '../utilities';

import type { Migration } from 'sequelize-cli';

module.exports = {
	async up(queryInterface, Sequelize) {
		const NEW_COLUMNS = [
			{
				name: 'interviewStatus',
				type: {
					type: Sequelize.ENUM(...Object.values(MEMBER_INTERVIEW_STATUS)),
					allowNull: false,
					defaultValue: MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED,
				},
			},
			{
				name: 'memberActionStatus',
				type: {
					type: Sequelize.ENUM(...Object.values(MEMBER_ACTIONS_STATUS)),
					allowNull: true,
				},
			},
			{
				name: 'responseDeadlineDatePassInterview',
				type: { type: Sequelize.DATEONLY, allowNull: true },
			},
		];
		for (const newColumn of NEW_COLUMNS) {
			if (await SequelizeUtility.columnNotExists(queryInterface, DATABASE_TABLE_NAME.REGISTRATIONS, newColumn.name)) {
				await queryInterface.addColumn(DATABASE_TABLE_NAME.REGISTRATIONS, newColumn.name, newColumn.type);
			}
		}
	},

	async down(queryInterface) {
		const NEW_COLUMNS = ['interviewStatus', 'memberActionStatus', 'responseDeadlineDatePassInterview'];
		for (const newColumn of NEW_COLUMNS) {
			if (await SequelizeUtility.columnExists(queryInterface, DATABASE_TABLE_NAME.REGISTRATIONS, newColumn)) {
				await queryInterface.removeColumn(DATABASE_TABLE_NAME.REGISTRATIONS, newColumn);
			}
		}
	},
} satisfies Migration;

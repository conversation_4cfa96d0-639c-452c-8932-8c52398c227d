import { NextFunction, Request, Response } from 'express';
import { RESPONSE_SUCCESS, NOT_FOUND, SETTING_TYPES, BAD_REQUEST } from '../config';
import * as InterviewSettingsService from '../services/interviewSettings.service';
import * as InterviewSettingTimesService from '../services/interviewSettingTimes.service';
import { Transaction } from 'sequelize';
import { db } from '~models';
import { AppError } from '~utilities';
import { createInterviewSettingsSchema } from '~schemas/createInterviewSettings';
import { ClassCategoriesService } from '~services';

export const createInterviewSettings = async (req: Request, res: Response, next: NextFunction) => {
	let transaction: Transaction | null = null;
	try {
		transaction = await db.sequelize.transaction();
		const validatedBody = createInterviewSettingsSchema.parse(req.body);
		const { startDate, endDate, settingType, classCategoryIds, interviewSettingTimes } = validatedBody;

		const isClassCategoryExists = await ClassCategoriesService.checkClassCategoriesExist(classCategoryIds);
		if (!isClassCategoryExists) {
			throw new AppError(BAD_REQUEST, 'Some class categories do not exist', false);
		}
		const interviewSetting = await InterviewSettingsService.createInterviewSettings({
			startDate,
			endDate,
			settingType,
			classCategoryIds,
			transaction,
		});

		const interviewSettingId = interviewSetting.interviewSettingId;

		// if (settingType === SETTING_TYPES.FIXED_TIME || settingType === SETTING_TYPES.WEEKLY_TIME) {
		const dataInterviewSettingTimes: any[] = [];

		// get bản ghi con từ startDate đến endDate
		const dateAndCategoryExists = await InterviewSettingTimesService.getInterviewSettingTimesFromToDate({
			from: startDate,
			to: endDate,
			transaction,
		});

		// map lấy ra thông tin date và classCategoryId từ DB từ startDate đến endDate
		const dateInterviewSettingInDB = dateAndCategoryExists.map((item: any) => ({
			date: item.date,
			classCategoryId: item.classCategoryId,
		}));

		// map lấy ra thông tin date và classCategoryId từ body có trong DB rồi
		const dateInterviewSettingOnlyInDB: any[] = [];

		// setting isBlockedCategory cho trường hợp chặn và ngược lại
		const isBlockedCategory = SETTING_TYPES.NO_INTERVIEW ? true : false;

		// mapping bản ghi lấy từ body cho ngày đi với classCategory được setting
		classCategoryIds.forEach((classCategoryId: number) => {
			interviewSettingTimes.forEach((interviewSettingTime: any) => {
				// lấy ra các ngày và classCategory từ body không tồn tại trong DB
				if (
					!dateInterviewSettingInDB.some(
						(item) => item.date === interviewSettingTime.date && item.classCategoryId === classCategoryId,
					)
				) {
					dataInterviewSettingTimes.push({
						date: interviewSettingTime.date,
						startTime: interviewSettingTime.startTime,
						endTime: interviewSettingTime.endTime,
						classCategoryId,
						isBlockedCategory,
					});
				} else {
					dateInterviewSettingOnlyInDB.push({
						date: interviewSettingTime.date,
						startTime: interviewSettingTime.startTime,
						endTime: interviewSettingTime.endTime,
						classCategoryId,
						isBlockedCategory,
					});
				}
			});
		});

		// Tạo các bản ghi với date và classCategoryId từ body không tồn tại trong DB
		await InterviewSettingTimesService.createInterviewSettingTimes({
			interviewSettingId,
			interviewSettingTimes: dataInterviewSettingTimes,
			transaction,
		});

		// Xóa các bản ghi với date và classCategoryId từ body tồn tại trong DB
		await InterviewSettingTimesService.updateInterviewSettingTimesWithDateExists({
			dateInterviewSettingOnlyInDB,
			transaction,
		});

		// } else if (settingType === SETTING_TYPES.NO_INTERVIEW) {
		// 	const dateAndCategoryExists: any[] = [];

		// 	classCategoryIds.forEach((classCategoryId: number) => {
		// 		interviewSettingTimes.forEach((interviewSettingTime: any) => {
		// 			if (!dateAndCategoryExists.includes({ date: interviewSettingTime.date, classCategoryId })) {
		// 				dateAndCategoryExists.push({
		// 					date: interviewSettingTime.date,
		// 					classCategoryId,
		// 				});
		// 			}
		// 		});
		// 	});

		// 	await InterviewSettingTimesService.updateInterviewSettingTimesWithDateExists({
		// 		dateAndCategoryExists,
		// 		transaction,
		// 	});
		// }
		await transaction.commit();
		res.sendStatus(RESPONSE_SUCCESS);
	} catch (e) {
		if (transaction != null) {
			await transaction.rollback();
		}
		next(e);
	}
};
/** 
startDate: date;
endDate: date;
settingType: number; // 1: time (8h -> 21h), 2: date + time(), 3: none
classCategoryIds: number[];
dates: [
    {
        date: date;
        startTime: time;
        endTime: time;
    },
    {
        date: date;
        startTime: time;
        endTime: time;
    },
    ...
];
*/
/** 
 * case 1: settingType = 1
 * case 2: settingType = 2
 * => dates: [{
        date: date;
        startTime: time;
        endTime: time;
    }]
 * case 2: settingType = 3
    => dates: [{
        date: date;
    }]
 */

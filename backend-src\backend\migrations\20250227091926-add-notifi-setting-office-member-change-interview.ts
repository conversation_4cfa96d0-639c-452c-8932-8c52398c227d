import { DATABASE_TABLE_NAME } from '../config';
import { SequelizeUtility } from '../utilities';

import type { Migration } from 'sequelize-cli';
import { DataTypes } from 'sequelize';

module.exports = {
	async up(queryInterface, Sequelize) {
		const NEW_COLUMNS = ['isMemberJoinedOffice', 'isMemberKeepOffice'];
		for (const newColumn of NEW_COLUMNS) {
			if (await SequelizeUtility.columnNotExists(queryInterface, DATABASE_TABLE_NAME.SPECTATORS, newColumn)) {
				await queryInterface.addColumn(DATABASE_TABLE_NAME.SPECTATORS, newColumn, {
					type: DataTypes.BOOLEAN,
					allowNull: false,
					defaultValue: false,
				});
			}
		}
	},

	async down(queryInterface) {
		const NEW_COLUMNS = ['isMemberJoinedOffice', 'isMemberKeepOffice'];
		for (const newColumn of NEW_COLUMNS) {
			if (await SequelizeUtility.columnExists(queryInterface, DATABASE_TABLE_NAME.SPECTATORS, newColumn)) {
				await queryInterface.removeColumn(DATABASE_TABLE_NAME.SPECTATORS, newColumn);
			}
		}
	},
} satisfies Migration;

import {
	Sequelize,
	Model,
	DataTypes,
	InferAttributes,
	InferCreationAttributes,
	CreationOptional,
	ForeignKey,
} from 'sequelize';
import { ClassCategory } from './classCategoryModel';
import { InterviewSettings } from './interviewSettingsModel';
import { DATABASE_TABLE_NAME } from '~config';

export class InterviewSettingTimes extends Model<
	InferAttributes<InterviewSettingTimes>,
	InferCreationAttributes<InterviewSettingTimes>
> {
	//ATTRIBUTES
	declare interviewSettingTimeId: CreationOptional<number>;
	declare interviewSettingId: ForeignKey<InterviewSettings['interviewSettingId'] | null>;
	declare date: Date;
	declare startTime: string;
	declare endTime: string;
	declare classCategoryId: ForeignKey<ClassCategory['classCategoryId']>;
	declare isBlockedCategory: boolean;

	// //TIMESTAMPS
	declare createdAt?: CreationOptional<Date>;
	declare updatedAt?: CreationOptional<Date>;

	static initClass = (sequelize: Sequelize) =>
		InterviewSettingTimes.init(
			{
				interviewSettingTimeId: {
					type: DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				date: {
					type: DataTypes.DATEONLY,
					allowNull: false,
				},
				startTime: {
					type: DataTypes.TIME,
					allowNull: false,
				},
				endTime: {
					type: DataTypes.TIME,
					allowNull: false,
				},
				isBlockedCategory: {
					type: DataTypes.BOOLEAN,
					allowNull: false,
					defaultValue: false,
				},
				createdAt: {
					allowNull: false,
					type: DataTypes.DATE,
					defaultValue: DataTypes.NOW,
				},
				updatedAt: {
					allowNull: false,
					type: DataTypes.DATE,
					defaultValue: DataTypes.NOW,
				},
			},
			{
				sequelize: sequelize,
				tableName: DATABASE_TABLE_NAME.INTERVIEW_SETTING_TIMES,
				timestamps: true,
				charset: 'utf8mb4',
				collate: 'utf8mb4_general_ci',
				name: {
					singular: 'interviewSettingTime',
					plural: DATABASE_TABLE_NAME.INTERVIEW_SETTING_TIMES,
				},
			},
		);
}

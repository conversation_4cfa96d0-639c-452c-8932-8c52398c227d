import type { Reminder } from '../models/reminderModel';

import _ from 'lodash';
import moment from 'moment';
import 'moment-timezone';

import { CreationAttributes, HasOne, literal, Op, Sequelize, Transaction, WhereAttributeHash } from 'sequelize';
import {
	DAYS_REMINDER,
	INTERVIEWS_STEP_LABELS,
	MEMBER_INTERVIEW_STATUS,
	REMINDER_NOTIFY_TYPES,
	TIME_ZONE_DEFAULT,
	systemConfig,
	LABEL_INTERVIEW_STATUS,
	LABEL_MEMBER_ACTIONS_STATUS,
	SUBJECT_EMAIL_SENDER,
} from '~config';
import { CustomerRegistrationService, MailService } from '~services';
import { reminderReplaceString } from '~utilities/common.utils';
import { isDateAfterOrEqualDays } from '~utilities/commonDateTime';

import { db } from '../models';
import { LineService } from '../services';
import { FlexContainer } from '@line/bot-sdk';
import { Member } from '~models/memberModel';
import { writeLog } from '~utilities';

export const createReminder = async (
	params: CreationAttributes<Reminder> & {
		startAt: string | Date;
		fullName: string;
		telephone?: string | null;
		confirmationUrl?: string | null;
		buildingInfo: string;
		timeZone: string;
	},
	regexps: {
		replacerName: RegExp;
		replacerDateTime: RegExp;
		replacerTelephoneCompany: RegExp;
		replacerConfirmationUrl: RegExp;
		replacerBuilding: RegExp;
	},
	transaction?: Transaction,
) => {
	const messageToClient = params.message;
	let reminderMessage = messageToClient.replace(regexps.replacerName, `${params.fullName}`);
	reminderMessage = reminderMessage.replace(
		regexps.replacerDateTime,
		params.startAt ? moment(params.startAt).tz(TIME_ZONE_DEFAULT).format('YYYY年MM月DD日HH時mm分') : '',
		// moment(params.startAt).format('YYYY年MM月DD日HH時mm分')
	);
	reminderMessage = reminderMessage.replace(regexps.replacerTelephoneCompany, `${params.telephone ?? ''}`);
	reminderMessage = reminderMessage.replace(regexps.replacerConfirmationUrl, `${params.confirmationUrl ?? ''}`);
	reminderMessage = reminderMessage.replace(regexps.replacerBuilding, params.buildingInfo);
	return await db.reminders.create(
		{
			memberId: params.memberId,
			registrationId: params.registrationId,
			message: reminderMessage,
			remindDT: params.remindDT,
			key: params.key,
		},
		{ transaction },
	);
};

export const getReminders = async (where: WhereAttributeHash, transaction?: Transaction) => {
	return db.reminders.findAll({
		where: where,
		include: {
			association: db.reminders.associations.Member,
			where: {
				lineId: { [Op.not]: null },
				isFriends: true,
			},
			required: true,
			attributes: ['lineId'],
		},
		transaction,
	});
};

export const destroyReminders = async (reminderIds: number[], transaction?: Transaction) => {
	if (reminderIds.length == 0) {
		return;
	}
	return db.reminders.destroy({
		where: { reminderId: { [Op.in]: reminderIds } },
		transaction,
	});
};

export const destroyReminderByRegistrationId = async (registrationId: number, transaction?: Transaction) => {
	return db.reminders.destroy({
		where: { registrationId },
		transaction,
	});
};

export const destroyReminderByRegistrationIdAndType = async (
	registrationId: number,
	key: string,
	transaction?: Transaction,
) => {
	return db.reminders.destroy({
		where: { registrationId, key },
		transaction,
	});
};

export const createMessageReminderEvent = async ({
	messagesToClient,
	registrationResult,
	memberId,
	nameMember,
	timeZone,
	replacerName,
	replacerBuilding,
	replacerConfirmationUrl,
	replacerDateTime,
	replacerTelephoneCompany,
}: CreateMessageReminderEvent) => {
	const { isMessage, reminderMessageOneDay, reminderMessageThreeDays, startAt } = registrationResult;
	const eventStartAtISO = moment(startAt).toISOString();
	const messageReminderOneDay = isMessage ? reminderMessageOneDay : messagesToClient?.remind2?.valueString;
	const afterOrEqualOneDay = isDateAfterOrEqualDays(eventStartAtISO, DAYS_REMINDER.one_day);

	const confirmationUrl = messagesToClient?.bookConfirmationUrl?.valueString ?? '';
	const companyTelephone = messagesToClient?.companyTelephoneForTemplate?.valueString ?? '';
	const buildingInfo = registrationResult?.categoryTitle;

	if (messageReminderOneDay && afterOrEqualOneDay) {
		await createReminder(
			{
				startAt: registrationResult.startAt,
				remindDT: registrationResult.startAt,
				registrationId: registrationResult.registrationId,
				memberId: memberId,
				message: messageReminderOneDay,
				fullName: nameMember ?? '',
				telephone: companyTelephone,
				confirmationUrl: confirmationUrl,
				key: REMINDER_NOTIFY_TYPES.one_day,
				buildingInfo: buildingInfo,
				timeZone: timeZone,
			},
			{
				replacerName,
				replacerBuilding,
				replacerConfirmationUrl,
				replacerDateTime,
				replacerTelephoneCompany,
			},
		);
	}

	const messageReminderAnotherDays = isMessage ? reminderMessageThreeDays : messagesToClient?.remind1?.valueString;
	const afterAnotherDays = isDateAfterOrEqualDays(eventStartAtISO, DAYS_REMINDER.seven_day);

	if (messageReminderAnotherDays && afterAnotherDays) {
		await createReminder(
			{
				startAt: registrationResult?.startAt,
				remindDT: registrationResult.startAt,
				registrationId: registrationResult?.registrationId,
				memberId: memberId,
				message: messageReminderAnotherDays,
				fullName: nameMember ?? '',
				telephone: companyTelephone,
				confirmationUrl: confirmationUrl,
				key: REMINDER_NOTIFY_TYPES.another_day,
				buildingInfo: buildingInfo,
				timeZone: timeZone,
			},
			{
				replacerName,
				replacerBuilding,
				replacerConfirmationUrl,
				replacerDateTime,
				replacerTelephoneCompany,
			},
		);
	}
	return true;
};

const getSystemSettingValue = async (name: string) => {
	const setting = await db.systemSettings.findOne({
		where: { name },
		attributes: ['valueString'],
		raw: true,
	});
	return setting?.valueString || null;
};

// Helper function to get members with valid lineId
const getMembersWithLineId = async (memberIds: number[]) => {
	return await db.members.findAll({
		where: {
			memberId: { [Op.in]: memberIds },
			lineId: { [Op.not]: null },
		},
		attributes: ['lineId'],
		raw: true,
	});
};

// Helper function to send messages to members
const sendMessagesToMembers = async (members: { lineId: string }[], message: string) => {
	for (const member of members) {
		const msg = reminderReplaceString(message, member);
		writeLog(
			{
				msg: 'sendMessagesToMembers',
				content: msg,
				member,
			},
			'info',
		);

		await LineService.sendMessage(member.lineId as string, {
			type: 'text',
			text: msg,
		});
	}
};

export const getReminderConfig = async (
	key: string,
): Promise<{ success: false } | { success: true; message: string; fullNameKey: string }> => {
	const message = await getSystemSettingValue(key);

	const fullNameField = await CustomerRegistrationService.getFullNameField();

	if (!message || !fullNameField) return { success: false };

	const fullNameKey = `customerRegistrationId${fullNameField.customerRegistrationId}`;

	return {
		success: true,
		message,
		fullNameKey,
	};
};

// Refactored function for messageDeliveredAfterReservationReminder
export const messageDeliveredAfterReservationReminder = async (memberIds: number[]) => {
	try {
		const reminderConfig = await getReminderConfig('messageDeliveredAfterReservation');

		if (!reminderConfig.success) return;

		const { fullNameKey, message } = reminderConfig;

		const members = (await db.occasionMembers.findAll({
			where: {
				occasionMemberId: { [Op.in]: memberIds },
			},
			include: [
				{
					association: new HasOne(db.occasions, db.occurrences, {
						foreignKey: 'occasionId',
					}),
					attributes: [],
					on: literal(
						'`occurrence`.`occasionId` = `OccasionMember`.`occasionId` AND `occurrence`.`interviewStep` = `OccasionMember`.`memberInterviewCurrentStep`',
					),
				},
				{
					model: db.occasions,
					attributes: [],
					include: [
						{
							model: db.categories,
							attributes: [],
						},
					],
				},
				{
					model: db.members,
					attributes: [],
				},
			],
			attributes: [
				[literal('`occurrence`.`occurrenceId`'), 'occurrenceId'],
				[literal('`occurrence`.`responseDeadlineDate`'), 'DATE'],
				[literal('`Occasion->Category`.`title`'), '事務所名'],
				[literal('`Member`.`lineId`'), 'lineId'],
				[literal(`COALESCE(\`Member\`.\`${fullNameKey}\`, \`Member\`.\`displayName\`)`), 'NAME'],
			],
			raw: true,
		})) as unknown as { DATE: string; 事務所名: string; lineId: string; NAME: string; occurrenceId: string }[];

		for (const member of members) {
			const altText = reminderReplaceString(message, member);

			const msg: FlexContainer = {
				type: 'bubble',
				hero: {
					type: 'box',
					layout: 'vertical',
					contents: [
						{
							type: 'text',
							text: '面接依頼が来ました',
							align: 'center',
							weight: 'bold',
							gravity: 'center',
							size: 'sm',
							lineSpacing: '18px',
						},
					],
					backgroundColor: '#CECECE',
					justifyContent: 'center',
					alignItems: 'center',
					height: '30px',
				},
				body: {
					type: 'box',
					layout: 'vertical',
					contents: [
						{
							type: 'text',
							weight: 'bold',
							size: 'xs',
							text: reminderReplaceString(message, member),
							wrap: true,
						},
					],
				},
				footer: {
					type: 'box',
					layout: 'vertical',
					spacing: 'sm',
					contents: [
						{
							type: 'button',
							style: 'link',
							height: 'sm',
							action: {
								type: 'uri',
								label: '日程調整をする',
								uri: `${systemConfig.LINE.LINE_LIFF_URI}?occasionsConfirmInterview=${member.occurrenceId}`,
							},
						},
					],
					flex: 0,
				},
			};

			await LineService.sendFlexMessage(member.lineId as string, msg, altText);
		}
	} catch (error) {
		console.error('Error in messageDeliveredAfterReservationReminder:', error);
	}
};

// Refactored function for messageSentToStudentAfterInterviewRequest
export const messageSentToStudentAfterInterviewRequest = async (
	memberIds: number[],
	occasionId: number,
	responseDeadlineDatePassInterview: string | null,
) => {
	try {
		const reminderConfig = await getReminderConfig('messageSentToStudentAfterInterviewRequest');

		if (!reminderConfig.success) return;

		const { fullNameKey, message } = reminderConfig;

		const members = (await db.occasionMembers.findAll({
			raw: true,
			where: {
				memberId: {
					[Op.in]: memberIds,
				},
				occasionId,
			},
			include: [
				{
					association: new HasOne(db.occasions, db.registrations, {
						foreignKey: 'occasionId',
					}),
					on: literal(
						'`Registration`.`occasionId` = `OccasionMember`.`occasionId` AND `Registration`.`memberId` = `OccasionMember`.`memberId` AND `Registration`.`interviewStatus` = `OccasionMember`.`interviewStatus`',
					),
					where: {
						cancelledAt: null,
						attended: 0, // Ignore pass interview for before step
					},
					attributes: [],
					required: true,
				},
				{
					model: db.occasions,
					attributes: [],
					include: [
						{
							model: db.categories,
							attributes: [],
						},
					],
				},
				{
					model: db.members,
					attributes: [],
				},
			],
			attributes: [
				[literal('`Occasion->Category`.`title`'), '事務所名'],
				[literal(`'${responseDeadlineDatePassInterview || '[DATE]'}'`), 'DATE'],
				[literal('`Member`.`lineId`'), 'lineId'],
				[literal(`COALESCE(\`Member\`.\`${fullNameKey}\`, \`Member\`.\`displayName\`)`), 'NAME'],
				[
					literal('COALESCE(`Registration`.`memberActionStatus`, `OccasionMember`.`interviewStatus`)'),
					'選考ステータス',
				],
			],
		})) as unknown as { DATE: string; 事務所名: string; lineId: string; NAME: string; 選考ステータス: string }[];

		await sendMessagesToMembers(
			members.map((member) => ({
				...member,
				選考ステータス:
					_.get(LABEL_MEMBER_ACTIONS_STATUS, member.選考ステータス) ||
					_.get(LABEL_INTERVIEW_STATUS, member.選考ステータス) ||
					'－',
			})),
			message,
		);
	} catch (error) {
		console.error('Error in messageSentToStudentAfterInterviewRequest:', error);
	}
};

export const notificationMessageForInterviewEventDeadline = async () => {
	try {
		const noticeContent = await getSystemSettingValue('notificationMessageForInterviewEventDeadline');
		const responseDeadlineDate = moment().subtract(1, 'day').format('YYYY-MM-DD');

		console.log('notificationMessageForInterviewEventDeadlineStart', new Date(), responseDeadlineDate, !noticeContent);

		if (!noticeContent) return;

		const occurrences = (await db.occurrences.findAll({
			where: {
				responseDeadlineDate,
			},
			group: ['`occurrence`.`occurrenceId`'],
			attributes: [
				//
				[Sequelize.literal('`Category->Manager`.`emailContact`'), 'emailContact'],
				[Sequelize.literal('`occurrence`.`occasionId`'), 'occasionId'],
				[Sequelize.literal('`Category`.`title`'), 'companyName'],
				[Sequelize.literal('`Occasion`.`title`'), 'BUILDING'],
				[Sequelize.literal('`occurrence`.`interviewStep`'), 'currentStep'],
			],
			include: [
				{
					model: db.categories,
					required: true,
					attributes: [],
					include: [
						{
							model: db.managers,
							attributes: [],
							required: true,
							where: {
								emailContact: {
									[Op.not]: null,
								},
							},
						},
					],
				},
				{
					model: db.occasions,
					attributes: [],
					include: [
						{
							model: db.occasionMembers,
							attributes: [],
						},
					],
				},
			],
			raw: true,
		})) as unknown as { emailContact: string; companyName: string; BUILDING: string; currentStep: string }[];

		writeLog(
			{
				msg: 'notificationMessageForInterviewEventDeadline',
				total: occurrences?.length,
				data: occurrences,
			},
			'info',
		);

		const occurrencesGroupByOccasionId = _.groupBy(occurrences, 'occasionId');

		for (const occasionId of Object.keys(occurrencesGroupByOccasionId)) {
			const occurrences = _.get(occurrencesGroupByOccasionId, occasionId);

			const promises = occurrences.map((occurrence) => {
				const to = occurrence.emailContact;
				const contentSetting = reminderReplaceString(noticeContent, {
					事務所名: occurrence.companyName,
				});
				const subject = reminderReplaceString(SUBJECT_EMAIL_SENDER, {
					面接ステップ: _.get(INTERVIEWS_STEP_LABELS, occurrence.currentStep),
					BUILDING: occurrence.BUILDING,
				});
				writeLog(
					{
						msg: 'notificationMessageForInterviewEventDeadlineInfoEmail',
						subject,
						to,
					},
					'info',
				);
				return MailService.sendMail(
					to,
					subject,
					JSON.stringify(contentSetting).replace(/\\n/g, '<br />').replace(/^"/g, '').replace(/"$/g, ''),
				);
			});

			await Promise.all(promises);
		}
	} catch (error) {
		writeLog(
			{
				msg: '🚀 ~ file: reminderService.ts:267 ~ notificationMessageForInterviewEventDeadline ~ error:',
				error,
			},
			'error',
		);
		if (systemConfig.CONSOLE_ONLY) {
			console.log('🚀 ~ file: reminderService.ts:267 ~ notificationMessageForInterviewEventDeadline ~ error:', error);
		}
	}
};

const fetchMembersAndSendReminderWithOccasions = async (daysOffset: number, reminderType: string) => {
	try {
		const reminderConfig = await getReminderConfig(reminderType);

		if (!reminderConfig.success) return;

		const { fullNameKey, message } = reminderConfig;

		const date = moment().add(daysOffset, 'days');
		const from = date.clone().startOf('day').format('YYYY-MM-DD HH:mm:ss');
		const to = date.clone().endOf('day').format('YYYY-MM-DD HH:mm:ss');

		const members = (await db.occasionMembers.findAll({
			where: {
				interviewStatus: MEMBER_INTERVIEW_STATUS.INTERVIEW_PASSED,
			},
			include: [
				{
					association: new HasOne(db.occasions, db.registrations, {
						foreignKey: 'occasionId',
					}),
					on: literal(
						'`Registration`.`occasionId` = `OccasionMember`.`occasionId` AND `Registration`.`memberId` = `OccasionMember`.`memberId` AND `Registration`.`interviewStatus` = `OccasionMember`.`interviewStatus`',
					),
					required: true,
					where: {
						interviewStatus: MEMBER_INTERVIEW_STATUS.INTERVIEW_PASSED,
						attended: 0,
						cancelledAt: null,
						memberId: {
							[Op.not]: null,
						},
						responseDeadlineDatePassInterview: {
							[Op.between]: [from, to],
						},
					},
					attributes: [],
				},
				{
					model: db.occasions,
					attributes: [],
					include: [
						{
							model: db.categories,
							attributes: [],
						},
					],
				},
				{
					model: db.members,
					attributes: [],
				},
			],

			attributes: [
				[literal('`Occasion->Category`.`title`'), '事務所名'],
				[literal('`Member`.`lineId`'), 'lineId'],
				[literal(`COALESCE(\`Member\`.\`${fullNameKey}\`, \`Member\`.\`displayName\`)`), 'NAME'],
				[literal('`Registration`.`responseDeadlineDatePassInterview`'), 'DATE'],
			],
			raw: true,
			logging: true,
		})) as unknown as { DATE: string; 事務所名: string; lineId: string; NAME: string }[];

		writeLog(
			{
				msg: 'fetchMembersAndSendReminderWithOccasions',
				members,
				reminderType,
				daysOffset,
				from,
				to,
			},
			'info',
		);

		await sendMessagesToMembers(members, message);
	} catch (error) {
		console.error(`🚀 ~ ${reminderType} ~ error:`, error);
	}
};

export const interviewPassDeadlineReminderThreeDaysBefore = async () => {
	await fetchMembersAndSendReminderWithOccasions(3, 'interviewPassDeadlineReminderThreeDaysBefore');
};

export const interviewPassDeadlineReminderOneDayBefore = async () => {
	await fetchMembersAndSendReminderWithOccasions(1, 'interviewPassDeadlineReminderOneDayBefore');
};

/**
 * Sends reminders to members about schedule response deadlines
 * @param reminderType - The type of reminder to send
 * @param daysBeforeDeadline - Number of days before the deadline
 */
export const sendScheduleResponseDeadlineReminder = async (
	reminderType: string,
	daysBeforeDeadline: number,
): Promise<void> => {
	try {
		const reminderConfig = await getReminderConfig(reminderType);
		if (!reminderConfig.success) return;

		const { fullNameKey, message } = reminderConfig;
		const date = moment().add(daysBeforeDeadline, 'days');
		const from = date.clone().startOf('day').format('YYYY-MM-DD HH:mm:ss');
		const to = date.clone().endOf('day').format('YYYY-MM-DD HH:mm:ss');

		console.log('sendScheduleResponseDeadlineReminder', date, reminderType);

		const members = (await db.occasionMembers.findAll({
			raw: true,
			where: {
				interviewStatus: MEMBER_INTERVIEW_STATUS.SCHEDULE_ADJUSTMENT,
				'$occurrence.responseDeadlineDate$': {
					[Op.between]: [from, to],
				},
			},
			include: [
				{
					association: new HasOne(db.occasionMembers, db.occurrences, {
						foreignKey: 'occasionId',
					}),
					on: literal(
						'`occurrence`.`occasionId` = `OccasionMember`.`occasionId` AND `occurrence`.`interviewStep` = `OccasionMember`.`memberInterviewCurrentStep`',
					),
					required: true,
					include: [
						{
							model: db.categories,
							required: true,
						},
					],
				},
				{
					model: db.members,
					required: true,
				},
			],
			attributes: [
				[Sequelize.literal('`occurrence`.`responseDeadlineDate`'), 'DATE'],
				[Sequelize.literal('`Member`.`lineId`'), 'lineId'],
				[Sequelize.literal('`occurrence->Category`.`title`'), '事務所名'],
				[literal(`COALESCE(\`Member\`.\`${fullNameKey}\`, \`Member\`.\`displayName\`)`), 'NAME'],
			],
		})) as unknown as { DATE: string; lineId: string; 事務所名: string; NAME: string }[];

		writeLog(
			{
				msg: reminderType,
				members,
				from,
				to,
				reminderConfig,
				total: members.length,
			},
			'info',
		);

		await sendMessagesToMembers(members, message);
	} catch (error) {
		writeLog({ msg: `${reminderType} error`, error }, 'error');
	}
};

export const scheduleResponseDeadlineReminderThreeDaysBefore = async (): Promise<void> => {
	await sendScheduleResponseDeadlineReminder('scheduleResponseDeadlineReminderThreeDaysBefore', 3);
};

export const scheduleResponseDeadlineReminderOneDayBefore = async (): Promise<void> => {
	await sendScheduleResponseDeadlineReminder('scheduleResponseDeadlineReminderOneDayBefore', 1);
};

export const reservationMessage = async (occurrenceDetailId: number, memberInfo: Member) => {
	try {
		const reminderConfig = await getReminderConfig('watchRegistrationTemplate');

		const spectators = (await db.spectators.findAll({
			where: {
				isSpectatingRegistration: true,
			},
			include: [
				{
					model: db.members,
					attributes: [],
				},
			],
			raw: true,
			attributes: [[Sequelize.col('Member.lineId'), 'id']],
		})) as unknown as { id: string }[];

		if (!reminderConfig.success || !spectators.length) return;

		const { message, fullNameKey } = reminderConfig;

		const member = await db.members.findOne({
			where: {
				memberId: memberInfo.memberId,
			},
			attributes: [fullNameKey, 'displayName', 'lineId'],
			raw: true,
		});

		const occurrenceDetail = (await db.occurrenceDetailModel.findOne({
			raw: true,
			where: {
				occurrenceDetailId,
			},
			include: [
				{
					model: db.occurrences,
					attributes: [],
					include: [
						{
							model: db.occasions,
							attributes: [],
						},
					],
				},
			],
			attributes: [
				[literal('`occurrence->Occasion`.`title`'), 'BUILDING'],
				[literal('`occurrenceDetail`.`eventDay`'), 'eventDay'],
				[literal('`occurrenceDetail`.`startTime`'), 'startTime'],
				[literal('`occurrenceDetail`.`endTime`'), 'endTime'],
			],
		})) as unknown as {
			BUILDING: string;
			eventDay: string;
			startTime: string;
			endTime: string;
		};

		for (const spectator of spectators) {
			await sendMessagesToMembers(
				[
					{
						lineId: spectator.id,
						BUILDING: occurrenceDetail.BUILDING,
						DATE: `${occurrenceDetail.eventDay}（火）${occurrenceDetail.startTime.replace(
							/:00$/,
							'',
						)}～${occurrenceDetail.endTime.replace(/:00$/, '')}`,
						NAME: _.get(member, fullNameKey, member!.displayName),
					} as any,
				],
				message,
			);
		}
	} catch (error) {
		console.log('🚀 ~ file: reminderService.ts:645 ~ reservationMessage ~ error:', error);
	}
};

export const reservationCancelMessage = async (registrationId: number) => {
	try {
		const reminderConfig = await getReminderConfig('watchRegistrationCancelTemplate');

		const spectators = (await db.spectators.findAll({
			where: {
				isSpectatingRegistration: true,
			},
			include: [
				{
					model: db.members,
					attributes: [],
				},
			],
			raw: true,
			attributes: [[Sequelize.col('Member.lineId'), 'id']],
		})) as unknown as { id: string }[];

		if (!reminderConfig.success || !spectators.length) return;

		const { message, fullNameKey } = reminderConfig;

		const member = (await db.registrations.findOne({
			where: {
				registrationId,
			},
			include: [
				{
					model: db.members,
					attributes: [],
				},
				{
					model: db.occurrences,
					attributes: [],
					include: [
						{
							model: db.occurrenceDetailModel,
							attributes: [],
						},
					],
				},
				{
					model: db.occasions,
					attributes: [],
				},
			],
			attributes: [
				[literal('`occurrence->occurrenceDetails`.`eventDay`'), 'eventDay'],
				[literal('`occurrence->occurrenceDetails`.`startTime`'), 'startTime'],
				[literal('`occurrence->occurrenceDetails`.`endTime`'), 'endTime'],
				[literal('`Occasion`.`title`'), 'BUILDING'],
				[literal('`Member`.`lineId`'), 'lineId'],
				[literal(`COALESCE(\`Member\`.\`${fullNameKey}\`, \`Member\`.\`displayName\`)`), 'NAME'],
			],
			raw: true,
		})) as unknown as {
			eventDay: string;
			startTime: string;
			endTime: string;
			BUILDING: string;
			lineId: string;
			NAME: string;
		};

		for (const spectator of spectators) {
			await sendMessagesToMembers(
				[
					{
						lineId: spectator.id,
						BUILDING: member.BUILDING,
						DATE: `${member.eventDay}（火）${member.startTime.replace(/:00$/, '')}～${member.endTime.replace(
							/:00$/,
							'',
						)}`,
						NAME: member.NAME,
					} as any,
				],
				message,
			);
		}
	} catch (error) {
		console.log('🚀 ~ file: reminderService.ts:726 ~ reservationCancelMessage ~ error:', error);
	}
};

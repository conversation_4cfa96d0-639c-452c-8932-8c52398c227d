import type { Migration } from 'sequelize-cli';

import { RICH_MENU_TYPE } from '../config';
import { SequelizeUtility } from '../utilities';

module.exports = {
	async up(queryInterface, Sequelize) {
		if (await SequelizeUtility.columnExists(queryInterface, 'RichMenus', 'type')) {
			await queryInterface.changeColumn('RichMenus', 'type', {
				type: Sequelize.ENUM,
				values: Object.values(RICH_MENU_TYPE),
				allowNull: false,
			});
		}
	},

	async down(queryInterface, Sequelize) {
		if (await SequelizeUtility.columnExists(queryInterface, 'RichMenus', 'type')) {
			await queryInterface.changeColumn('RichMenus', 'type', {
				type: Sequelize.ENUM,
				values: [RICH_MENU_TYPE.DEFAULT, 'memberRM'],
				allowNull: false,
			});
		}
	},
} satisfies Migration;

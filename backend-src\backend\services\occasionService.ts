import path = require('path');
import {
	cast,
	col,
	CreationAttributes,
	fn,
	HasMany,
	IncludeOptions,
	literal,
	Op,
	Sequelize,
	Transaction,
	WhereAttributeHash,
} from 'sequelize';
import { BAD_REQUEST, SYSTEM_ERROR, systemConfig } from '../config';
import { db } from '../models';
import { OccasionDetail } from '../models/occasionDetailModel';
import { OccasionImage } from '../models/occasionImageModel';
import { Occasion } from '../models/occasionModel';
import { AppError, FileOps } from '../utilities';
import moment, { Moment } from 'moment';
import _, { omit } from 'lodash';
import { CreateOccasionSchema, UpdateOccasionDisplaySchema, UpdateOccasionSchema } from '~schemas/occasions';
import { OccurrenceDetail } from '~models/occurrenceDetailModel';
import { GetMemberOccasionSchema } from '~schemas/occasionMember';
import { CategoryService } from '../services';

export const listOccasionsBare = async (
	include?: IncludeOptions | IncludeOptions[],
	isParanoid = true,
	isCampaign = false,
) =>
	db.occasions.findAll({
		where: isCampaign
			? {
					campaignId: {
						[Op.not]: null,
					},
			  }
			: {
					categoryId: {
						[Op.not]: null,
					},
			  },
		attributes: ['occasionId', isCampaign ? 'campaignId' : 'categoryId', 'title'],
		order: [['showOrder', 'asc']],
		include: include,
		paranoid: isParanoid,
	});

export const detailOccasion_Master = async ({
	occasionId,
	isParanoid = true,
	occurrenceWhere,
	transaction,
	search,
}: {
	occasionId: number;
	isParanoid: boolean;
	occurrenceWhere?: WhereAttributeHash;
	transaction?: Transaction;
	search: any;
}) => {
	const occasion = await db.occasions.findByPk(occasionId, {
		attributes: {
			include: [
				[fn('MAX', col('occurrences.startAt')), 'end'],
				[fn('MIN', col('occurrences.startAt')), 'start'],
				[cast(fn('SUM', col('occurrences.maxAttendee')), 'signed'), 'maxCapacity'],
				[cast(fn('IFNULL', fn('SUM', col('occurrences.registrations.expected')), 0), 'signed'), 'sumExpected'],
				[cast(fn('IFNULL', fn('SUM', col('occurrences.registrations.attended')), 0), 'signed'), 'sumAttended'],
				[
					Sequelize.literal(`(
						SELECT
							CAST(COALESCE(SUM(\`maxAttendee\`), 0) AS SIGNED)
						FROM
							occurrences
						WHERE
							\`Occasion\`.\`occasionId\` = \`occurrences\`.\`occasionId\`
					)`),
					'maximumAttendees',
				],
				[
					Sequelize.literal(`(
						SELECT
							CASE
							WHEN \`Occasion\`.\`groupBooking\` IS TRUE THEN CAST(COALESCE(SUM(\`companionCount\` + \`participantCount\`), 0) AS SIGNED)
								ELSE CAST(COALESCE(COUNT(\`registrationId\`), 0) AS SIGNED)
							END
						FROM
							registrations
						WHERE
							\`cancelledAt\` IS NULL
							AND \`Occasion\`.\`occasionId\` = \`registrations\`.\`occasionId\`
					)`),
					'reservationCount',
				],
				[
					Sequelize.literal(`(
						SELECT
							CASE
								WHEN \`Occasion\`.\`groupBooking\` IS TRUE THEN CAST(COALESCE(SUM(\`actualCompanionCount\` + \`actualParticipantCount\`), 0) AS SIGNED)
								ELSE CAST(COALESCE(COUNT(\`registrationId\`), 0) AS SIGNED)
							END
						FROM
							registrations
						WHERE
							\`attended\` IS TRUE
							AND \`Occasion\`.\`occasionId\` = \`registrations\`.\`occasionId\`
					)`),
					'attendeeCount',
				],
			],
			exclude: ['createdAt', 'updatedAt', 'deletedAt'],
		},
		include: [
			{
				separate: true,
				association: db.occasions.associations.cancelConditions,
				attributes: { exclude: ['createdAt', 'categoryId', 'updatedAt', 'id'] },
			},
			{
				association: db.occasions.associations.occurrences,
				attributes: [],
				include: [
					{
						association: db.occurrences.associations.registrations,
						attributes: [],
					},
				],
			},
			{
				model: db.classCategoryModel,
			},
		],
		paranoid: isParanoid,
		transaction,
		group: ['occasionId'],
	});
	if (occasion == null) {
		throw new AppError(SYSTEM_ERROR, `occasion ${occasionId} does not exist`, false);
	}
	const occasionInfo = await db.occasions.findByPk(occasionId, {
		attributes: ['occasionId'],
		include: [
			{
				association: db.occasions.associations.occasionDetails,
				attributes: { exclude: ['occasionDetailId', 'occasionId'] },
			},
			{
				association: db.occasions.associations.occasionImages,
				attributes: { exclude: ['occasionImageId', 'occasionId'] },
			},
			{
				model: db.categoryMessageDetails,
				attributes: { exclude: ['id', 'categoryId', 'occasionId'] },
			},
		],
		order: [
			[db.occasionDetails, col('showOrder'), 'asc'],
			[db.occasionImages, col('showOrder'), 'asc'],
		],
	});
	let start: any;
	let end: any;
	if (search && search.length === 2) {
		start = moment(search[0]);
		end = moment(search[1]);
	}

	const sortKey = occasion?.isSettingTime ? 'startDate' : 'startAt';
	const isGroupBooking = occasion?.groupBooking;

	const occurrences = await db.occurrences
		.findAll({
			where: occasion?.isSettingTime
				? {
						occasionId: occasion.occasionId,
						...(search && search.length === 2
							? {
									startDate: {
										[Op.gte]: start,
										[Op.lte]: end,
									},
							  }
							: {}),
				  }
				: { occasionId: occasion.occasionId, ...occurrenceWhere },
			attributes: {
				include: [
					[fn('DATE_FORMAT', literal('DATE_ADD(`startAt`, INTERVAL 9 HOUR)'), '%Y-%m-%d'), 'groupDate'],
					...((isGroupBooking
						? [
								[
									cast(
										fn(
											'IFNULL',
											Sequelize.literal(
												'SUM(`registrations`.`participantCount`) + SUM(`registrations`.`companionCount`)',
											),
											0,
										),
										'signed',
									),
									'sumExpected',
								],
								[
									cast(
										fn(
											'IFNULL',
											Sequelize.literal(
												'SUM(`registrations`.`actualParticipantCount`) + SUM(`registrations`.`actualCompanionCount`)',
											),
											0,
										),
										'signed',
									),
									'sumAttended',
								],
						  ]
						: [
								[cast(fn('IFNULL', fn('SUM', col('registrations.expected')), 0), 'signed'), 'sumExpected'],
								[cast(fn('IFNULL', fn('SUM', col('registrations.attended')), 0), 'signed'), 'sumAttended'],
						  ]) as any[]),
				],
				exclude: ['occasionId', 'createdAt', 'updatedAt', 'deletedAt'],
			},
			include: {
				association: db.occurrences.associations.registrations,
				attributes: [],
			},
			group: [col('occurrenceId')],
			transaction,
			order: [[sortKey, 'ASC']],
		})
		.then((occus) =>
			occus.map((occu) => {
				const o: any = occu.toJSON();
				delete o.registrations;
				return o;
			}),
		);

	const occurrencesTableView = await db.occurrences.findAll({
		where: occasion?.isSettingTime
			? {
					occasionId: occasion.occasionId,
					...(search && search.length === 2
						? {
								startDate: {
									[Op.gte]: start,
									[Op.lte]: end,
								},
						  }
						: {}),
			  }
			: { occasionId: occasion.occasionId, ...occurrenceWhere },
		include: [
			{
				model: db.registrations,
				attributes: [],
			},
			{
				model: db.occasions,
				attributes: [],
			},
		],
		attributes: {
			exclude: ['createdAt', 'updatedAt', 'deletedAt'],
			include: [
				...((isGroupBooking
					? [
							[
								Sequelize.literal(`(
									SELECT CAST(COALESCE(SUM(\`registrations\`.\`participantCount\`) + SUM(\`registrations\`.\`companionCount\`), 0) AS SIGNED)
									FROM \`registrations\`
									WHERE \`registrations\`.\`occurrenceId\` = \`Occurrence\`.\`occurrenceId\`
									AND \`registrations\`.\`cancelledAt\` IS NULL
								)`),
								'sumExpected',
							],
							[
								Sequelize.literal(`(
									SELECT CAST(COALESCE(SUM(\`registrations\`.\`actualParticipantCount\`) + SUM(\`registrations\`.\`actualCompanionCount\`), 0) AS SIGNED)
									FROM \`registrations\`
									WHERE \`registrations\`.\`occurrenceId\` = \`Occurrence\`.\`occurrenceId\`
									AND \`registrations\`.\`cancelledAt\` IS NULL
									AND \`registrations\`.\`attended\` IS TRUE
								)`),
								'sumAttended',
							],
					  ]
					: [
							[
								Sequelize.literal(`(
									SELECT CAST(COALESCE(SUM(\`registrations\`.\`expected\`), 0) AS SIGNED)
									FROM \`registrations\`
									WHERE \`registrations\`.\`occurrenceId\` = \`Occurrence\`.\`occurrenceId\`
									AND \`registrations\`.\`cancelledAt\` IS NULL
								)`),
								'sumExpected',
							],
							[
								Sequelize.literal(`(
									SELECT CAST(COALESCE(SUM(\`registrations\`.\`attended\`), 0) AS SIGNED)
									FROM \`registrations\`
									WHERE \`registrations\`.\`occurrenceId\` = \`Occurrence\`.\`occurrenceId\`
									AND \`registrations\`.\`cancelledAt\` IS NULL
									AND \`registrations\`.\`attended\` IS TRUE
								)`),
								'sumAttended',
							],
					  ]) as any[]),
				[
					Sequelize.literal(`(
							SELECT CAST(COALESCE(COUNT(\`registrations\`.\`registrationId\`), 0) AS SIGNED)
							FROM \`registrations\`
							WHERE \`registrations\`.\`occurrenceId\` = \`Occurrence\`.\`occurrenceId\`
							AND \`registrations\`.\`cancelledAt\` IS NOT NULL
						)`),
					'cancelCount',
				],
				[
					Sequelize.literal(`(
							SELECT CAST(COALESCE(SUM(\`transactions\`.\`amount\`), 0) AS SIGNED)
							FROM \`transactions\`
							JOIN \`registrations\` as \`Registration\` ON \`transactions\`.\`registration_id\` = \`Registration\`.\`registrationId\`
							WHERE \`transactions\`.\`status\` = 'FULFILLED'
							AND \`transactions\`.\`type\` = 'purchase'
							AND \`Registration\`.\`attended\` = 1
							AND \`Registration\`.\`occurrenceId\` = \`Occurrence\`.\`occurrenceId\`
						)`),
					'sales',
				],
				[Sequelize.literal('CAST(`Occasion`.`fee` as FLOAT)'), 'fee'],
			],
		},
		order: [
			//
			[Sequelize.literal(sortKey), 'ASC'],
		],
	});

	return { ...occasion.toJSON(), ...occasionInfo?.toJSON(), occurrences, occurrencesTableView };
};
export const detailOccasion_Member = async (
	occasionWhere?: WhereAttributeHash,
	occurrenceWhere?: WhereAttributeHash,
	transaction?: Transaction,
) => {
	const occasionInfo = await db.occasions
		.findOne({
			where: occasionWhere,
			attributes: {
				exclude: ['isDisplayed', 'showOrder', 'createdAt', 'updatedAt', 'deletedAt'],
			},
			include: [
				{
					separate: true,
					association: db.occasions.associations.occasionDetails,
					attributes: { exclude: ['occasionDetailId', 'occasionId'] },
					order: [[col('showOrder'), 'asc']],
				},
				{
					separate: true,
					association: db.occasions.associations.occasionImages,
					attributes: { exclude: ['occasionImageId', 'occasionId'] },
					order: [[col('showOrder'), 'asc']],
				},
				{
					separate: true,
					association: db.occasions.associations.cancelConditions,
				},
				{
					separate: true,
					order: [['showOrder', 'ASC']],
					association: new HasMany(db.occasions, db.categoryMessages, {
						foreignKey: 'categoryId',
						as: 'formMessages',
					}),
					on: Sequelize.literal('`formMessages`.`categoryId` = `Occasion`.`categoryId`'),
					attributes: [
						//
						'label',
						'type',
						'option',
						'required',
					],
				},
			],
			transaction,
		})
		.then((o) => o?.toJSON());
	if (occasionInfo == null) {
		throw new AppError(SYSTEM_ERROR, `occasion ${JSON.stringify(occasionWhere)} does not exist`, false);
	} else if (occasionInfo.isDisplayed == false) {
		return { ...occasionInfo, occurrences: [] };
	}
	const occurrences = await db.occurrences
		.findAll({
			where: {
				[Op.or]: [
					{ occasionId: occasionInfo.occasionId, ...occurrenceWhere },
					{ '$Occasion.isSettingTime$': true, occasionId: occasionInfo.occasionId },
				],
			},
			attributes: {
				include: [
					[fn('DATE_FORMAT', literal('DATE_ADD(`startAt`, INTERVAL 9 HOUR)'), '%Y-%m-%d'), 'groupDate'],
					[cast(fn('IFNULL', fn('SUM', col('registrations.expected')), 0), 'signed'), 'sumExpected'],
					[cast(fn('IFNULL', fn('SUM', col('registrations.attended')), 0), 'signed'), 'sumAttended'],
				],
				exclude: ['occasionId', 'createdAt', 'updatedAt', 'deletedAt'],
			},
			include: [
				{
					association: db.occurrences.associations.registrations,
					attributes: [],
				},
				{
					association: db.occurrences.associations.Occasion,
					attributes: [],
				},
			],
			group: [col('occurrenceId')],
			order: [['startAt', 'ASC']],
			transaction,
		})
		.then((os) => os.map((o) => o.toJSON()));
	return { ...occasionInfo, occurrences };
};
export const browseOccasionsCategoryMaster = async (categoryId: number, pagination: PaginationParamsNew) => {
	const occasionCount = await db.occasions.count({
		where: { categoryId: categoryId },
	});
	const occasionsListByCategory = await db.occasions.findAll({
		attributes: ['occasionId', 'isDisplayed', 'title', 'categoryId'],
		where: { categoryId: categoryId },
		limit: pagination.pageSize,
		offset: (pagination.page - 1) * pagination.pageSize,
		include: [
			{
				model: db.classCategoryModel,
				attributes: ['name'],
			},
		],
		order: [[pagination.sortKey, pagination.sort]],
	});
	return { total: occasionCount, list: occasionsListByCategory };
};
export const browseOccasionsMaster = async (
	campaignId: number,
	pagination: paginationParams,
	transaction?: Transaction,
) => {
	const occasionCount = await db.occasions.findAll({
		attributes: ['occasionId'],
		where: { campaignId: campaignId },
		limit: pagination.pp,
		offset: (pagination.p - 1) * pagination.pp,
		transaction,
	});
	const occasionIds = occasionCount.map((o) => o.occasionId);
	if (occasionIds.length > 0) {
		const rows = await Promise.all([
			db.occasions
				.findAll({
					where: { campaignId: campaignId, occasionId: { [Op.in]: occasionIds } },
					attributes: {
						include: [
							[fn('MIN', col('occurrences.startAt')), 'start'],
							[fn('MAX', col('occurrences.startAt')), 'end'],
							[cast(fn('IFNULL', fn('SUM', col('occurrences.maxAttendee')), 0), 'signed'), 'maxCapacity'],
							[cast(fn('IFNULL', fn('SUM', col('occurrences.registrations.expected')), 0), 'signed'), 'sumExpected'],
						],
					},
					include: {
						association: db.occasions.associations.occurrences,
						required: false,
						attributes: [],
						include: [
							{
								association: db.occurrences.associations.registrations,
								required: false,
								attributes: [],
							},
						],
					},
					group: ['occasionId'],
					order: [
						[col(pagination.sortKey), pagination.sort],
						[literal('end'), 'DESC'],
					],
					transaction,
				})
				.then((os) => os.map((o) => o.toJSON())),
			db.occasions
				.findAll({
					where: { campaignId: campaignId, occasionId: { [Op.in]: occasionIds } },
					include: [
						{
							separate: true,
							association: db.occasions.associations.occasionDetails,
							attributes: { exclude: ['occasionDetailId', 'occasionId'] },
							order: [['showOrder', 'asc']],
						},
						{
							separate: true,
							association: db.occasions.associations.occasionImages,
							attributes: { exclude: ['occasionImageId', 'occasionId'] },
							order: [['showOrder', 'asc']],
						},
					],
					order: [[col('occasionId'), 'asc']],
					limit: pagination.pp,
					transaction,
				})
				.then((os) => os.map((o) => o.toJSON())),
		]).then(([occasionsAggregated, occasionData]) =>
			occasionsAggregated.map((oA) => {
				const curOcca = occasionData.find((o) => o.occasionId == oA.occasionId);
				return curOcca ? { ...oA, ...curOcca } : { ...oA };
			}),
		);
		return { count: occasionIds.length, rows: rows };
	} else {
		return { rows: [], count: 0 };
	}
};
export const browseOccasions_Member = async (data: GetMemberOccasionSchema) => {
	const { page, pageSize, interviewStatus, memberLine } = data;
	const currentMember = await db.members.findOne({
		where: {
			lineId: memberLine.userId,
		},
		attributes: ['memberId'],
	});

	if (!currentMember) {
		throw new AppError(BAD_REQUEST, 'Member is not found.', false);
	}
	const { memberId } = currentMember;
	const occasions = await db.occasionMembers.findAll({
		where: { memberId, interviewStatus },
		include: [
			{
				model: db.occasions,
				where: {
					isDisplayed: true,
				},
				include: [
					{
						model: db.categories,
						where: {
							isDisplayed: true,
						},
					},
				],
			},
		],
	});
	const total = await db.occurrenceDetailModel.count({
		where: {},
		include: [
			{
				model: db.occurrences,
				where: { occasionId: { [Op.in]: occasions.map((item) => item.occasionId) } },
				include: [
					{
						model: db.occasions,
						attributes: ['title'],
					},
				],
			},
		],
	});

	const occurrencesDetails = await db.occurrenceDetailModel.findAll({
		where: {},
		include: [
			{
				model: db.occurrences,
				where: { occasionId: { [Op.in]: occasions.map((item) => item.occasionId) } },
				include: [
					{
						model: db.occasions,
						attributes: ['title'],
					},
				],
			},
		],
		limit: parseInt(pageSize),
		offset: (parseInt(page) - 1) * parseInt(pageSize),
	});
	return { list: occurrencesDetails, page, pageSize, total };
};

export const createOccasion = async (params: CreateOccasionSchema, transaction?: Transaction) => {
	await db.occasions.increment(
		{ showOrder: 1 },
		{
			where: { categoryId: params.categoryId },
			transaction,
		},
	);
	await db.categories.increment(
		{ numberOfEventDisplay: 1 },
		{
			where: { categoryId: params.categoryId },
			transaction,
		},
	);
	return db.occasions.create(params, { transaction });
};

export const createCampaignOccasion = async (params: CreationAttributes<Occasion>, transaction?: Transaction) =>
	db.occasions
		.increment(
			{ showOrder: 1 },
			{
				where: { campaignId: params.campaignId },
				transaction,
			},
		)
		.then(() =>
			db.occasions.create(
				{
					campaignId: params.campaignId,
					title: params.title,
					description: params.description,
					canOverlap: params.canOverlap,
					isDisplayed: params.isDisplayed,
				},
				{ transaction },
			),
		);

export const updateOccasion = async (data: UpdateOccasionSchema, transaction?: Transaction) => {
	const { occasionId } = data;
	await db.occasions.update(
		{
			..._.omit(data, 'occasionId'),
		},
		{
			where: {
				occasionId,
			},
			transaction,
		},
	);
	return true;
};

export const updateCampaignOccasion = async (
	occasionId: number,
	params: CreationAttributes<Occasion>,
	transaction?: Transaction,
) => {
	const occasions = await db.occasions.findByPk(occasionId);

	if (occasions == null) {
		throw new AppError(SYSTEM_ERROR, 'occasions does not exist', false);
	}

	return db.occasions.update(
		{
			title: params.title,
			description: params.description,
			canOverlap: params.canOverlap,
			isDisplayed: params.isDisplayed,
		},
		{
			where: { occasionId: occasionId },
			transaction,
		},
	);
};

export const updateOccasionOrder = async (
	params: { occasionId: number; showOrder: number }[],
	transaction?: Transaction,
) =>
	db.occasions
		.findAll({ where: { occasionId: { [Op.in]: params.map((p) => p.occasionId) } }, transaction })
		.then((occasions) =>
			Promise.all(
				occasions.map((o) => {
					const uo = params.find((p) => p.occasionId == o.occasionId);
					if (uo == undefined) {
						throw new Error(`uo not found ${o.occasionId}`);
					} else {
						return o.update({ showOrder: uo.showOrder }, { transaction });
					}
				}),
			),
		);
export const updateOccasionDetails = async (
	occasionId: number,
	details: CreationAttributes<OccasionDetail>[],
	transaction?: Transaction,
) =>
	db.occasionDetails
		.destroy({
			where: { occasionId: occasionId },
			transaction,
		})
		.then(() =>
			db.occasionDetails.bulkCreate(
				details.map((d) => ({ ...d, occasionId: occasionId })),
				{
					fields: ['occasionId', 'label', 'value', 'showOrder'],
					transaction,
				},
			),
		);

export const updateOccasionImages = async (
	occasionId: number,
	images: Express.Multer.File[],
	imageDetails: imageUpdateType[],
	transaction?: Transaction,
) => {
	let toRemove: OccasionImage[] = [];
	const toAdd: CreationAttributes<OccasionImage>[] = [];
	const toChange: { occasionImageId: number; showOrder: number }[] = [];
	const imagesInDB = await db.occasionImages.findAll({ where: { occasionId: occasionId }, transaction });
	for (const pD of imageDetails) {
		const picInDB = imagesInDB.find((pI: OccasionImage) => pI.picUrl == pD.originalName);
		if (picInDB == undefined) {
			const picFile = images.find((p) => p.originalname == pD.originalName)?.filename;
			toAdd.push({
				occasionId: occasionId,
				picUrl: picFile ? picFile : pD.originalName,
				showOrder: pD.showOrder,
			});
		} else if (picInDB != undefined && picInDB.showOrder != pD.showOrder) {
			toChange.push({ occasionImageId: picInDB.occasionImageId, showOrder: pD.showOrder });
		}
	}
	toRemove = imagesInDB.filter((pI) => imageDetails.every((pD) => pD.originalName != pI.picUrl));
	return await Promise.all([
		removeOccasionImageFiles(toRemove.map((pI) => pI.picUrl)),
		removeOccasionImages(
			toRemove.map((pI) => pI.occasionImageId),
			transaction,
		),
	]).then(() => Promise.all([changeOccasionImageOrders(toChange, transaction), addOccasionImages(toAdd, transaction)]));
};

export const deleteOccasion = async (occasionId: number, transaction?: Transaction) => {
	const currentOccasion = await db.occasions.findOne({
		where: {
			occasionId,
		},
	});
	if (!currentOccasion) {
		throw new AppError(BAD_REQUEST, `occasionId not found ${occasionId}`, true);
	}
	Promise.all([
		db.occasions.destroy({ where: { occasionId }, transaction }),
		db.occurrences.destroy({ where: { occasionId }, force: true, transaction }),
		db.occasionImages
			.findAll({ attributes: ['picUrl'], where: { occasionId }, transaction })
			.then((occasionImages) => removeOccasionImageFiles(occasionImages.map((oI) => oI.picUrl))),
	]);
	const categoryId = currentOccasion.categoryId;
	if (categoryId) {
		await CategoryService.incrementOrDecrementNumberOfEventDisplay(categoryId, -1, transaction);
	}
	return true;
};

export const deleteOccasionByCategoryId = async (categoryId: number, transaction?: Transaction) =>
	db.occasions
		.findAll({
			attributes: ['occasionId'],
			where: { categoryId: categoryId },
			include: { separate: true, association: db.occasions.associations.occasionImages, attributes: ['picUrl'] },
			transaction,
		})
		.then((occasions) => {
			const picUrls: string[] = [];
			const occasionIds: number[] = [];
			occasions.forEach((o) => {
				occasionIds.push(o.occasionId);
				if (o.occasionImages) {
					o.occasionImages.forEach((oI) => picUrls.push(oI.picUrl));
				}
			});
			return Promise.all([
				db.occasionImages.destroy({ where: { occasionId: { [Op.in]: occasionIds } } }),
				db.occasions.destroy({ where: { categoryId: categoryId }, transaction }),
				removeOccasionImageFiles(picUrls),
			]);
		});
export const deleteOccasionByCampaignId = async (campaignId: number, transaction?: Transaction) =>
	db.gifts
		.findAll({
			attributes: ['giftId'],
			where: { campaignId: campaignId },
			include: { separate: true, association: db.gifts.associations.occasionImages, attributes: ['picUrl'] },
			transaction,
		})
		.then((gifts) => {
			const picUrls: string[] = [];
			const giftIds: number[] = [];
			gifts.forEach((o) => {
				giftIds.push(o.giftId);
				if (o.occasionImages) {
					o.occasionImages.forEach((oI) => picUrls.push(oI.picUrl));
				}
			});
			return Promise.all([
				db.occasionImages.destroy({ where: { occasionId: { [Op.in]: giftIds } } }),
				db.gifts.destroy({ where: { campaignId: campaignId }, transaction }),
				removeOccasionImageFiles(picUrls),
			]);
		});

const addOccasionImages = async (images: CreationAttributes<OccasionImage>[], transaction?: Transaction) =>
	db.occasionImages.bulkCreate(images, {
		fields: ['occasionId', 'showOrder', 'picUrl'],
		transaction,
	});

// const removeOccasionImagesByOccasionId = async (occasionId: number | number[], transaction?: Transaction) =>
// 	db.occasionImages.destroy({
// 		where: { occasionId: occasionId },
// 		transaction
// 	})

const removeOccasionImages = async (occasionImageId: number | number[], transaction?: Transaction) =>
	db.occasionImages.destroy({
		where: { occasionImageId: occasionImageId },
		transaction,
	});

const removeOccasionImageFiles = async (picUrls: string[]) =>
	Promise.all(picUrls.map(async (pU) => FileOps.deleteFile(path.join(systemConfig.PATH_FILE_UPLOAD_OCCASION, pU))));

const changeOccasionImageOrders = async (
	images: { occasionImageId: number; showOrder: number }[],
	transaction?: Transaction,
) =>
	Promise.all(
		images.map((i) =>
			db.occasionImages.update(
				{ showOrder: i.showOrder },
				{
					where: { occasionImageId: i.occasionImageId },
					transaction,
				},
			),
		),
	);

export const updateOccasionDisplay = async (params: UpdateOccasionDisplaySchema, transaction?: Transaction) => {
	const { occasionId } = params;
	const currentOccasion = await db.occasions.findOne({
		where: {
			occasionId,
		},
	});
	if (!currentOccasion) {
		throw new AppError(BAD_REQUEST, `occasionId not found ${occasionId}`, true);
	}
	await db.occasions.update(omit(params, 'occasionId'), { where: { occasionId }, transaction });
	const categoryId = currentOccasion.categoryId;
	if (categoryId) {
		const countNumber = params.isDisplayed ? 1 : -1;
		await CategoryService.incrementOrDecrementNumberOfEventDisplay(categoryId, countNumber, transaction);
	}
	return true;
};

export const getOccasionDetailsV2 = async ({
	occasionId,
	from,
	to,
	transaction,
}: {
	occasionId: number;
	isParanoid: boolean;
	from: Date | Moment;
	to: Date | Moment;
	transaction?: Transaction;
}) => {
	const occurrenceDetails = await db.occurrenceDetailModel.findAll({
		where: {
			eventDay: { [Op.between]: [from, to] },
		},
		include: [
			{
				model: db.occurrences,
				where: { occasionId: occasionId },
			},
		],
	});
	const occasion = await db.occasions.findByPk(occasionId, {
		include: [
			{
				model: db.classCategoryModel,
			},
		],
		transaction,
	});

	if (occasion == null) {
		throw new AppError(BAD_REQUEST, `occasion ${occasionId} does not exist`, false);
	}
	const occurrences = occurrenceDetails.map((it) => {
		const dataOccurrenceDetails = it?.toJSON() as OccurrenceDetail;
		return { ...dataOccurrenceDetails?.occurrence, ..._.omit(dataOccurrenceDetails, 'occurrence') };
	});

	const deadlineSteps = await db.occurrences.findAll({
		where: {
			occasionId: occasionId,
			deletedAt: { [Op.is]: null } as any,
		},
		attributes: ['responseDeadlineDate', 'interviewStep'],
	});

	const dataResponseDeadlineDate = deadlineSteps.map((e) => e.dataValues);
	
	return { ...occasion.toJSON(), occurrences, dataResponseDeadlineDate };
};

import { NextFunction, Request, Response } from 'express';
import { LineService } from '../services';
import { SESSION_ERROR, ERROR_MESSAGES, systemConfig } from '../config';
import { AppError } from '../utilities';
import { isTrue } from '~utilities/common.utils';

export const checkSession = (req: Request, res: Response, next: NextFunction) => {
	try {
		if (isTrue(req.headers['skip-authen']) && isTrue(systemConfig.SKIP_AUTHEN)) {
			next();
			return;
		}
		if (req.session.user == null) {
			throw new AppError(SESSION_ERROR, 'session does not exist', false);
		}
		next();
	} catch (e) {
		next(e);
	}
};
export const checkLineProfile = async (req: Request, res: Response, next: NextFunction) => {
	try {
		if (!req.headers['access-token']) {
			throw new AppError(SESSION_ERROR, 'access-token', false);
		}
		console.log('req.headers', req.headers['access-token']);
		const isVerified = await LineService.verifyAccessToken(req.headers['access-token'] as string);
		if (!isVerified) {
			throw new AppError(SESSION_ERROR, ERROR_MESSAGES.LINE_NOT_VERIFIED, false);
		}
		const memberLine = await LineService.getProfileByToken(req.headers['access-token'] as string);
		if (!memberLine) {
			throw new AppError(SESSION_ERROR, 'profile not found', false);
		}
		res.locals.memberLine = memberLine;
		next();
	} catch (e) {
		next(e);
	}
};

export const checkSecretKey = (req: Request, res: Response, next: NextFunction) => {
	try {
		const secretKey = req.headers['x-secret-key'];

		if (!secretKey || systemConfig.SECRET_KEY !== secretKey) {
			res.status(401).json({ message: 'Secret key is invalid' });
			return;
		}
		next();
	} catch (e) {
		next(e);
	}
};

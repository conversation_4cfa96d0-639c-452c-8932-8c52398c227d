import { Migration } from 'sequelize-cli';
import { DATABASE_TABLE_NAME } from '../config';

module.exports = {
	async up(queryInterface) {
		const transaction = await queryInterface.sequelize.transaction();

		const dataInsert = [
			{
				name: 'アクト',
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				name: 'ヴォーカル',
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				name: 'ジュニア',
				createdAt: new Date(),
				updatedAt: new Date(),
			},
			{
				name: '芸人',
				createdAt: new Date(),
				updatedAt: new Date(),
			},
		];
		try {
			await queryInterface.bulkInsert(DATABASE_TABLE_NAME.CLASS_CATEGORY, dataInsert, {
				transaction,
			});
			await transaction.commit();
		} catch (error) {
			console.log('🚀 -------------------------------------------------------------------------------------🚀');
			console.log('🚀 ~ file: 20241212060723-init-class-categories ~ up ~ error:', error);
			console.log('🚀 -------------------------------------------------------------------------------------🚀');

			await transaction.rollback();
		}
	},

	async down(queryInterface) {
		const transaction = await queryInterface.sequelize.transaction();

		try {
			await queryInterface.bulkDelete(
				DATABASE_TABLE_NAME.CLASS_CATEGORY,
				{},
				{
					transaction,
				},
			);
			await transaction.commit();
		} catch (error) {
			console.log('🚀 ---------------------------------------------------------------------------------------🚀');
			console.log('🚀 ~ file: 20241212060723-init-class-categories ~ down ~ error:', error);
			console.log('🚀 ---------------------------------------------------------------------------------------🚀');

			await transaction.rollback();
		}
	},
} satisfies Migration;

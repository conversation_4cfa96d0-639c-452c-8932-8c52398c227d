export const isTrue = (value: any) => {
	if (typeof value === 'string') {
		return value?.toLocaleLowerCase() === 'true';
	}
	return value === 1 || value === true;
};

export const reminderReplaceString = (str: string, replacements: Record<string, string>) => {
	for (const [key, value] of Object.entries(replacements)) {
		const regex = new RegExp(`\\[${key}\\]`, 'g');
		str = str.replace(regex, value);
	}
	return str;
};

const replacerName = new RegExp(/\[NAME\]/, 'gm');
const replacerDateTime = new RegExp(/\[DATE\]/, 'gm');
const replacerTelephone = new RegExp(/\[TEL\]/, 'gm');
const replacerTelephoneCompany = new RegExp(/\[COMPANY-TEL\]/, 'gm');
const replacerConfirmationUrl = new RegExp(/\[CONFIRM-URL\]/, 'gm');
const replacerBuilding = new RegExp(/\[BUILDING\]/, 'gm');
const replacerOfficeName = new RegExp(/\[事務所名\]/, 'gm');

export const TEMPLATE_REPLACERS = {
	REPLACE_NAME: replacerName,
	REPLACE_DATE_TIME: replacerDateTime,
	REPLACE_TELEPHONE: replacerTelephone,
	REPLACE_TELEPHONE_COMPANY: replacerTelephoneCompany,
	REPLACE_CONFIRMATION_URL: replacerConfirmationUrl,
	REPLACE_BUILDING: replacerBuilding,
	REPLACE_OFFICE_NAME: replacerOfficeName,
};

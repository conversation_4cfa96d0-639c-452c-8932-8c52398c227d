import { z } from 'zod';
import { INTERVIEW_STEP, MEMBER_INTERVIEW_STATUS } from '~config';
import { memberLineSchema } from '~schemas/member';
import { interviewStepSchema } from '~schemas/occurrenceDetail';

export const interviewStatusSchema = z.enum([
	MEMBER_INTERVIEW_STATUS.INTERVIEW_PROPOSAL,
	MEMBER_INTERVIEW_STATUS.SCHEDULE_ADJUSTMENT,
	MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED,
	MEMBER_INTERVIEW_STATUS.WAITING_RESULTS,
	MEMBER_INTERVIEW_STATUS.INTERVIEW_PASSED,
	MEMBER_INTERVIEW_STATUS.ASSIGNED_ACCEPTED,
	MEMBER_INTERVIEW_STATUS.ASSIGNED_CONDITIONALLY_ACCEPTED,
	MEMBER_INTERVIEW_STATUS.NOT_ACCEPTED,
	MEMBER_INTERVIEW_STATUS.DECLINED,
	MEMBER_INTERVIEW_STATUS.AUTO_DECLINED,
]);

export const addMemberToOccasionSchema = z.object({
	memberIds: z.array(z.number().min(1)).min(1),
	occasionId: z.number().min(1),
});

export const getMemberOccasionSchema = z.object({
	interviewStatus: interviewStatusSchema.optional().default(MEMBER_INTERVIEW_STATUS.SCHEDULE_ADJUSTMENT),
	memberLine: memberLineSchema,
	page: z.string().optional().default('1'),
	pageSize: z.string().optional().default('4'),
});

export const memberCancelledEventSchema = z.object({
	occurrenceId: z.number().min(1),
	memberLine: memberLineSchema,
	occasionId: z.number().min(1),
	registrationId: z.number().optional(),
});

export const adminGetMemberByInterviewStatusSchema = z.object({
	interviewStatuses: z.array(interviewStatusSchema).optional().default([]),
	page: z.string().optional().default('1'),
	pageSize: z.string().optional().default('4'),
	occasionId: z.string().min(1),
	interviewSteps: z.array(interviewStepSchema).optional(),
});

export const memberScheduleAdjustmentListSchema = z.object({
	memberLine: memberLineSchema,
	page: z.string().optional().default('1'),
	pageSize: z.string().optional().default('4'),
});

export const memberGetAllInterviewSchema = z.object({
	memberLine: memberLineSchema,
	page: z.string().optional().default('1'),
	pageSize: z.string().optional().default('4'),
});

export const adminChangeMemberStatusToScheduleAdjustmentSchema = z.object({
	occasionMemberIds: z.array(z.number()).min(1),
	occasionId: z.number().min(1),
	memberIds: z.array(z.number()).min(1),
});

export const adminUpdateOccasionMemberNotesSchema = z.object({
	occasionMemberId: z.number().min(1),
	notes: z.string().optional().nullable(),
});

export const memberFinishListSchema = z.object({
	memberLine: memberLineSchema,
	page: z.string().optional().default('1'),
	pageSize: z.string().optional().default('4'),
});

export type AddMemberToOccasionSchema = z.infer<typeof addMemberToOccasionSchema>;
export type GetMemberOccasionSchema = z.infer<typeof getMemberOccasionSchema>;
export type MemberCancelledEventSchema = z.infer<typeof memberCancelledEventSchema>;
export type InterviewStatusSchema = z.infer<typeof interviewStatusSchema>;
export type MemberScheduleAdjustmentListSchema = z.infer<typeof memberScheduleAdjustmentListSchema>;
export type MemberGetAllInterviewSchema = z.infer<typeof memberGetAllInterviewSchema>;
export type AdminChangeMemberStatusToScheduleAdjustmentSchema = z.infer<
	typeof adminChangeMemberStatusToScheduleAdjustmentSchema
>;
export type AdminGetMemberByInterviewStatusSchema = z.infer<typeof adminGetMemberByInterviewStatusSchema>;
export type AdminUpdateOccasionMemberNotesSchema = z.infer<typeof adminUpdateOccasionMemberNotesSchema>;
export type MemberFinishListSchema = z.infer<typeof memberFinishListSchema>;

import { NextFunction, Request, Response } from 'express';

import { RESPONSE_SUCCESS } from '~config';
import { createOrUpdateBufferEventTimeSettingSchema } from '~schemas/bufferEventTimeSetting';
import { BufferEventTimeSettingService } from '~services';

export const createOrUpdateBufferEventTimeSetting = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const validatedBody = createOrUpdateBufferEventTimeSettingSchema.parse(req.body);

		await BufferEventTimeSettingService.createOrUpdateNewBufferEventTimeSetting(validatedBody);
		res.sendStatus(RESPONSE_SUCCESS);
	} catch (e) {
		next(e);
	}
};

export const getBufferEventTimeSetting = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const data = await BufferEventTimeSettingService.getBufferEventTimeSetting();
		return res.send(data);
	} catch (e) {
		next(e);
	}
};

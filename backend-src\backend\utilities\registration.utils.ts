import moment from 'moment';
import { INTERVIEW_STEP } from '~config';

/**
 * Check the server's current time against the start and end time range of the event.
 * @param day (format YYYY-MM-DD)
 * @param startTime (dạng HH:mm:ss)
 * @param endTime (dạng HH:mm:ss)
 * @returns boolean.
 */
export const isValidateTimeClientRegisterForEvent = (day: string, startTime: string, endTime: string) => {
	const now = moment();

	const startDateTime = moment(`${day} ${startTime}`, 'YYYY-MM-DD HH:mm:ss');
	const endDateTime = moment(`${day} ${endTime}`, 'YYYY-MM-DD HH:mm:ss');

	return !(now.isBefore(startDateTime) || now.isAfter(endDateTime));
};

export const getNextInterviewStep = (currentStep?: INTERVIEW_STEP) => {
	if (!currentStep) return null;
	const values = Object.values(INTERVIEW_STEP);
	const currentIndex = values.indexOf(currentStep);

	if (currentIndex >= 0 && currentIndex < values.length - 1) {
		return values[currentIndex + 1] as INTERVIEW_STEP;
	}

	return null;
};

export const getBackInterviewStep = (currentStep?: INTERVIEW_STEP) => {
	if (!currentStep) return null;
	const values = Object.values(INTERVIEW_STEP);
	const currentIndex = values.indexOf(currentStep);

	if (currentIndex >= 0 && currentIndex < values.length - 1) {
		return values[currentIndex - 1] as INTERVIEW_STEP;
	}

	return null;
};

import { DATABASE_TABLE_NAME } from '../config';
import { SequelizeUtility } from '../utilities';

import type { Migration } from 'sequelize-cli';

module.exports = {
	async up(queryInterface, Sequelize) {
		const NEW_COLUMNS = [
			{
				name: 'notes',
				type: { type: Sequelize.STRING(5000), allowNull: true },
			},
		];
		for (const newColumn of NEW_COLUMNS) {
			if (
				await SequelizeUtility.columnNotExists(queryInterface, DATABASE_TABLE_NAME.OCCURRENCE_DETAIL, newColumn.name)
			) {
				await queryInterface.addColumn(DATABASE_TABLE_NAME.OCCURRENCE_DETAIL, newColumn.name, newColumn.type);
			}
		}
	},

	async down(queryInterface) {
		const NEW_COLUMNS = ['notes'];
		for (const newColumn of NEW_COLUMNS) {
			if (await SequelizeUtility.columnExists(queryInterface, DATABASE_TABLE_NAME.OCCURRENCE_DETAIL, newColumn)) {
				await queryInterface.removeColumn(DATABASE_TABLE_NAME.OCCURRENCE_DETAIL, newColumn);
			}
		}
	},
} satisfies Migration;

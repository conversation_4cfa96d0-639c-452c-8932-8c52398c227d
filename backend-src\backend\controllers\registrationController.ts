import { NextFunction, Request, Response } from 'express';
import json2csv from 'json2csv';
import _ from 'lodash';
import moment from 'moment';
import { col, HasOne, literal, Op, Sequelize, Transaction, WhereAttributeHash } from 'sequelize';
import { DATE_FORMAT_TYPE, formatDate } from '~utilities/commonDateTime';
import { adminRegistrationByInterviewStepSchema, adminUpdateInterviewStatusSchema } from '~schemas/registration';
import {
	ADMIN_LABEL_INTERVIEW_CANDIDATE_ACCEPTED_OTHER_OFFICE,
	cancelHistoryColumn,
	LABEL_INTERVIEW_STATUS_EXPORT_CSV,
	OFFICE_LABEL_CSV_DECLINED_OR_AUTO_DECLINED,
} from '~config';
import Papa from 'papaparse';

import {
	AUTH_LEVELS,
	BAD_REQUEST,
	CREATED,
	CUSTOMER_REGISTRATION_NAME,
	INTERVIEW_TYPE_NAME,
	INTERVIEWS_STEP_LABELS,
	LABEL_INTERVIEW_STATUS,
	LABEL_MEMBER_ACTIONS_STATUS,
	MEMBER_ACTIONS_STATUS,
	MEMBER_INTERVIEW_STATUS,
	MEMBER_IS_FRIEND_LABEL,
	MEMBER_ORIGIN,
	MEMBER_VIA_TYPE,
	OCCASION_DISPLAY_LABEL,
	PERMISSION_ERROR,
	RESPONSE_SUCCESS,
	STUDENT_TYPE,
	SYSTEM_ERROR,
} from '../config';
import { db } from '../models';
import { ManagerService, MemberService, RegistrationService, SocketServerService } from '../services';
import { AppError, comparePassword, generateWhereClauseBetween } from '../utilities';
import { ProjectionAlias } from 'sequelize/types/model';
import { CustomerRegistration } from '~models/customerRegistrationModel';
import { OccurrenceDetail } from '~models/occurrenceDetailModel';

export const createManualRegistration = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const {
			occurrenceId,
			lastName,
			firstName,
			lastNameKana,
			firstNameKana,
			email,
			telephone,
			postalCode,
			address,
			building,
			message,
		} = req.body as {
			occurrenceId: number;
			lastName: string;
			firstName: string;
			lastNameKana: string;
			firstNameKana: string;
			email?: string;
			telephone?: string;
			postalCode?: string;
			address?: string;
			building?: string;
			message?: string;
		};
		if (!occurrenceId || isNaN(occurrenceId)) {
			throw new AppError(SYSTEM_ERROR, 'invalid occurrence ids', false);
		}
		const member = await db.members.create({
			lastName,
			firstName,
			lastNameKana,
			firstNameKana,
			email,
			telephone,
			postalCode,
			address,
			building,
			via: MEMBER_VIA_TYPE.OTHERS,
			origin: MEMBER_ORIGIN.SYSTEM,
		});

		// MemberService.createManualMember(guardianInfo, transaction);
		const socketNotifData = await RegistrationService.memberRegisterForEvent({
			occurrenceId,
			member,
			message,
			isManual: true,
		});
		if (socketNotifData != null) {
			SocketServerService.emitRegistration({
				memberId: member.memberId,
				categoryId: socketNotifData.categoryId,
				occasionId: socketNotifData.occasionId,
				occurrenceId: occurrenceId,
			});
		}
		res.sendStatus(RESPONSE_SUCCESS);
	} catch (e) {
		console.log(e);

		next(e);
	}
};

export const createCampaignManualRegistration = async (req: Request, res: Response, next: NextFunction) => {
	let transaction: Transaction | null = null;
	try {
		const {
			occurrenceId,
			lastName,
			firstName,
			lastNameKana,
			firstNameKana,
			email,
			telephone,
			postalCode,
			address,
			building,
			message,
		} = req.body as {
			occurrenceId: number;
			lastName: string;
			firstName: string;
			lastNameKana: string;
			firstNameKana: string;
			email?: string;
			telephone?: string;
			postalCode?: string;
			address?: string;
			building?: string;
			message?: string;
		};
		if (!occurrenceId || isNaN(occurrenceId)) {
			throw new AppError(SYSTEM_ERROR, 'invalid occurrence ids', false);
		}
		transaction = await db.sequelize.transaction();

		const member = await db.members.create(
			{
				lastName,
				firstName,
				lastNameKana,
				firstNameKana,
				email,
				telephone,
				postalCode,
				address,
				building,
				via: 'others',
			},
			{ transaction },
		);

		const socketNotifData = await RegistrationService.memberRegisterForCampaignEvent(
			{ occurrenceId, member, message, isManual: true },
			transaction,
		);
		await transaction.commit();
		if (socketNotifData != null) {
			SocketServerService.emitRegistration({
				memberId: member.memberId,
				campaignId: socketNotifData.campaignId,
				occasionId: socketNotifData.occasionId,
				occurrenceId: occurrenceId,
			});
		}
		res.sendStatus(RESPONSE_SUCCESS);
	} catch (e) {
		console.log(e);

		if (transaction != null) {
			await transaction.rollback();
		}
		next(e);
	}
};

export const getRegistration = async (req: Request, res: Response, next: NextFunction) => {
	const registrationId = parseInt(req.params.registrationId);
	try {
		if (!registrationId || isNaN(registrationId)) {
			throw new AppError(SYSTEM_ERROR, 'invalid parameters', false);
		}
		await RegistrationService.getRegistration(registrationId).then((registration) => {
			if (registration == null) {
				throw new AppError(SYSTEM_ERROR, `registration ${registrationId} does not exist`, false);
			}
			res.send(registration);
		});
	} catch (e) {
		next(e);
	}
};

export const getCampaignRegistrations = async (req: Request, res: Response, next: NextFunction) => {
	const campaignId = parseInt(req.params.campaignId);

	try {
		if (!campaignId || isNaN(campaignId)) {
			throw new AppError(SYSTEM_ERROR, 'invalid parameters', false);
		}
		await RegistrationService.getCampaignRegistrations(campaignId).then((registration) => {
			if (registration == null) {
				throw new AppError(SYSTEM_ERROR, `campaignId ${campaignId} does not exist`, false);
			}
			res.send(registration);
		});
	} catch (e) {
		console.log(e);

		next(e);
	}
};

export const getAttended = async (req: Request, res: Response, next: NextFunction) => {
	const memberId = parseInt(req.params.memberId);
	try {
		if (!memberId || isNaN(memberId)) {
			throw new AppError(SYSTEM_ERROR, 'invalid parameters', false);
		}
		const dataRegistrations = await RegistrationService.getAttended(memberId);
		res.send(dataRegistrations);
	} catch (e) {
		next(e);
	}
};

export const getCampaignsAttended = async (req: Request, res: Response, next: NextFunction) => {
	const memberId = parseInt(req.params.memberId);
	try {
		if (!memberId || isNaN(memberId)) {
			throw new AppError(SYSTEM_ERROR, 'invalid parameters', false);
		}
		const dataRegistrations = await RegistrationService.getCampaignsAttended(memberId);
		res.send(dataRegistrations);
	} catch (e) {
		next(e);
	}
};

export const updateRegistration = async (req: Request, res: Response, next: NextFunction) => {
	let transaction: Transaction | null = null;
	const registrationId = parseInt(req.body.registrationId);
	try {
		if (!registrationId || isNaN(registrationId)) {
			throw new AppError(SYSTEM_ERROR, 'invalid parameters', false);
		}
		transaction = await db.sequelize.transaction();
		const socketData = await RegistrationService.editRegistration(req.body, transaction);
		await transaction.commit().then(() => {
			SocketServerService.emitRegistration(socketData);
			res.sendStatus(RESPONSE_SUCCESS);
		});
	} catch (e) {
		if (transaction != null) {
			await transaction.rollback();
		}
		next(e);
	}
};

export const updateRegistrationAttended = async (req: Request, res: Response, next: NextFunction) => {
	const transaction = await db.sequelize.transaction();

	const registrationId = parseInt(req.body.registrationId);
	try {
		if (!registrationId || isNaN(registrationId)) {
			throw new AppError(SYSTEM_ERROR, 'invalid parameters', false);
		}

		const registration = await db.registrations.findOne({
			where: { registrationId: registrationId },
			include: [
				{
					association: db.registrations.associations.Occurrence,
					attributes: ['categoryId', 'occasionId', 'occurrenceId'],
				},
			],
		});
		if (registration == null) {
			throw new AppError(SYSTEM_ERROR, `registration ${registrationId} does not exist`, false);
		}
		if (registration?.attended) {
			throw new AppError(SYSTEM_ERROR, 'attended', false);
		}

		await db.registrations.update({ attended: 1 }, { where: { registrationId: registrationId }, transaction });

		await transaction.commit();

		res.sendStatus(RESPONSE_SUCCESS);
	} catch (e) {
		await transaction.rollback();

		next(e);
	}
};

export const cancelRegistration = async (req: Request, res: Response, next: NextFunction) => {
	let transaction;
	try {
		const registrationId = parseInt(req.params.registrationId);
		if (!registrationId || isNaN(registrationId)) {
			throw new AppError(SYSTEM_ERROR, 'invalid registrationId');
		}

		transaction = await db.sequelize.transaction();
		try {
			// await RegistrationService.cancelRegistration(registrationId, undefined, transaction);
			// await ReminderService.destroyReminderByRegistrationId(registrationId, transaction);
			// await transaction.commit();
			// if (socketData.occurrenceId != null) {
			// 	await redisCacheService.decreaseOccurrenceRegisterCount(socketData.occurrenceId, socketData.expected);
			// }
			// SocketServerService.emitRegistration({
			// 	memberId: socketData.memberId,
			// 	categoryId: socketData.categoryId,
			// 	occasionId: socketData.occasionId,
			// 	occurrenceId: socketData.occurrenceId,
			// });
			return res.sendStatus(RESPONSE_SUCCESS);
		} catch (error) {
			console.log(error);
		}
	} catch (e) {
		if (transaction != null) {
			await transaction.rollback();
		}
		next(e);
	}
};

export const deleteRegistration = async (req: Request, res: Response, next: NextFunction) => {
	let transaction: Transaction | null = null;
	try {
		const registrationId = parseInt(req.params.registrationId);
		if (!registrationId || isNaN(registrationId)) {
			throw new AppError(SYSTEM_ERROR, 'invalid registrationId');
		}
		transaction = await db.sequelize.transaction();
		const socketData = await RegistrationService.deleteRegistration(registrationId, transaction);
		await transaction.commit().then(() => {
			SocketServerService.emitRegistration(socketData);
			res.sendStatus(RESPONSE_SUCCESS);
		});
	} catch (e) {
		if (transaction != null) {
			await transaction.rollback();
		}
		next(e);
	}
};

async function getRegistrationCsvData({
	registrationWhere,
	occasionWhere,
	categoryWhere,
	occurrenceWhere,
}: {
	registrationWhere: WhereAttributeHash;
	occasionWhere: WhereAttributeHash;
	categoryWhere: WhereAttributeHash;
	occurrenceWhere: WhereAttributeHash;
}) {
	const customerRegistrations = await db.customerRegistrations.findAll({
		where: {
			isAdminDisplayed: true,
			studentType: STUDENT_TYPE.ENROLLED_STUDENT,
		},
		attributes: ['customerRegistrationId', 'type', 'label', 'name'],
		order: [['showOrder', 'asc']],
		raw: true,
	});
	const classes = await db.memberClassModel.findAll({
		attributes: ['memberClassId', 'className'],
	});
	const classMap: Record<string, string> = classes.reduce(
		(acc, c) => ({
			...acc,
			[c.memberClassId]: c.className,
		}),
		{},
	);
	const categoryCSVData: any[] = await RegistrationService.generateRegistrationDataForCSV({
		registrationWhere,
		occasionWhere,
		categoryWhere,
		customerRegistrations,
		occurrenceWhere,
	});
	const csvData = categoryCSVData.map((member) => {
		const isRegisterManually = member?.lineId;
		return MemberService.transformUserCsvData(
			member,
			customerRegistrations,
			{
				会員ID: `${member.memberId ?? ''}`,
				会員コード: member.memberCode ?? '',
				LINE名: member.displayName ?? '',
				LINEフォロー状態: `${member.isFriends ? MEMBER_IS_FRIEND_LABEL.IS_FRIEND : MEMBER_IS_FRIEND_LABEL.NOT_FRIEND}`,
				面接日時: `${member.startAt ? moment(member.startAt).format('YYYY年MM月DD日HH時mm分') : ''}`,
				備考欄: isRegisterManually ? (_.get(member, 'notes', '') as string) : '',
				友だち登録日: isRegisterManually ? formatDate(member?.createdAt) : '',
				会員登録日: isRegisterManually ? formatDate(member?.memberSince) : '',
				親イベント名: member.categoryTitle,
				子イベント名: member.occasionTitle,
			},
			classMap,
		);
	});
	const fields = [
		'会員ID',
		'会員コード',
		'LINE名',
		'LINEフォロー状態',
		'親イベント名',
		'子イベント名',
		'面接日時',
		..._.map(customerRegistrations, (cR) => cR?.label),
		'備考欄',
		'友だち登録日',
		'会員登録日',
	];
	const opts = { fields: fields, withBOM: true, excelStrings: true };
	const csv = json2csv.parse(csvData, opts);
	return csv;
}

export const generateCategoryRegistrationCSV = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const managerId = req.session.user?.id;
		const { categoryId, occasionId, from, to, password } = req.body;
		if (!managerId) {
			throw new AppError(PERMISSION_ERROR, 'no session', false);
		}
		if (!password || (!from && !to)) {
			throw new AppError(SYSTEM_ERROR, 'invalid parameters', false);
		}
		const manager = await ManagerService.getManager(managerId);
		if (manager == null) {
			throw new AppError(SYSTEM_ERROR, `manager ${managerId} does not exist`, false);
		}
		const isMatch = await comparePassword(password, manager.pwhash);
		if (!isMatch) {
			throw new AppError(PERMISSION_ERROR, 'invalid password', false);
		}
		const categoryWhere: WhereAttributeHash = {};
		if (categoryId) {
			categoryWhere.categoryId = categoryId;
		}
		const occasionWhere: WhereAttributeHash = {};
		if (occasionId) {
			occasionWhere.occasionId = occasionId;
		}
		const registrationWhere: WhereAttributeHash = {
			memberId: { [Op.not]: null },
			createdAt: { [Op.not]: null },
		};
		const occurrenceWhere: WhereAttributeHash = {
			...generateWhereClauseBetween('eventDay', [from, to]),
		};
		const csvData = await getRegistrationCsvData({ registrationWhere, occasionWhere, categoryWhere, occurrenceWhere });
		res.setHeader('Content-Type', 'text/csv');
		res.setHeader('Content-Disposition', 'attachment; filename=registrations.csv');
		res.status(RESPONSE_SUCCESS).end(csvData);
	} catch (e) {
		next(e);
	}
};

export const getConfirmRegistration = async (req: Request, res: Response, next: NextFunction) => {
	const registrationId = parseInt(req.body.registrationId);

	try {
		if (!registrationId || isNaN(registrationId)) {
			throw new AppError(BAD_REQUEST, 'QR error', false);
		}
		const registration = await db.registrations.findOne({
			where: { registrationId },
			attributes: [
				//
				'message',
				'attended',
				'registrationId',
				'note',
				'isManual',
				'participantName',
				'participantCount',
				'companionCount',
				'actualParticipantCount',
				'actualCompanionCount',
				'cancelledAt',
			],
			include: [
				{
					association: db.registrations.associations.Member,
					attributes: [
						'lastName',
						'firstName',
						'lastNameKana',
						'firstNameKana',
						'telephone',
						'customerRegistrationId1',
						'customerRegistrationId2',
						'displayName',
					],
				},
				{
					association: db.registrations.associations.Occurrence,
					attributes: ['startDate', 'startAt', 'endAt'],
				},
				{
					association: db.registrations.associations.Category,
					attributes: ['title', 'groupBooking'],
					include: [
						{
							association: db.categories.associations.categoryImages,
							attributes: { exclude: ['categoryImageId', 'categoryId', 'campaignId'] },
							order: [[col('showOrder'), 'asc']],
						},
					],
				},
				{
					association: db.registrations.associations.Occasion,
					attributes: ['title', 'groupBooking'],
					include: [
						{
							association: db.occasions.associations.occasionImages,
							attributes: { exclude: ['occasionImageId', 'occasionId'] },
							order: [[col('showOrder'), 'asc']],
						},
					],
				},
			],
			paranoid: false,
		});
		if (!registration || registration?.cancelledAt) {
			throw new AppError(BAD_REQUEST, `registration ${registrationId} does not exist`, false);
		}
		res.send(registration);
	} catch (e) {
		next(e);
	}
};

export const updateIsWin = async (req: Request, res: Response, next: NextFunction) => {
	const registrationId = parseInt(req.body.registrationId);

	try {
		if (!registrationId || isNaN(registrationId)) {
			throw new AppError(BAD_REQUEST, 'registrationId error', false);
		}
		const registration = await db.registrations.update(
			{ isWin: false },
			{
				where: {
					registrationId,
				},
			},
		);

		res.send(registration);
	} catch (e) {
		next(e);
	}
};

export const confirmRegistration = async (req: Request, res: Response, next: NextFunction) => {
	const registrationId = req.params.registrationId as unknown as number;
	const { actualCompanionCount, actualParticipantCount } = req.body;

	try {
		await RegistrationService.confirmationRegistration({
			registrationId,
			actualCompanionCount,
			actualParticipantCount,
		});
		res.status(CREATED).json({
			message: 'Registration confirmed',
		});
	} catch (error) {
		next(error);
	}
};

export const updateCountRegistration = async (req: Request, res: Response, next: NextFunction) => {
	const registrationId = req.params.registrationId as unknown as number;
	const { participantCount, companionCount } = req.body;

	try {
		await RegistrationService.updateCountRegistration({
			registrationId,
			participantCount,
			companionCount,
		});
		res.status(RESPONSE_SUCCESS).json({
			message: 'Registration updated',
		});
	} catch (error) {
		next(error);
	}
};

export const adminRegistrationByInterviewStep = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const validateData = adminRegistrationByInterviewStepSchema.parse(req.query);
		const data = await RegistrationService.adminRegistrationByInterviewStep(validateData);
		return res.send(data);
	} catch (e) {
		next(e);
	}
};

export const adminUpdateInterviewStatus = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const validateData = adminUpdateInterviewStatusSchema.parse(req.body);
		const data = await RegistrationService.adminUpdateInterviewStatus(validateData);
		SocketServerService.emitOccasionMemberChangeStatus();
		res.send(data);
	} catch (e) {
		next(e);
	}
};

export const cronJobChangeInterviewToWaitingResult = async (req: Request, res: Response, next: NextFunction) => {
	try {
		await RegistrationService.cronJobChangeInterviewToWaitingResult(true);
		return res.sendStatus(RESPONSE_SUCCESS);
	} catch (e) {
		next(e);
	}
};

const createFieldsWithUniqueKeys = (staticFields: string[], customerRegistrations: CustomerRegistration[]) => {
	const labelCountMap: Record<string, number> = {};
	const allFields: string[] = [];

	staticFields.forEach((field) => {
		if (labelCountMap[field] !== undefined) {
			labelCountMap[field]++;
			allFields.push(`${field}_${labelCountMap[field]}`);
		} else {
			labelCountMap[field] = 0;
			allFields.push(field);
		}
	});

	customerRegistrations.forEach((cR) => {
		const originalLabel = cR?.label;
		if (labelCountMap[originalLabel] !== undefined) {
			labelCountMap[originalLabel]++;
			allFields.push(`${originalLabel}_${labelCountMap[originalLabel]}`);
		} else {
			labelCountMap[originalLabel] = 0;
			allFields.push(originalLabel);
		}
	});

	return allFields;
};

const createHeadersForCSV = (customerRegistrations: CustomerRegistration[]) => {
	return customerRegistrations.map((cR) => cR?.label);
};

async function getRegistrationCsvDataV2({
	registrationWhere,
	occasionWhere,
	categoryWhere,
	occurrenceWhere,
	authLevel,
}: {
	registrationWhere: WhereAttributeHash;
	occasionWhere: WhereAttributeHash;
	categoryWhere: WhereAttributeHash;
	occurrenceWhere: WhereAttributeHash;
	authLevel: AuthValue;
}) {
	const customerRegistrationWhere: WhereAttributeHash = {
		isAdminDisplayed: true,
		studentType: STUDENT_TYPE.ENROLLED_STUDENT,
		[Op.or]: [{ name: { [Op.notIn]: [CUSTOMER_REGISTRATION_NAME.VOCAL_DEMO] } }, { name: { [Op.is]: null } }],
	};
	const customerRegistrations: CustomerRegistration[] = await db.customerRegistrations.findAll({
		where: customerRegistrationWhere,
		attributes: ['customerRegistrationId', 'type', 'label', 'name'],
		order: [['showOrder', 'asc']],
		raw: true,
	});
	const classes = await db.memberClassModel.findAll({
		attributes: ['memberClassId', 'className'],
	});
	const classMap = classes.reduce(
		(acc, c) => ({
			...acc,
			[c.memberClassId]: c.className,
		}),
		{},
	) as unknown as Record<string, string>;
	// const categoryCSVData: any[] = await RegistrationService.generateRegistrationDataForCSVV2({
	// 	registrationWhere,
	// 	occasionWhere,
	// 	categoryWhere,
	// 	customerRegistrations,
	// 	occurrenceWhere,
	// });
	const registrationConditions = {
		...registrationWhere,
		cancelledAt: null,
		deletedAt: null,
		memberActionStatus: MEMBER_ACTIONS_STATUS.MEMBER_ACCEPTED,
	} as WhereAttributeHash;
	const allRegistrationsMemberAccepted = await db.registrations.findAll({
		where: registrationConditions,
		include: [
			{
				model: OccurrenceDetail,
				attributes: ['eventDay', 'startTime', 'endTime', 'interviewType', 'interviewLocation'],
			},
			{
				model: db.occurrences,
				attributes: ['occurrenceId', 'responseDeadlineDate'],
				required: true,
			},
		],
		raw: true,
		nest: true,
	});
	const occasionMembers: any[] = await db.occasionMembers.findAll({
		distinct: true,
		attributes: [
			[col('Member.memberCode'), 'memberCode'],
			[col('Member.memberId'), 'memberId'],
			[col('Member.displayName'), 'displayName'],
			[col('Member.firstName'), 'firstName'],
			[col('Member.lastName'), 'lastName'],
			[col('Member.firstNameKana'), 'firstNameKana'],
			[col('Member.lastNameKana'), 'lastNameKana'],
			[col('Member.email'), 'email'],
			[col('Member.telephone'), 'telephone'],
			[col('Member.postalCode'), 'postalCode'],
			[col('Member.building'), 'building'],
			[col('Member.address'), 'address'],
			[col('Member.memberSince'), 'memberSince'],
			[col('Member.isCampaign'), 'isCampaign'],
			[col('Member.candidateAt'), 'candidateAt'],
			[col('Member.isFriends'), 'isFriends'],
			[col('`Occasion->Category`.`title`'), 'categoryTitle'],
			[col('`Occasion.isDisplayed`'), 'isDisplayedOccasion'],
			[col('Occasion.occasionId'), 'occasionId'],
			[col('Occasion.title'), 'occasionTitle'],
			[col('OccasionMember.interviewStatus'), 'interviewStatus'],
			[col('OccasionMember.notes'), 'occasionMemberNotes'],
			[col('memberInterviewCurrentStep'), 'interviewStep'],
			[col('Member.notes'), 'notes'],
			[col('Member.countVisit'), 'countVisit'],
			[col('Member.lastVisit'), 'lastVisit'],
			[col('Member.currentPoints'), 'currentPoints'],
			[col('Member.createdAt'), 'createdAt'],
			[col('Member.activeUntil'), 'activeUntil'],
			[col('Member.lineId'), 'lineId'],
			[col('`Occasion->classCategory`.`name`'), 'classCategoryName'],
			[col('`Occasion->occurrences.responseDeadlineDate`'), 'responseDeadlineDate'],
			[col('Registration.message'), 'message'],
			[
				Sequelize.literal(
					// eslint-disable-next-line quotes
					"CONCAT(`Registration->occurrenceDetail`.`eventDay`, ' ', `Registration->occurrenceDetail`.`startTime`)",
				),
				'startAt',
			],
			[col('Registration->occurrenceDetail.interviewType'), 'interviewType'],
			[col('Registration->occurrenceDetail.interviewLocation'), 'interviewLocation'],
			[col('Registration->occurrenceDetail.notes'), 'occurrenceDetailNotes'],
			[col('OccasionMember.memberInterviewCurrentStep'), 'interviewStep'],
			[col('Registration.cancelledAt'), 'cancelledAt'],
			[col('Registration.memberActionStatus'), 'memberActionStatus'],
			[col('Registration.responseDeadlineDatePassInterview'), 'responseDeadlineDatePassInterview'],
			...customerRegistrations.map(
				({ customerRegistrationId }) =>
					[
						col(`Member.customerRegistrationId${customerRegistrationId}`),
						`customerRegistrationId${customerRegistrationId}`,
					] as ProjectionAlias,
			),
		],
		include: [
			{
				model: db.members,
				required: true,
				attributes: [],
			},
			{
				model: db.occasions,
				required: true,
				include: [
					{
						model: db.categories,
						required: true,
						attributes: [],
						include: [
							{
								model: db.managers,
							},
						],
						where: categoryWhere,
					},
					{
						model: db.classCategoryModel,
						attributes: [],
					},
					{
						model: db.occurrences,
						where: {
							deletedAt: null,
							interviewStep: { [Op.col]: 'OccasionMember.memberInterviewCurrentStep' },
						},
						required: false,
					},
				],
				where: occasionWhere,
				attributes: [],
			},
			{
				association: new HasOne(db.occasionMembers, db.registrations, {
					foreignKey: 'occasionId',
				}),
				on: literal(
					'`Registration`.`occasionId` = `OccasionMember`.`occasionId` AND `Registration`.`attended` = 0 AND `Registration`.`memberId` = `OccasionMember`.`memberId`',
				),
				include: [
					{
						association: db.registrations.associations.occurrenceDetail,
						required: true,
						attributes: [],
						where: {},
					},
				],
				attributes: [],
				required: false,
				where: Sequelize.literal(
					'`Registration`.`registrationId` = (SELECT r2.`registrationId` FROM `registrations` r2 INNER JOIN `occurrences` occ ON r2.`occurrenceId` = occ.`occurrenceId` WHERE r2.`memberId` = `OccasionMember`.`memberId` AND r2.`occasionId` = `OccasionMember`.`occasionId` AND r2.`attended` = 0 AND `OccasionMember`.`memberInterviewCurrentStep` = occ.`interviewStep` ORDER BY r2.`registrationId` DESC LIMIT 1)',
				),
			},
		],
		raw: true,
		nest: true,
	} as any);
	const dataExports = occasionMembers;
	const enrollNumber = customerRegistrations.find((it) => it.name === CUSTOMER_REGISTRATION_NAME.ROLL_NUMBER);

	const csvData = dataExports.map((member) => {
		const { emailContact } = member.Occasion.Category.Manager;
		const interviewStep = (member.interviewStep ||
			member.memberInterviewCurrentStep) as keyof typeof INTERVIEWS_STEP_LABELS;
		const interviewStatus = member.interviewStatus as keyof typeof LABEL_INTERVIEW_STATUS;
		const memberActionStatus = member.memberActionStatus as keyof typeof LABEL_MEMBER_ACTIONS_STATUS;
		const interviewType = member?.interviewType as keyof typeof INTERVIEW_TYPE_NAME;
		const interviewLocation = member?.interviewLocation || '';

		let step = interviewStep ? INTERVIEWS_STEP_LABELS[interviewStep] : '';

		if (interviewStatus === MEMBER_INTERVIEW_STATUS.INTERVIEW_PROPOSAL) {
			step = '面接候補';
		}
		let responseDeadlineDate = member?.responseDeadlineDatePassInterview || member?.responseDeadlineDate;
		if (interviewStatus === MEMBER_INTERVIEW_STATUS.INTERVIEW_PROPOSAL) {
			responseDeadlineDate = 'ー';
		}
		let currentStatus = interviewStatus ? LABEL_INTERVIEW_STATUS_EXPORT_CSV[interviewStatus] : '';
		if (
			[MEMBER_INTERVIEW_STATUS.DECLINED, MEMBER_INTERVIEW_STATUS.AUTO_DECLINED].includes(interviewStatus) &&
			authLevel === AUTH_LEVELS.manager &&
			currentStatus
		) {
			currentStatus = OFFICE_LABEL_CSV_DECLINED_OR_AUTO_DECLINED;
		}
		if (
			[MEMBER_INTERVIEW_STATUS.SCHEDULE_ADJUSTMENT, MEMBER_INTERVIEW_STATUS.INTERVIEW_PASSED].includes(interviewStatus)
		) {
			currentStatus = `${LABEL_INTERVIEW_STATUS_EXPORT_CSV[interviewStatus]}（回答期限：${formatDate(
				responseDeadlineDate,
				DATE_FORMAT_TYPE.DATE_MONTH_DAY,
			)}）`;
		}
		if (interviewStatus === MEMBER_INTERVIEW_STATUS.INTERVIEW_PROPOSAL) {
			const isAcceptedOffice = allRegistrationsMemberAccepted.find((reg) => reg.memberId === member.memberId);
			if (isAcceptedOffice) {
				currentStatus = ADMIN_LABEL_INTERVIEW_CANDIDATE_ACCEPTED_OTHER_OFFICE;
			}
		}
		if (
			memberActionStatus &&
			[MEMBER_INTERVIEW_STATUS.ASSIGNED_ACCEPTED, MEMBER_INTERVIEW_STATUS.ASSIGNED_CONDITIONALLY_ACCEPTED].includes(
				interviewStatus,
			) &&
			(authLevel === AUTH_LEVELS.master || memberActionStatus === MEMBER_ACTIONS_STATUS.MEMBER_NOT_ACCEPTED)
		) {
			currentStatus = `${currentStatus}(${LABEL_MEMBER_ACTIONS_STATUS[memberActionStatus]})`;
		}
		const isDisplayedOccasion = member.isDisplayedOccasion as keyof typeof OCCASION_DISPLAY_LABEL;
		const registrationCancelledAt = formatDate(member.cancelledAt);
		let cancelledHistory = member.cancelledAt ? `予約キャンセル日（${registrationCancelledAt}）` : 'ー';
		if (MEMBER_INTERVIEW_STATUS.SCHEDULE_ADJUSTMENT !== interviewStatus) {
			cancelledHistory = 'ー';
		}
		const result = MemberService.transformUserCsvData(
			member,
			customerRegistrations,
			{
				会員ID: member.memberId,
				会員コード: member.memberCode ?? '',
				LINE名: member.displayName ?? '',
				選考名: member.occasionTitle,
				進行状況: OCCASION_DISPLAY_LABEL[isDisplayedOccasion],
				事務所名: member.categoryTitle,
				通知送付先メールアドレス: emailContact,
				面接日時: `${
					member.startAt && !member.cancelledAt ? moment(member.startAt).format('YYYY年MM月DD日HH時mm分') : ''
				}`,
				面接ステップ: step,
				面接ステータス: currentStatus,
				面接形式: interviewType ? INTERVIEW_TYPE_NAME[interviewType as keyof typeof INTERVIEW_TYPE_NAME] : '',
				面接会場の情報: interviewLocation,
				事前準備物: member?.occurrenceDetailNotes,
				カテゴリー: member.classCategoryName,
				[cancelHistoryColumn]: cancelledHistory,
			},
			classMap,
		) as any;
		const labelEnrollNumber = `${enrollNumber?.label}`;
		result[labelEnrollNumber] = result[`${enrollNumber?.label}`] ? parseInt(result[`${enrollNumber?.label}`]) : '';
		return result;
	});

	const fullName = customerRegistrations.find(
		(it) => it.name === CUSTOMER_REGISTRATION_NAME.FULL_NAME_ENROLLED_STUDENT,
	);
	const classCustomerRegistration = customerRegistrations.find((it) => it.name === CUSTOMER_REGISTRATION_NAME.CLASS);
	const participationYear = customerRegistrations.find(
		(it) => it.name === CUSTOMER_REGISTRATION_NAME.PARTICIPATION_YEAR,
	);
	const imageProfile = customerRegistrations.find((it) => it.name === CUSTOMER_REGISTRATION_NAME.IMAGE_PROFILE);
	const customerRegistrationIdsFixed = [
		enrollNumber?.customerRegistrationId,
		fullName?.customerRegistrationId,
		classCustomerRegistration?.customerRegistrationId,
		participationYear?.customerRegistrationId,
		imageProfile?.customerRegistrationId,
	];
	const customerRegistrationRemain = customerRegistrations.filter(
		(it) => !customerRegistrationIdsFixed.includes(it.customerRegistrationId),
	);

	const staticFields = [
		'会員ID',
		'会員コード',
		'LINE名',
		'選考名',
		'進行状況',
		'事務所名',
		'通知送付先メールアドレス',
		'面接日時',
		'面接ステップ',
		'面接ステータス',
		'面接形式',
		'面接会場の情報',
		'事前準備物',
		`${fullName?.label}`,
		`${enrollNumber?.label}`,
		`${participationYear?.label}`,
		'カテゴリー',
		`${classCustomerRegistration?.label}`,
	];

	const fields = createFieldsWithUniqueKeys(staticFields, customerRegistrationRemain);

	if (authLevel === AUTH_LEVELS.master) {
		fields.push(cancelHistoryColumn);
	}

	const csvHeaders = [
		'会員ID',
		'会員コード',
		'LINE名',
		'選考名',
		'進行状況',
		'事務所名',
		'通知送付先メールアドレス',
		'面接日時',
		'面接ステップ',
		'面接ステータス',
		'面接形式',
		'面接会場の情報',
		'事前準備物',
		`${fullName?.label}`,
		`${enrollNumber?.label}`,
		`${participationYear?.label}`,
		'カテゴリー',
		`${classCustomerRegistration?.label}`,
		...createHeadersForCSV(customerRegistrationRemain),
	];

	if (authLevel === AUTH_LEVELS.master) {
		csvHeaders.push(cancelHistoryColumn);
	}

	const csv = Papa.unparse(
		{
			fields: csvHeaders,
			data: csvData.map((row) => fields.map((field) => row[field] ?? '')),
		},
		{
			quotes: true,
			delimiter: ',',
			header: true,
			skipEmptyLines: false,
		},
	);

	return '\uFEFF' + csv;
}

export const generateCategoryRegistrationCSVV2 = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const managerId = req.session.user?.id;
		const { categoryId, occasionId, from, to, password } = req.body;
		if (!managerId) {
			throw new AppError(PERMISSION_ERROR, 'no session', false);
		}
		if (!password) {
			throw new AppError(BAD_REQUEST, 'password not found', false);
		}
		const manager = await ManagerService.getManager(managerId);
		if (manager == null) {
			throw new AppError(SYSTEM_ERROR, `manager ${managerId} does not exist`, false);
		}
		const isMatch = await comparePassword(password, manager.pwhash);
		if (!isMatch) {
			throw new AppError(PERMISSION_ERROR, 'invalid password', false);
		}
		const { authLevel } = manager;
		const categoryWhere: WhereAttributeHash = {};
		if (categoryId) {
			categoryWhere.categoryId = categoryId;
		}
		const occasionWhere: WhereAttributeHash = {};
		if (occasionId) {
			occasionWhere.occasionId = occasionId;
		}
		const registrationWhere: WhereAttributeHash = {
			memberId: { [Op.not]: null },
			createdAt: { [Op.not]: null },
		};
		const occurrenceWhere: WhereAttributeHash = {
			...generateWhereClauseBetween('eventDay', [from, to]),
		};
		const csvData = await getRegistrationCsvDataV2({
			registrationWhere,
			occasionWhere,
			categoryWhere,
			occurrenceWhere,
			authLevel: manager.authLevel as unknown as AuthValue,
		});
		res.setHeader('Content-Type', 'text/csv');
		res.setHeader('Content-Disposition', 'attachment; filename=registrations.csv');
		res.status(RESPONSE_SUCCESS).end(csvData);
	} catch (e) {
		next(e);
	}
};

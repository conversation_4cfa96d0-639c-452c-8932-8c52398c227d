import { QueryInterface, DataTypes, literal } from 'sequelize';
import { tableExists, tableNotExists } from '~utilities';
import { DATABASE_TABLE_NAME } from '~config';

module.exports = {
	up: async (queryInterface: QueryInterface) => {
		if (await tableNotExists(queryInterface, DATABASE_TABLE_NAME.INTERVIEW_SETTING_TIMES)) {
			await queryInterface.createTable(DATABASE_TABLE_NAME.INTERVIEW_SETTING_TIMES, {
				interviewSettingTimeid: {
					type: DataTypes.INTEGER,
					primaryKey: true,
					autoIncrement: true,
				},
				interviewSettingId: {
					type: DataTypes.INTEGER,
					references: {
						model: DATABASE_TABLE_NAME.INTERVIEW_SETTINGS,
						key: 'interviewSettingId',
					},
					allowNull: true,
					onDelete: 'CASCADE',
				},
				date: {
					type: DataTypes.DATEONLY,
					allowNull: false,
				},
				startTime: {
					type: DataTypes.TIME,
					allowNull: false,
				},
				endTime: {
					type: DataTypes.TIME,
					allowNull: false,
				},
				classCategoryId: {
					type: DataTypes.INTEGER({ unsigned: true }),
					allowNull: false,
					references: {
						model: DATABASE_TABLE_NAME.CLASS_CATEGORY,
						key: 'classCategoryId',
					},
					onDelete: 'CASCADE',
				},
				isBlockedCategory: {
					type: DataTypes.BOOLEAN,
					allowNull: false,
					defaultValue: false,
				},
				createdAt: {
					allowNull: false,
					type: DataTypes.DATE,
					defaultValue: literal('CURRENT_TIMESTAMP'),
				},
				updatedAt: {
					allowNull: false,
					type: DataTypes.DATE,
					defaultValue: literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
				},
			});
		}
	},

	down: async (queryInterface: QueryInterface) => {
		if (await tableExists(queryInterface, DATABASE_TABLE_NAME.INTERVIEW_SETTING_TIMES)) {
			await queryInterface.dropTable(DATABASE_TABLE_NAME.INTERVIEW_SETTING_TIMES);
		}
	},
};

import { QueryInterface, DataTypes } from 'sequelize';
import { tableExists, tableNotExists } from '~utilities';
import { DATABASE_TABLE_NAME } from '~config';

module.exports = {
	up: async (queryInterface: QueryInterface) => {
		if (await tableNotExists(queryInterface, DATABASE_TABLE_NAME.INTERVIEW_SETTING_TIMES)) {
			await queryInterface.createTable(DATABASE_TABLE_NAME.INTERVIEW_SETTING_TIMES, {
				interviewSettingTimeid: {
					type: DataTypes.INTEGER,
					primaryKey: true,
					autoIncrement: true,
				},
				interview_setting_id: {
					type: DataTypes.INTEGER,
					references: {
						model: DATABASE_TABLE_NAME.INTERVIEW_SETTING_TIMES,
						key: 'interviewSettingId',
					},
					allowNull: true,
				},
				date: {
					type: DataTypes.DATEONLY,
					allowNull: false,
				},
				start_time: {
					type: DataTypes.TIME,
					allowNull: false,
				},
				end_time: {
					type: DataTypes.TIME,
					allowNull: false,
				},
				classCategoryId: {
					type: DataTypes.INTEGER,
					references: {
						model: DATABASE_TABLE_NAME.CLASS_CATEGORY,
						key: 'classCategoryId',
					},
					allowNull: false,
				},
				is_blocked_category: {
					type: DataTypes.BOOLEAN,
					allowNull: false,
					defaultValue: false,
				},
				createdAt: {
					type: DataTypes.DATE,
					allowNull: false,
					defaultValue: new Date(),
				},
				updatedAt: {
					type: DataTypes.DATE,
					allowNull: false,
					defaultValue: new Date(),
				},
			});

			await queryInterface.bulkInsert(DATABASE_TABLE_NAME.INTERVIEW_SETTING_TIMES, [
				{
					isEnabled: false,
					imageFilename: null,
					createdAt: new Date(),
					updatedAt: new Date(),
				},
			]);
		}
	},

	down: async (queryInterface: QueryInterface) => {
		if (await tableExists(queryInterface, DATABASE_TABLE_NAME.INTERVIEW_SETTING_TIMES)) {
			await queryInterface.dropTable(DATABASE_TABLE_NAME.INTERVIEW_SETTING_TIMES);
		}
	},
};

import { DATABASE_TABLE_NAME } from '../config';
import { SequelizeUtility } from '../utilities';

import type { Migration } from 'sequelize-cli';

module.exports = {
	async up(queryInterface, Sequelize) {
		const NEW_COLUMNS = [
			{
				name: 'startDate',
				type: { type: Sequelize.DATE, allowNull: true },
			},
			{
				name: 'startAt',
				type: { type: Sequelize.DATE, allowNull: true },
			},
			{
				name: 'endAt',
				type: { type: Sequelize.DATE, allowNull: true },
			},
		];
		for (const newColumn of NEW_COLUMNS) {
			if (await SequelizeUtility.columnExists(queryInterface, DATABASE_TABLE_NAME.OCCURRENCE, newColumn.name)) {
				await queryInterface.changeColumn(DATABASE_TABLE_NAME.OCCURRENCE, newColumn.name, newColumn.type);
			}
		}
	},

	async down(queryInterface, Sequelize) {
		const OLD_COLUMNS = [
			{
				name: 'startDate',
				type: { type: Sequelize.DATE, allowNull: false },
			},
			{
				name: 'startAt',
				type: { type: Sequelize.DATE, allowNull: false },
			},
			{
				name: 'endAt',
				type: { type: Sequelize.DATE, allowNull: false },
			},
		];
		for (const oldColumns of OLD_COLUMNS) {
			if (await SequelizeUtility.columnExists(queryInterface, DATABASE_TABLE_NAME.OCCURRENCE, oldColumns.name)) {
				await queryInterface.changeColumn(DATABASE_TABLE_NAME.OCCURRENCE, oldColumns.name, oldColumns.type);
			}
		}
	},
} satisfies Migration;

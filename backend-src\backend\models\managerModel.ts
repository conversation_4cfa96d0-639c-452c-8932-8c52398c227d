import { Model, Sequelize, DataTypes, InferAttributes, InferCreationAttributes, CreationOptional } from 'sequelize';
import { DATABASE_TABLE_NAME } from '~config';

export class Manager extends Model<InferAttributes<Manager>, InferCreationAttributes<Manager>> {
	//ATTRIBUTES
	declare managerId: CreationOptional<number>;
	declare username: string;
	declare pwhash: string;
	declare recoveryMail: string;
	declare authLevel: number;
	declare isActivated: CreationOptional<boolean>;
	declare officeName: string | null;
	declare phoneNumber: string | null;
	declare managerName: string | null;
	declare emailContact: string | null;
	declare website: string | null;
	declare officeAddress: string | null;
	declare interviewLocation: string | null;
	//TIMESTAMPS
	declare createdAt: CreationOptional<Date>;
	declare updatedAt: CreationOptional<Date>;
	//ASSOCIATIONS
	static initClass = (sequelize: Sequelize, { defaultAuthLevel }: { defaultAuthLevel: number }) =>
		Manager.init(
			{
				managerId: {
					type: DataTypes.INTEGER({ unsigned: true }),
					allowNull: false,
					primaryKey: true,
					autoIncrement: true,
				},
				username: { type: DataTypes.STRING, allowNull: false, unique: 'username' },
				pwhash: { type: DataTypes.STRING, allowNull: false },
				recoveryMail: { type: DataTypes.STRING, allowNull: true },
				authLevel: { type: DataTypes.INTEGER, defaultValue: defaultAuthLevel, allowNull: false },
				isActivated: { type: DataTypes.BOOLEAN, defaultValue: false },
				officeName: { type: DataTypes.STRING, allowNull: true },
				phoneNumber: { type: DataTypes.STRING, allowNull: true },
				managerName: { type: DataTypes.STRING, allowNull: true },
				website: { type: DataTypes.STRING, allowNull: true },
				emailContact: { type: DataTypes.STRING(5000), allowNull: true },
				officeAddress: { type: DataTypes.STRING, allowNull: true },
				interviewLocation: { type: DataTypes.STRING, allowNull: true },
				createdAt: DataTypes.DATE,
				updatedAt: DataTypes.DATE,
			},
			{
				tableName: DATABASE_TABLE_NAME.MANAGER,
				timestamps: true,
				paranoid: false,
				sequelize: sequelize,
				charset: 'utf8mb4',
				collate: 'utf8mb4_general_ci',
				name: {
					singular: 'Manager',
					plural: DATABASE_TABLE_NAME.MANAGER,
				},
			},
		);
}

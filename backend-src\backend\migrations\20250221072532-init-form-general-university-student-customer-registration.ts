import { Migration } from 'sequelize-cli';
import {
	CUSTOMER_REGISTRATION_FIELD_TYPE,
	CUSTOMER_REGISTRATION_NAME,
	DATABASE_TABLE_NAME,
	STUDENT_TYPE,
} from '~config';
import { get } from 'lodash';

module.exports = {
	async up(queryInterface, Sequelize) {
		const transaction = await queryInterface.sequelize.transaction();

		try {
			const DATA_CREATE = [
				{
					required: true,
					isDisplayed: true,
					label: '代表者氏名',
					type: CUSTOMER_REGISTRATION_FIELD_TYPE.TEXT,
					name: CUSTOMER_REGISTRATION_NAME.REPRESENTATIVE_NAME,
					showOrder: 0,
					studentType: STUDENT_TYPE.GENERAL_UNIVERSITY_STUDENT,
				},
				{
					required: true,
					isDisplayed: true,
					label: 'メールアドレス',
					type: CUSTOMER_REGISTRATION_FIELD_TYPE.TEXT,
					name: CUSTOMER_REGISTRATION_NAME.UNIVERSITY_STUDENT_EMAIL,
					showOrder: 1,
					studentType: STUDENT_TYPE.GENERAL_UNIVERSITY_STUDENT,
				},
				{
					required: true,
					isDisplayed: true,
					label: '大学名',
					type: CUSTOMER_REGISTRATION_FIELD_TYPE.TEXT,
					name: CUSTOMER_REGISTRATION_NAME.UNIVERSITY_NAME,
					showOrder: 2,
					studentType: STUDENT_TYPE.GENERAL_UNIVERSITY_STUDENT,
				},
				{
					required: true,
					isDisplayed: true,
					label: 'エントリー都市',
					type: CUSTOMER_REGISTRATION_FIELD_TYPE.RADIO,
					name: CUSTOMER_REGISTRATION_NAME.ENTRY_CITY,
					showOrder: 3,
					studentType: STUDENT_TYPE.GENERAL_UNIVERSITY_STUDENT,
				},
				{
					required: true,
					isDisplayed: true,
					label: 'クラス',
					type: CUSTOMER_REGISTRATION_FIELD_TYPE.NUMBER,
					name: CUSTOMER_REGISTRATION_NAME.CLASS_UNIVERSITY,
					showOrder: 4,
					studentType: STUDENT_TYPE.GENERAL_UNIVERSITY_STUDENT,
				},
			];

			await queryInterface.bulkDelete(DATABASE_TABLE_NAME.CUSTOMER_REGISTRATIONS, {
				studentType: STUDENT_TYPE.GENERAL_UNIVERSITY_STUDENT,
			});

			for (const dataCreate of DATA_CREATE) {
				await queryInterface.bulkUpdate(
					DATABASE_TABLE_NAME.CUSTOMER_REGISTRATIONS,
					{
						showOrder: Sequelize.literal('showOrder + 1'),
					},
					{
						showOrder: { [Sequelize.Op.gte]: dataCreate.showOrder },
					},
					{ transaction },
				);
				const registrationCreatedId = await queryInterface.bulkInsert(
					DATABASE_TABLE_NAME.CUSTOMER_REGISTRATIONS,
					[dataCreate],
					{
						transaction,
					},
				);

				await queryInterface.addColumn(
					DATABASE_TABLE_NAME.MEMBERS,
					`customerRegistrationId${registrationCreatedId}`,
					{
						type:
							dataCreate.type === CUSTOMER_REGISTRATION_FIELD_TYPE.RADIO
								? Sequelize.DataTypes.JSON
								: Sequelize.DataTypes.STRING,
						defaultValue: null,
						allowNull: true,
					},
					{
						transaction,
					},
				);
			}

			await transaction.commit();
		} catch (error) {
			console.log('🚀 -------------------------------------------------------------------------------------🚀');
			console.log(
				'🚀 ~ file: 20250221072532-init-form-general-university-student-customer-registration ~ up ~ error:',
				error,
			);
			console.log('🚀 -------------------------------------------------------------------------------------🚀');
			await transaction.rollback();
		}
	},

	async down(queryInterface, Sequelize) {
		const transaction = await queryInterface.sequelize.transaction();

		try {
			const DATA_REMOVE = [
				{
					required: true,
					isDisplayed: true,
					label: '氏名',
					type: CUSTOMER_REGISTRATION_FIELD_TYPE.TEXT,
					name: CUSTOMER_REGISTRATION_NAME.FULL_NAME_ENROLLED_STUDENT,
					showOrder: 1,
					studentType: STUDENT_TYPE.ENROLLED_STUDENT,
				},
				{
					required: true,
					isDisplayed: true,
					label: '出場者番号',
					type: CUSTOMER_REGISTRATION_FIELD_TYPE.TEXT,
					name: CUSTOMER_REGISTRATION_NAME.ROLL_NUMBER,
					showOrder: 3,
					studentType: STUDENT_TYPE.ENROLLED_STUDENT,
				},
				{
					required: true,
					isDisplayed: true,
					label: 'プロフィール写真',
					type: CUSTOMER_REGISTRATION_FIELD_TYPE.IMAGE,
					name: CUSTOMER_REGISTRATION_NAME.IMAGE_PROFILE,
					showOrder: 4,
					studentType: STUDENT_TYPE.ENROLLED_STUDENT,
				},
			];
			for (const dataRemove of DATA_REMOVE) {
				const [[field]] = await queryInterface.sequelize.query(
					`SELECT *
           FROM ${DATABASE_TABLE_NAME.CUSTOMER_REGISTRATIONS}
           WHERE name = :name
             AND label = :label`,
					{
						replacements: {
							table: DATABASE_TABLE_NAME.CUSTOMER_REGISTRATIONS,
							name: dataRemove.name,
							label: dataRemove.label,
						},
					},
				);

				await queryInterface.bulkDelete(
					DATABASE_TABLE_NAME.CUSTOMER_REGISTRATIONS,
					{
						name: dataRemove.name,
						label: dataRemove.label,
					},
					{
						transaction,
					},
				);

				await queryInterface.bulkUpdate(
					DATABASE_TABLE_NAME.CUSTOMER_REGISTRATIONS,
					{
						showOrder: Sequelize.literal('showOrder - 1'),
					},
					{
						showOrder: { [Sequelize.Op.gte]: get(field, 'showOrder') },
					},
					{ transaction },
				);

				if (get(field, 'customerRegistrationId')) {
					await queryInterface.removeColumn(
						DATABASE_TABLE_NAME.MEMBERS,
						`customerRegistrationId${get(field, 'customerRegistrationId')}`,
					);
				}
			}
			await transaction.commit();
		} catch (error) {
			console.log('🚀 ---------------------------------------------------------------------------------------🚀');
			console.log(
				'🚀 ~ file: 20250221072532-init-form-general-university-student-customer-registration ~ down ~ error:',
				error,
			);
			console.log('🚀 ---------------------------------------------------------------------------------------🚀');
			await transaction.rollback();
		}
	},
} satisfies Migration;

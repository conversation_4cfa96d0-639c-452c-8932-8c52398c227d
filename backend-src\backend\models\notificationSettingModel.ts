import { Model, DataTypes, Sequelize, InferAttributes, InferCreationAttributes, CreationOptional } from 'sequelize';
import { DATABASE_TABLE_NAME, NOTIFICATION_SETTING, PUBLIC_NOTIFICATION_SETTING } from '~config';

export class NotificationSetting extends Model<
	InferAttributes<NotificationSetting>,
	InferCreationAttributes<NotificationSetting>
> {
	declare settingNotificationId: CreationOptional<number>;
	declare title: string;
	declare content: string;
	declare settingType: NOTIFICATION_SETTING;
	declare isMemberNotificationEnabled: CreationOptional<boolean>;
	declare isAdminNotificationEnabled: CreationOptional<boolean>;
	declare publicSettingStatus: CreationOptional<PUBLIC_NOTIFICATION_SETTING>;

	declare createdAt: CreationOptional<Date>;
	declare updatedAt: CreationOptional<Date>;

	static initClass = (sequelize: Sequelize) =>
		NotificationSetting.init(
			{
				settingNotificationId: {
					type: DataTypes.INTEGER.UNSIGNED,
					autoIncrement: true,
					primaryKey: true,
				},
				title: {
					type: DataTypes.STRING,
					allowNull: false,
				},
				content: {
					type: DataTypes.TEXT,
					allowNull: false,
				},
				isMemberNotificationEnabled: {
					type: DataTypes.BOOLEAN,
					defaultValue: false,
				},
				isAdminNotificationEnabled: {
					type: DataTypes.BOOLEAN,
					defaultValue: false,
				},
				publicSettingStatus: {
					type: DataTypes.ENUM(...Object.values(PUBLIC_NOTIFICATION_SETTING)),
					allowNull: false,
					defaultValue: PUBLIC_NOTIFICATION_SETTING.MAKE_PRIVATE,
				},
				settingType: {
					type: DataTypes.STRING,
					allowNull: false,
					defaultValue: NOTIFICATION_SETTING.notificationSetting,
				},
				createdAt: {
					type: DataTypes.DATE,
					allowNull: false,
					defaultValue: DataTypes.NOW,
				},
				updatedAt: {
					type: DataTypes.DATE,
					allowNull: false,
					defaultValue: DataTypes.NOW,
				},
			},
			{
				sequelize,
				timestamps: true,
				tableName: DATABASE_TABLE_NAME.NOTIFICATION_SETTING,
				charset: 'utf8mb4',
				collate: 'utf8mb4_general_ci',
				name: {
					singular: 'settingNotiPopup',
					plural: DATABASE_TABLE_NAME.NOTIFICATION_SETTING,
				},
			},
		);
}

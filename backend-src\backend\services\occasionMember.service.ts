import { pick } from 'lodash';
import moment from 'moment';
import { Attributes, CreationAttributes, Op, QueryTypes, Transaction, WhereAttributeHash } from 'sequelize';
import {
	BAD_REQUEST,
	CUSTOM_SHOW_MESSAGE,
	INTERVIEW_STEP,
	MEMBER_ACTIONS_STATUS,
	MEMBER_INTERVIEW_STATUS,
} from '~config';
import { db } from '~models';
import { OccasionMember } from '~models/occasionMemberModel';
import { Occasion } from '~models/occasionModel';
import { OccurrenceDetail } from '~models/occurrenceDetailModel';
import { Occurrence } from '~models/occurrenceModel';
import { PaginationSchema } from '~schemas/common';
import {
	AddMemberToOccasionSchema,
	AdminChangeMemberStatusToScheduleAdjustmentSchema,
	AdminGetMemberByInterviewStatusSchema,
	InterviewStatusSchema,
	MemberCancelledEventSchema,
	MemberFinishListSchema,
	MemberScheduleAdjustmentListSchema,
} from '~schemas/occasionMember';
import { AppError, writeLog } from '~utilities';
import {
	memberClassService,
	MemberService,
	OccasionMemberService,
	OccurrenceService,
	ReminderService,
} from '../services';
import { Member } from '~models/memberModel';

export const addMemberToOccasion = async (data: AddMemberToOccasionSchema, transaction?: Transaction) => {
	const { memberIds, occasionId } = data;
	const listExistsData = await db.occasionMembers.findAll({
		where: {
			memberId: { [Op.in]: memberIds as number[] },
			occasionId,
		},
	});
	const listExistMemberIds = listExistsData.map((it) => it.memberId);
	const listMemberAdd = memberIds.filter((item) => !listExistMemberIds.includes(item));
	if (memberIds.length !== listMemberAdd.length) {
		writeLog({ mgs: 'duplicate addMemberToOccasion', listExistMemberIds, memberIds, listMemberAdd }, 'info');
	}
	for (const memberId of listMemberAdd) {
		await db.occasionMembers.create(
			{
				memberId,
				occasionId,
			},
			{ transaction },
		);
	}
	return true;
};

export const memberScheduleAdjustmentList = async (data: MemberScheduleAdjustmentListSchema) => {
	const { page, pageSize, memberLine } = data;
	const currentMember = await db.members.findOne({
		where: {
			lineId: memberLine.userId,
		},
		attributes: ['memberId', 'memberClassId'],
	});

	if (!currentMember) {
		throw new AppError(BAD_REQUEST, 'Member is not found.', false);
	}
	const { memberId } = currentMember;
	const occasions = await db.occasionMembers.findAll({
		where: { memberId, interviewStatus: MEMBER_INTERVIEW_STATUS.SCHEDULE_ADJUSTMENT },
		include: [
			{
				model: db.occasions,
				where: {
					isDisplayed: true,
				},
				include: [
					{
						model: db.categories,
						where: {
							isDisplayed: true,
						},
					},
				],
			},
		],
	});

	const queryOccurrence = [] as any;

	const currentDay = moment().format('YYYY-MM-DD');
	const occasionsWaitingConfirmed = await db.registrations.findAll({
		where: {
			memberId,
			cancelledAt: null,
			attended: 0,
			interviewStatus: MEMBER_INTERVIEW_STATUS.INTERVIEW_PASSED,
		},
	});

	const occasionsWaitingConfirmedIds = occasionsWaitingConfirmed.map((it) => it?.occasionId);

	occasions.forEach((item) => {
		const occurrenceIdsRejected = item.occurrenceIdsRejected || [];
		if (occasionsWaitingConfirmedIds.includes(item.occasionId)) return;
		queryOccurrence.push({
			occasionId: item.occasionId,
			interviewStep: item.memberInterviewCurrentStep,
			responseDeadlineDate: { [Op.gte]: currentDay },
			...(occurrenceIdsRejected.length && { occurrenceId: { [Op.notIn]: occurrenceIdsRejected } }),
		});
	});

	// const queryOccurrenceDetails = await OccurrenceDetailService.getConditionRuleGetOccurrenceDetails(memberId);

	const queryOccurrenceAndIncludes = {
		where: { [Op.or]: queryOccurrence },
		include: [
			{
				model: db.occasions,
				attributes: ['title', 'occasionId'],
				include: [
					{
						model: db.categories,
						attributes: ['title', 'categoryId', 'managerId'],
						include: [
							{
								model: db.managers,
							},
						],
					},
				],
			},
			{
				model: db.occurrenceDetailModel,
				// where: queryOccurrenceDetails,
				required: true,
			},
		],
	};

	const occurrences = await db.occurrences.findAll({
		...queryOccurrenceAndIncludes,
		limit: parseInt(pageSize),
		offset: (parseInt(page) - 1) * parseInt(pageSize),
		order: [['responseDeadlineDate', 'ASC']],
	});

	const list = [] as {
		occurrence: Attributes<Occurrence>;
		Occasion?: Attributes<Occasion>;
		isRejectInterview: boolean;
	}[];

	const totalRegister = await db.registrations.count({
		where: {
			memberId,
			cancelledAt: null,
		},
		include: [
			{
				model: db.occurrences,
				where: {
					interviewStep: INTERVIEW_STEP.FIRST_INTERVIEW,
				},
			},
		],
		distinct: true,
	});

	for (const occurrence of occurrences) {
		let isRejectInterview = false;

		if (occurrence.interviewStep !== INTERVIEW_STEP.FIRST_INTERVIEW) {
			isRejectInterview = true;
		} else if (currentMember.memberClassId) {
			const memberClass = await memberClassService.getMemberClassDetails(currentMember.memberClassId);
			if (memberClass && memberClass.minimumFirstRoundInterviews <= totalRegister) {
				isRejectInterview = true;
			}
		}

		list.push({ occurrence: occurrence.toJSON(), Occasion: occurrence.Occasion, isRejectInterview });
	}

	const total = await db.occurrences.count({
		...queryOccurrenceAndIncludes,
		distinct: true,
	});

	return { list, page: parseInt(page), pageSize: parseInt(pageSize), total };
};

export const memberRejectInterview = async (data: MemberCancelledEventSchema, transaction?: Transaction) => {
	const { occasionId, occurrenceId, memberLine } = data;
	const member = await MemberService.findMemberByLineProfile(memberLine);
	const memberId = member.memberId;
	const occasionMember = await db.occasionMembers.findOne({
		where: {
			memberId,
			occasionId,
		},
	});
	if (!occasionMember) {
		throw new AppError(BAD_REQUEST, 'Occasion Member not found', false);
	}
	const oldOccurrenceIdsCancelled = occasionMember.occurrenceIdsRejected || [];
	const newOccurrenceIdsRejected = [...oldOccurrenceIdsCancelled, occurrenceId];

	const interviewScheduled = await db.registrations.findOne({
		where: {
			memberId,
			occasionId,
			interviewStatus: MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED,
			cancelledAt: null,
		},
	});

	if (interviewScheduled) {
		throw new AppError(BAD_REQUEST, 'Cannot reject the interview while there is a scheduled interview.', false);
	}

	await db.occasionMembers.update(
		{
			occurrenceIdsRejected: [...new Set(newOccurrenceIdsRejected)],
			interviewStatus: MEMBER_INTERVIEW_STATUS.DECLINED,
		},
		{
			where: {
				memberId,
				occasionId,
			},
			transaction,
		},
	);
	return true;
	// if (!registrationId) {
	// 	return null;
	// }
	// await db.registrations.update(
	// 	{
	// 		interviewStatus: MEMBER_INTERVIEW_STATUS.DECLINED,
	// 	},
	// 	{
	// 		where: {
	// 			registrationId,
	// 		},
	// 		transaction,
	// 	},
	// );
	// const registrationRejected = await db.registrations.findOne({
	// 	where: { registrationId },
	// 	attributes: ['occurrenceDetailId'],
	// });
	// return [registrationRejected];
};

export const getOccasionMemberByConditionWhere = async (conditionWhere: WhereAttributeHash) =>
	db.occasionMembers.findOne({
		where: conditionWhere,
	});

export const updateOccasionMemberByConditionWhere = async (
	conditionWhere: WhereAttributeHash,
	dataUpdate: CreationAttributes<OccasionMember>,
	transaction?: Transaction,
) =>
	db.occasionMembers.update(dataUpdate, {
		where: conditionWhere,
		transaction,
	});

export const adminGetMemberByConditionWhere = async (
	conditionWhere: WhereAttributeHash,
	pagination: PaginationSchema,
) => {
	const { page, pageSize } = pagination;
	const occasionMembers = await db.occasionMembers.findAll({
		where: conditionWhere,
		include: [
			{
				model: db.members,
			},
		],
		limit: parseInt(pageSize),
		offset: (parseInt(page) - 1) * parseInt(pageSize),
		order: [['createdAt', 'DESC']],
	});
	const total = await db.occasionMembers.count({ where: conditionWhere });
	return { list: occasionMembers, page: parseInt(page), pageSize: parseInt(pageSize), total };
};

export const memberGetOccasionMembersByStatusInterviewList = async (
	memberId: number,
	statusInterview: InterviewStatusSchema[],
	page: string,
	pageSize: string,
) => {
	const conditionWhere = {
		where: {
			memberId,
			interviewStatus: { [Op.in]: statusInterview },
		},
		include: [
			{
				model: db.occasions,
				attributes: ['occasionId'],
				include: [
					{
						model: db.categories,
						attributes: ['title', 'categoryId', 'managerId'],
						include: [
							{
								model: db.managers,
							},
						],
					},
				],
				required: true,
			},
		],
	};
	const list = await db.occasionMembers.findAll({
		...conditionWhere,
		limit: parseInt(pageSize),
		offset: (parseInt(page) - 1) * parseInt(pageSize),
		order: [['updatedAt', 'DESC']],
	});
	const total = await db.occasionMembers.count(conditionWhere);
	return { list, page: parseInt(page), pageSize: parseInt(pageSize), total };
};

export const adminChangeMemberStatusToScheduleAdjustment = async (
	data: AdminChangeMemberStatusToScheduleAdjustmentSchema,
	transaction?: Transaction,
) => {
	const { occasionMemberIds, occasionId, memberIds } = data;

	const hasMemberAcceptedOffice = await db.registrations.count({
		where: {
			memberId: { [Op.in]: memberIds },
			cancelledAt: null,
			deletedAt: null,
			memberActionStatus: MEMBER_ACTIONS_STATUS.MEMBER_ACCEPTED,
		},
	});

	if (hasMemberAcceptedOffice) {
		throw new AppError(BAD_REQUEST, 'Member has accepted office', false);
	}

	const listOccasionMemberAdded = await db.occasionMembers.findAll({
		where: {
			occasionId,
			memberInterviewCurrentStep: INTERVIEW_STEP.FIRST_INTERVIEW,
			interviewStatus: MEMBER_INTERVIEW_STATUS.SCHEDULE_ADJUSTMENT,
		},
	});

	const isMaxAttendees = await OccurrenceService.checkMaxAttendees(
		occasionId,
		INTERVIEW_STEP.FIRST_INTERVIEW,
		occasionMemberIds.length + listOccasionMemberAdded.length,
	);

	if (isMaxAttendees.status) {
		throw new AppError(BAD_REQUEST, `Occurrence is full ${CUSTOM_SHOW_MESSAGE} ${isMaxAttendees.reason}`);
	}

	await db.occasionMembers.update(
		{ interviewStatus: MEMBER_INTERVIEW_STATUS.SCHEDULE_ADJUSTMENT },
		{
			where: {
				occasionMemberId: { [Op.in]: occasionMemberIds },
			},
			transaction,
		},
	);

	ReminderService.messageDeliveredAfterReservationReminder(occasionMemberIds);

	return true;
};

export const adminGetMemberByInterview = async (data: AdminGetMemberByInterviewStatusSchema) => {
	const occasionId = data.occasionId;
	const condition = {
		interviewStatus: { [Op.in]: data.interviewStatuses },
		occasionId,
		...(data.interviewSteps ? { memberInterviewCurrentStep: { [Op.in]: data.interviewSteps } } : {}),
	};
	const pagination = pick(data, ['page', 'pageSize']);
	const { page, pageSize } = pagination;

	const occasionMembers = await db.occasionMembers.findAll({
		where: condition,
		limit: parseInt(pageSize),
		offset: (parseInt(page) - 1) * parseInt(pageSize),
		order: [
			['createdAt', 'DESC'],
			['occasionMemberId', 'DESC'],
		],
		raw: true,
	});

	const memberIds = occasionMembers.map((occasionMember) => occasionMember.memberId);

	let members = [] as Member[];

	if (memberIds.length > 0) {
		members = await db.sequelize.query(
			`SELECT *
			 FROM members
			 WHERE memberId IN (:memberIds)`,
			{
				type: QueryTypes.SELECT,
				replacements: { memberIds },
			},
		);
	}

	let registrationConditionWhere = {
		memberId: { [Op.in]: memberIds },
		occasionId,
		cancelledAt: null,
		deletedAt: null,
	} as WhereAttributeHash;

	let occurrenceConditionWhere = {
		...(data.interviewSteps ? { interviewStep: { [Op.in]: data.interviewSteps } } : {}),
	};

	const isGetInterviewProposal =
		data.interviewStatuses.length === 1 && data.interviewStatuses.includes(MEMBER_INTERVIEW_STATUS.INTERVIEW_PROPOSAL);

	if (isGetInterviewProposal) {
		registrationConditionWhere = {
			memberId: { [Op.in]: memberIds },
			cancelledAt: null,
			deletedAt: null,
		};
		occurrenceConditionWhere = {};
	}

	const registrations = await db.registrations.findAll({
		where: registrationConditionWhere,
		include: [
			{
				model: OccurrenceDetail,
				attributes: ['eventDay', 'startTime', 'endTime', 'interviewType', 'interviewLocation'],
			},
			{
				model: db.occurrences,
				where: occurrenceConditionWhere,
				attributes: ['occurrenceId', 'responseDeadlineDate', 'interviewStep'],
				required: true,
			},
		],
		raw: true,
		nest: true,
	});

	const occasionMemberIds = occasionMembers.map((om) => om.occasionMemberId);
	const occurrencesByOccasion = await db.occurrences.findAll({
		attributes: ['occasionId', 'interviewStep', 'responseDeadlineDate'],
		where: {
			occasionId: {
				[Op.in]: occasionMembers.map((om) => om.occasionId),
			},
		},
		raw: true,
	});

	const list = occasionMembers.map((occasionMember) => {
		let registration = null;
		if (!isGetInterviewProposal) {
			registration = registrations.find(
				(reg) =>
					reg.memberId === occasionMember.memberId &&
					reg?.occurrence?.interviewStep === occasionMember?.memberInterviewCurrentStep,
			);
		}
		const isAcceptedOffice = registrations.some(
			(reg) =>
				reg.memberId === occasionMember.memberId && reg.memberActionStatus === MEMBER_ACTIONS_STATUS.MEMBER_ACCEPTED,
		);
		const deadline = occurrencesByOccasion.find(
			(d) =>
				d.occasionId === occasionMember.occasionId && d.interviewStep === occasionMember.memberInterviewCurrentStep,
		);
		return {
			...occasionMember,
			responseDeadlineDate: deadline ? deadline.responseDeadlineDate : null,
			Member: members.find((it: any) => occasionMember.memberId === it.memberId),
			registration: registration || null,
			isAcceptedOffice,
		};
	});

	const total = await db.occasionMembers.count({ where: condition });

	return { list, page: parseInt(page), pageSize: parseInt(pageSize), total };
};

export const updateOccasionMemberAfterRemoveMemberClass = (memberIds: number[], transaction?: Transaction) => {
	return db.occasionMembers.destroy({
		where: { memberId: { [Op.in]: memberIds } },
		transaction,
	});
};

export const memberInterviewFinishList = async (data: MemberFinishListSchema) => {
	const { memberLine, page, pageSize } = data;
	const memberInfo = await MemberService.findMemberByLineProfile(memberLine);
	const interviewStatues = [
		MEMBER_INTERVIEW_STATUS.NOT_ACCEPTED,
		MEMBER_INTERVIEW_STATUS.DECLINED,
		MEMBER_INTERVIEW_STATUS.AUTO_DECLINED,
	];
	return OccasionMemberService.memberGetOccasionMembersByStatusInterviewList(
		memberInfo.memberId,
		interviewStatues,
		page,
		pageSize,
	);
};

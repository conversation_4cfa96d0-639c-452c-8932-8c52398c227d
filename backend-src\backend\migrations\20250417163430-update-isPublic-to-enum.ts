import { QueryInterface, DataTypes } from 'sequelize';
import { DATABASE_TABLE_NAME, PUBLIC_NOTIFICATION_SETTING } from '~config'; 

export const up = async (queryInterface: QueryInterface) => {
	await queryInterface.changeColumn(DATABASE_TABLE_NAME.NOTIFICATION_SETTING, 'isPublish', {
		type: DataTypes.ENUM(...Object.values(PUBLIC_NOTIFICATION_SETTING)), 
		allowNull: false,
		defaultValue: PUBLIC_NOTIFICATION_SETTING.MAKE_PRIVATE, 
	});
};

export const down = async (queryInterface: QueryInterface) => {

	await queryInterface.changeColumn(DATABASE_TABLE_NAME.NOTIFICATION_SETTING, 'isPublish', {
		type: DataTypes.BOOLEAN,
		allowNull: false,
		defaultValue: false,
	});
};

import { CreationOptional, DataTypes, InferAttributes, InferCreationAttributes, Model, Sequelize } from 'sequelize';
import { DATABASE_TABLE_NAME } from '~config';

export class BufferEventTimeSettingModel extends Model<
	InferAttributes<BufferEventTimeSettingModel>,
	InferCreationAttributes<BufferEventTimeSettingModel>
> {
	//ATTRIBUTES
	declare bufferEventTimeSettingId: CreationOptional<number>;
	declare startTime: number;
	declare endTime: number;
	declare key: CreationOptional<string>;
	static initClass = (sequelize: Sequelize) =>
		BufferEventTimeSettingModel.init(
			{
				bufferEventTimeSettingId: {
					type: DataTypes.INTEGER({ unsigned: true }),
					primaryKey: true,
					autoIncrement: true,
				},
				key: {
					type: DataTypes.STRING,
					unique: true,
					defaultValue: 'singleton',
				},
				startTime: {
					type: DataTypes.INTEGER,
					allowNull: false,
					defaultValue: 0,
				},
				endTime: {
					type: DataTypes.INTEGER,
					allowNull: false,
					defaultValue: 0,
				},
			},
			{
				tableName: DATABASE_TABLE_NAME.BUFFER_EVENT_TIME_SETTINGS,
				timestamps: true,
				paranoid: false,
				sequelize: sequelize,
				charset: 'utf8mb4',
				collate: 'utf8mb4_general_ci',
				name: {
					singular: 'bufferEventTimeSetting',
					plural: DATABASE_TABLE_NAME.BUFFER_EVENT_TIME_SETTINGS,
				},
			},
		);
}

{"name": "kakeru-baby-property-booking", "version": "1.0.0", "description": "LINE連動　物件内覧予約システム", "main": "server.js", "scripts": {"build": "rimraf server.js && rimraf ./backend && tsc", "dev": "ts-node-dev -r tsconfig-paths/register --respawn --transpile-only --exit-child --watch backend-src backend-src/server.ts", "start": "node -r ./module-alias.js server.js", "test:js": "ts-node ./backend/tests/logger.test.js", "test:ts": "ts-node ./backend-src/test/logger.test.ts", "lint": "eslint --cache \"backend-src/**/*.{ts,d.ts}\"", "lint:fix": "eslint --cache --fix \"backend-src/**/*.{ts,d.ts}\"", "deptestServer": "depcruise --exclude \"^node_modules\" --output-type dot server.js | dot -T svg -o dependency_graphs/dependencyGraph.svg", "quickSync": "node -r ./module-alias.js --trace-warnings ./backend/dev_ops/quickSync.js", "initManager": "node -r ./module-alias.js --trace-warnings ./backend/dev_ops/initManager.js", "clinic:doctor": "clinic doctor --on-port 'autocannon -c 10 -d 10 -m GET localhost:$PORT/api/m/categories' -- node server.js", "clinic:bubbleprof": "clinic bubbleprof --on-port 'autocannon -c 5 -a 500 -m GET localhost:$PORT/api/m/categories' -- node server.js", "pretty": "prettier --write ./backend-src", "db:migrate": "sequelize-cli db:migrate", "db:migrate:create": "sequelize-cli migration:create", "db:migrate:generate": "sequelize-cli migration:generate", "db:migrate:undo": "sequelize-cli db:migrate:undo", "db:seed": "sequelize-cli db:seed:all", "db:seed:generate": "sequelize-cli seed:generate", "db:seed:undo": "sequelize-cli db:seed:undo"}, "nodemonConfig": {"ignore": ["frontend/*", "dev_ops/*", "tsconfig.json"], "watch": ["src"], "ext": "ts,d.ts,json", "exec": "ts-node --files ./backend-src/server.ts"}, "engines": {"node": ">=16.20.0"}, "author": "<PERSON><PERSON>", "license": "UNLICENSED", "private": "true", "dependencies": {"@line/bot-sdk": "^7.5.2", "@sinclair/typebox": "^0.32.28", "ajv": "^8.13.0", "ajv-formats": "^3.0.1", "axios": "^0.27.2", "base-64": "^1.0.0", "bcryptjs": "^2.4.3", "chance": "^1.1.9", "connect-session-sequelize": "^7.1.5", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.0.3", "express": "^4.18.2", "express-json-validator-middleware": "^3.0.1", "express-session": "^1.17.2", "generate-password": "^1.7.0", "helmet": "^5.1.1", "ioredis": "^5.4.1", "json2csv": "^5.0.7", "jsonwebtoken": "^8.5.1", "lodash.omit": "^4.5.0", "module-alias": "^2.2.3", "moment": "^2.29.4", "moment-timezone": "^0.5.45", "multer": "^1.4.5-lts.1", "mysql2": "^2.3.3", "nanoid": "^3.3.4", "node-schedule": "^2.1.0", "nodemailer": "^6.9.16", "nodemon": "^3.0.1", "papaparse": "^5.4.1", "qrcode": "^1.5.3", "sequelize": "^6.28.0", "socket.io": "^4.5.4", "tsconfig-paths": "^4.2.0", "winston": "^3.8.2", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/chance": "^1.1.3", "@types/cors": "^2.8.13", "@types/express": "^4.17.15", "@types/express-session": "^1.17.5", "@types/json2csv": "^5.0.3", "@types/jsonwebtoken": "^8.5.9", "@types/lodash.omit": "^4.5.9", "@types/multer": "^1.4.7", "@types/node": "^14.18.33", "@types/node-schedule": "^2.1.0", "@types/nodemailer": "^6.4.17", "@types/papaparse": "^5.3.14", "@typescript-eslint/eslint-plugin": "^5.47.0", "@typescript-eslint/parser": "^5.47.0", "eslint": "^8.30.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-security": "^1.5.0", "prettier": "^2.8.1", "sequelize-cli": "^6.6.2", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^4.9.4"}}
{"env": {"node": true, "commonjs": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:prettier/recommended", "plugin:security/recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended"], "overrides": [], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["prettier", "security", "@typescript-eslint"], "rules": {"quotes": ["error", "single"], "@typescript-eslint/no-empty-interface": "off", "no-console": "warn", "no-undef": "off", "no-unused-vars": "off", "prettier/prettier": "error", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": ["warn", {"ignoreRestSiblings": true}]}, "ignorePatterns": ["node_modules", "frontend", "logs", "public", "*.js"]}
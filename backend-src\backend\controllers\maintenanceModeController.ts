import { NextFunction, Request, Response } from 'express';
import { RESPONSE_SUCCESS } from '../config';
import { db } from '../models/index';
import { SocketServerService } from '../services';

/**
 * Get the current maintenance mode status
 */
export const getMaintenanceStatus = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const status = await db.maintenanceMode.findOne({
			order: [['id', 'ASC']],
		});
		res.send(status || { isEnabled: false, imageFilename: null });
	} catch (e) {
		next(e);
	}
};

/**
 * Enable maintenance mode
 */
export const enableMaintenanceMode = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const { imageFilename } = req.body;

		let status = await db.maintenanceMode.findOne({
			order: [['id', 'ASC']],
		});

		if (status) {
			status.isEnabled = true;
			status.imageFilename = imageFilename || null;
			await status.save();
		} else {
			status = await db.maintenanceMode.create({
				isEnabled: true,
				imageFilename: imageFilename || null,
			});
		}

		SocketServerService.emitMaintenanceModeChanged({
			isEnabled: status.isEnabled,
			imageFilename: status.imageFilename,
		});

		res.sendStatus(RESPONSE_SUCCESS);
	} catch (e) {
		next(e);
	}
};

/**
 * Disable maintenance mode
 */
export const disableMaintenanceMode = async (req: Request, res: Response, next: NextFunction) => {
	try {
		// Find the first record or create one if none exists
		let status = await db.maintenanceMode.findOne({
			order: [['id', 'ASC']],
		});

		if (status) {
			status.isEnabled = false;
			status.imageFilename = null;
			await status.save();
		} else {
			status = await db.maintenanceMode.create({
				isEnabled: false,
				imageFilename: null,
			});
		}

		SocketServerService.emitMaintenanceModeChanged({
			isEnabled: status.isEnabled,
			imageFilename: status.imageFilename,
		});

		res.sendStatus(RESPONSE_SUCCESS);
	} catch (e) {
		next(e);
	}
};

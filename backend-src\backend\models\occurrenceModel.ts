import {
	Association,
	CreationOptional,
	DataTypes,
	ForeignKey,
	InferAttributes,
	InferCreationAttributes,
	Model,
	NonAttribute,
	Sequelize,
} from 'sequelize';
import { Category } from './categoryModel';
import { Occasion } from './occasionModel';
import { Registration } from './registrationModel';
import { Campaign } from './campaignModel';
import { DATABASE_TABLE_NAME, INTERVIEW_STEP } from '~config';
import { OccurrenceDetail } from '~models/occurrenceDetailModel';

export class Occurrence extends Model<
	InferAttributes<Occurrence, { omit: 'Occasion' | 'Category' | 'registrations' | 'Campaign' }>,
	InferCreationAttributes<
		Occurrence,
		{
			omit: 'Occasion' | 'Category' | 'registrations' | 'Campaign' | 'occurrenceDetails';
		}
	>
> {
	declare occurrenceId: CreationOptional<number>;
	declare occasionId: ForeignKey<Occasion['occasionId'] | null>;
	declare categoryId: ForeignKey<Category['categoryId'] | null>;
	declare campaignId: ForeignKey<Campaign['campaignId'] | null>;
	declare startDate: CreationOptional<Date>;
	declare startAt: CreationOptional<Date>;
	declare endAt: CreationOptional<Date>;
	declare maxAttendee: number;
	declare isDisplayed: CreationOptional<boolean>;
	declare remarks: CreationOptional<string> | null;
	declare sumExpected?: NonAttribute<number>;
	declare isSettingTime: CreationOptional<boolean>;
	declare notes: CreationOptional<string> | null;
	declare responseDeadlineDate: CreationOptional<string>;
	declare interviewStep: CreationOptional<INTERVIEW_STEP>;

	//TIMESTAMPS
	declare createdAt: CreationOptional<Date>;
	declare updatedAt: CreationOptional<Date>;
	declare deletedAt: CreationOptional<Date>;
	//ASSOCIATIONS
	declare Category?: NonAttribute<Category>;
	declare Occasion?: NonAttribute<Occasion>;
	declare registrations?: NonAttribute<Registration[]>;
	declare Campaign?: NonAttribute<Campaign>;
	declare occurrenceDetails?: CreationOptional<OccurrenceDetail[]>;
	declare static associations: {
		Category: Association<Occurrence, Category>;
		Occasion: Association<Occurrence, Occasion>;
		registrations: Association<Occurrence, Registration>;
		Campaign: Association<Occurrence, Campaign>;
	};
	static initClass = (sequelize: Sequelize) =>
		Occurrence.init(
			{
				occurrenceId: {
					type: DataTypes.INTEGER({ unsigned: true }),
					allowNull: false,
					primaryKey: true,
					autoIncrement: true,
				},
				startDate: { type: DataTypes.DATE, allowNull: true, defaultValue: null },
				startAt: { type: DataTypes.DATE, allowNull: true, defaultValue: null },
				endAt: { type: DataTypes.DATE, allowNull: true, defaultValue: null },
				maxAttendee: { type: DataTypes.INTEGER, allowNull: false },
				isDisplayed: { type: DataTypes.BOOLEAN, allowNull: false, defaultValue: true },
				remarks: { type: DataTypes.STRING, allowNull: true, defaultValue: null },
				isSettingTime: { type: DataTypes.BOOLEAN, allowNull: false, defaultValue: false },
				notes: { type: DataTypes.STRING(5000), allowNull: true },
				responseDeadlineDate: { type: DataTypes.DATEONLY, allowNull: true },
				interviewStep: {
					type: DataTypes.ENUM(...Object.values(INTERVIEW_STEP)),
					allowNull: false,
					defaultValue: INTERVIEW_STEP.FIRST_INTERVIEW,
				},

				createdAt: DataTypes.DATE,
				updatedAt: DataTypes.DATE,
				deletedAt: DataTypes.DATE,
			},
			{
				sequelize: sequelize,
				timestamps: true,
				paranoid: true,
				charset: 'utf8mb4',
				collate: 'utf8mb4_general_ci',
				tableName: DATABASE_TABLE_NAME.OCCURRENCE,
				modelName: 'occurrence',
				name: {
					singular: 'occurrence',
					plural: DATABASE_TABLE_NAME.OCCURRENCE,
				},
			},
		);
}

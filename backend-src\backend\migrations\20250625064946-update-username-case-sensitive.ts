import { DATABASE_TABLE_NAME } from '../config';
import { SequelizeUtility } from '../utilities';

import type { Migration } from 'sequelize-cli';

module.exports = {
	async up(queryInterface, Sequelize) {
		if (await SequelizeUtility.columnExists(queryInterface, DATABASE_TABLE_NAME.MANAGER, 'username')) {
			await queryInterface.sequelize.query(`
                ALTER TABLE \`${DATABASE_TABLE_NAME.MANAGER}\`
                MODIFY COLUMN \`username\` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL UNIQUE;
              `);
		}
	},

	async down(queryInterface, Sequelize) {
		if (await SequelizeUtility.columnExists(queryInterface, DATABASE_TABLE_NAME.MANAGER, 'username')) {
			await queryInterface.sequelize.query(`
                ALTER TABLE \`${DATABASE_TABLE_NAME.MANAGER}\`
                MODIFY COLUMN \`username\` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL UNIQUE;
              `);
		}
	},
} satisfies Migration;

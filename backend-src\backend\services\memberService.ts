import json2csv from 'json2csv';
import _, { cloneDeep, get, isEmpty, map } from 'lodash';
import moment from 'moment';
import {
	Attributes,
	cast,
	col,
	CreationAttributes,
	FindOptions,
	HasOne,
	literal,
	Op,
	QueryTypes,
	Sequelize,
	Transaction,
	WhereAttributeHash,
} from 'sequelize';
import { CampaignAnswer } from '~models/campaignAnswerModel';
import { CustomerRegistration } from '~models/customerRegistrationModel';
import { MemberService, OccasionMemberService, RegistrationService, SocketServerService } from '~services';
import {
	AppError,
	buildPaginationParams,
	CommonUtils,
	FileOps,
	FileUtility,
	generateWhereClauseBetween,
} from '~utilities';
import path from 'path';

import { DrawModel } from '~models/draw.model';
import { MemberSurveyReward } from '~models/memberSurveyRewardModel';
import type { GetInterviewHistoriesByMemberSchema } from '~schemas/member';
import { GetAllMemberSchema, MemberLineSchema } from '~schemas/member';
import { formatDate } from '~utilities/commonDateTime';
import {
	BAD_REQUEST,
	CAMPAIGN_CHOICES_TYPE_OTHER,
	CONTENT_TYPE_SURVEY_OTHER,
	CUSTOMER_REGISTRATION_FIELD_IMAGE_LABEL,
	CUSTOMER_REGISTRATION_FIELD_TYPE,
	CUSTOMER_REGISTRATION_NAME,
	DATABASE_TABLE_NAME,
	INTERVIEW_STEP,
	MEMBER_ACTIONS_STATUS,
	MEMBER_INTERVIEW_STATUS,
	MEMBER_IS_FRIEND_LABEL,
	RICH_MENU_TYPE,
	SORT,
	STUDENT_TYPE,
	SYSTEM_ERROR,
	systemConfig,
} from '../config';
import { db } from '../models';
import { Member } from '../models/memberModel';
import { linkRichMenuToUser, unlinkRichMenuFromUser } from './lineService';
import { Includeable } from 'sequelize/types/model';
import deleteFilesOldLocal from '../utilities/unLinkFile';
import { Occasion } from '~models/occasionModel';
import { Category } from '~models/categoryModel';
import { OccasionMember } from '~models/occasionMemberModel';
import { fieldKeysCsv, FieldKeyCsvType } from './../config/constants';
import { isNumberType } from '~utilities/isNumberUtil';
import type { Order, OrderItem } from 'sequelize';

type UpdateMember = Attributes<Member> & {
	pointIsAdd?: boolean;
	points?: number;
	isEditMember?: string;
};

type DataDuplicate = {
	label: string;
	value: string;
	checkIsGet: boolean;
};

export type ItemDynamicField = {
	label: string;
	value: string;
};

interface OccasionMemberData {
	memberId: number;
	interviewStatus: string;
	memberInterviewCurrentStep: string;
	Occasion?: {
		Category?: {
			title: string;
		};
	};
}

const incrementCounter = (counterObj: Record<number, number>, memberId: number): void => {
	counterObj[memberId] = (counterObj[memberId] || 0) + 1;
};

function addUniqueItem<T>(array: T[], item: T): void {
	if (!array.includes(item)) {
		array.push(item);
	}
}

export const transformUserCsvData = (
	member: Member,
	customerRegistrations: CustomerRegistration[],
	initValues: Record<string, string>,
	classesMapByClassId?: Record<string, string>,
): Record<string, string> => {
	if (!member?.lineId) return initValues;

	const data = cloneDeep(initValues);

	const labelCountMap: Record<string, number> = {};

	customerRegistrations?.length &&
		customerRegistrations.forEach((customerRegistration) => {
			const originalLabel = customerRegistration.label;

			// Create unique key avoid same label
			let uniqueKey = originalLabel;
			if (labelCountMap[originalLabel] !== undefined) {
				labelCountMap[originalLabel]++;
				uniqueKey = `${originalLabel}_${labelCountMap[originalLabel]}`;
			} else {
				labelCountMap[originalLabel] = 0;
			}

			if (customerRegistration?.type !== CUSTOMER_REGISTRATION_FIELD_TYPE.CHECKBOX) {
				const value = get(member, `customerRegistrationId${customerRegistration.customerRegistrationId}`, '');
				switch (customerRegistration?.type) {
					case CUSTOMER_REGISTRATION_FIELD_TYPE.DATE_PICKER:
						data[uniqueKey] = value ? moment(new Date(value)).format('YYYY-MM-DD') : '';
						break;

					case CUSTOMER_REGISTRATION_FIELD_TYPE.IMAGE:
						data[uniqueKey] = value
							? FileUtility.getMemberImageUrl(value)
							: CUSTOMER_REGISTRATION_FIELD_IMAGE_LABEL.NOT_EXIST;
						break;

					case CUSTOMER_REGISTRATION_FIELD_TYPE.RADIO:
						data[uniqueKey] = get(value, 'value', '');
						break;

					default:
						if (customerRegistration.name === 'class') {
							data[uniqueKey] = _.get(classesMapByClassId, value, '');
						} else {
							data[uniqueKey] = value || '';
						}
						break;
				}
			} else {
				const values = get(member, `customerRegistrationId${customerRegistration.customerRegistrationId}`, []);
				data[uniqueKey] = map(values, ({ value }) => value).join(',');
			}
		});
	if (member.campaignAnswers) {
		member.campaignAnswers.forEach((cA: CampaignAnswer) => {
			if (cA.CampaignQuestion) {
				data[cA.CampaignQuestion.contents] = cA.contents ?? '';
			}
		});
	}
	return data;
};

export const transformUserCsvDataMember = (
	member: Member,
	customerRegistrations: CustomerRegistration[],
	initValues: Record<string, string | number>,
	classesMapByClassId: Record<string, string>,
	classCategoryNameById: Record<string, string>,
	minimumFirstRoundInterviewsByClassId: Record<string, number>,
	membersInterviewNOT_ACCEPTED: Record<string, number | string>,
	membersInterviewDECLINED: Record<string, number>,
	listIdMemberHaveOffficeSendInterview: Record<string, string[]>,
	listIdCategoryHaveMemberInterview: Record<string, number[]>,
	allowedLabels: DataDuplicate[],
	fieldKeys: Record<FieldKeyCsvType, ItemDynamicField>,
): Record<string, string | number> => {
	if (!member?.lineId) return initValues;

	const memberId = get(member, 'memberId', '');
	const columnFormatTextToNumber = [fieldKeys.rollNumber.value];
	let minimumFirstRoundInterviews: number | string = 0;
	let result = 0;
	const dataCheckIsDulicate = JSON.parse(JSON.stringify(allowedLabels));

	const data = cloneDeep(initValues);

	if (customerRegistrations?.length) {
		customerRegistrations.forEach((customerRegistration, idx) => {
			const value = get(member, `customerRegistrationId${customerRegistration.customerRegistrationId}`, '');
			let safeKey = `dynamic_${idx}`;
			const label_column = customerRegistration.label;
			const indexFound = dataCheckIsDulicate.findIndex((item: DataDuplicate) => item.label === label_column);
			if (indexFound > -1) {
				if (!dataCheckIsDulicate[indexFound].checkIsGet) {
					dataCheckIsDulicate[indexFound].checkIsGet = true;
					safeKey = dataCheckIsDulicate[indexFound].value;
				}
			}
			if (customerRegistration?.type !== CUSTOMER_REGISTRATION_FIELD_TYPE.CHECKBOX) {
				switch (customerRegistration?.type) {
					case CUSTOMER_REGISTRATION_FIELD_TYPE.DATE_PICKER:
						data[safeKey] = value ? moment(new Date(value)).format('YYYY-MM-DD') : '';
						break;

					case CUSTOMER_REGISTRATION_FIELD_TYPE.IMAGE:
						data[safeKey] = value
							? FileUtility.getMemberImageUrl(value)
							: CUSTOMER_REGISTRATION_FIELD_IMAGE_LABEL.NOT_EXIST;
						break;

					case CUSTOMER_REGISTRATION_FIELD_TYPE.RADIO:
						data[safeKey] = get(value, 'value', '');
						break;

					default:
						if (customerRegistration.name === 'class') {
							minimumFirstRoundInterviews = _.get(minimumFirstRoundInterviewsByClassId, value, '');
							result = minimumFirstRoundInterviews !== '' ? Number(minimumFirstRoundInterviews) : 0;
							data[safeKey] = _.get(classesMapByClassId, value, '');
							data[fieldKeys.category.value] = _.get(classCategoryNameById, value, '');
							data[fieldKeys.minInterviewCount.value] = result;
						} else {
							data[safeKey] = value;
						}

						if (columnFormatTextToNumber.includes(customerRegistration.name as string)) {
							if (safeKey === fieldKeys.rollNumber.value && !value) {
								data[safeKey] = '';
							} else {
								data[safeKey] = Number(value);
							}
						}
						break;
				}
			} else {
				const values = get(member, `customerRegistrationId${customerRegistration.customerRegistrationId}`, []);
				data[safeKey] = map(values, ({ value }) => value).join(',');
			}
		});
	}

	data[fieldKeys.firstInterviewExpired.value] = membersInterviewNOT_ACCEPTED[memberId] ?? '';
	data[fieldKeys.firstInterviewSelfCancel.value] = membersInterviewDECLINED[memberId] ?? '';

	const countOfficeInterviewMember = listIdMemberHaveOffficeSendInterview[memberId]?.length || 0;
	data[fieldKeys.interviewRequestCount.value] = countOfficeInterviewMember;
	data[fieldKeys.minInterviewDiff.value] = countOfficeInterviewMember - result;

	Object.keys(listIdCategoryHaveMemberInterview).forEach((category, idx) => {
		const key = `officeTitle_${idx}`;
		const listMember = listIdCategoryHaveMemberInterview[category] as any;
		data[key] = listMember.includes(memberId) ? 1 : '';
	});

	return data;
};

export const findMemberByLineProfile = async (
	memberLine: MemberLineSchema,
	transaction?: Transaction,
	get?: boolean,
) => {
	const customerRegistrations = await db.customerRegistrations.findAll({
		where: {
			isDisplayed: true,
		},
		include: {
			association: db.customerRegistrations.associations.campaignChoices,
		},
		order: [['showOrder', 'asc']],
	});
	const attributes = customerRegistrations.map((item) => {
		return `customerRegistrationId${item?.customerRegistrationId}`;
	});
	const member = await db.members.findOne({
		where: { lineId: memberLine.userId },
		attributes: [
			'memberId',
			'memberCode',
			'lineId',
			'displayName',
			'picUrl',
			'firstName',
			'lastName',
			'firstNameKana',
			'lastNameKana',
			'email',
			'telephone',
			'postalCode',
			'building',
			'address',
			'memberSince',
			'curRM',
			'isCampaign',
			'candidateAt',
			'isRegistered',
			'isFriends',
			'unreadCount',
			'createdAt',
			'updatedAt',
			'activeUntil',
			'currentPoints',
			'studentType',
			'fullName',
			'imageProfile',
			'representativeName',
			'universityStudentEmail',
			'entryCity',
			'universityName',
			...attributes,
		],
		transaction,
	});
	if (member == null) {
		const member = await db.members.create(
			{
				lineId: memberLine.userId,
				displayName: memberLine.displayName,
				picUrl: memberLine.pictureUrl,
				curRM: 'defaultRM',
				via: 'others',
				origin: 'new',
			},
			{ transaction },
		);
		SocketServerService.emitMemberCreated(member);
		return member;
	} else {
		if (get) {
			return member;
		}
		return member.update(
			{
				displayName: memberLine.displayName,
				picUrl: memberLine.pictureUrl,
			},
			{ transaction },
		);
	}
};
export const findMemberByCode = async (memberCode: string, transaction?: Transaction) =>
	db.members.findOne({
		where: { memberCode: memberCode },
		transaction,
	});

export const findMemberByLineId = async (lineId: string, transaction?: Transaction) =>
	db.members.findOne({
		where: { lineId: lineId },
		transaction,
	});

export const findMemberById = async (memberId: number, transaction?: Transaction) =>
	db.sequelize.query(
		`
			SELECT m.*,
						 mc.*,
						 cc.*,
						 cc.name AS "classCategoryName",
						 (SELECT COUNT(DISTINCT o.categoryId)
							FROM ${DATABASE_TABLE_NAME.OCCASION_MEMBER} om
										 INNER JOIN ${DATABASE_TABLE_NAME.OCCASION} o ON om.occasionId = o.occasionId
							WHERE om.memberId = m.memberId
								AND om.interviewStatus != :proposalStatus
				) AS interviewOfficeCount
			FROM ${DATABASE_TABLE_NAME.MEMBERS} AS m
				LEFT JOIN ${DATABASE_TABLE_NAME.MEMBER_CLASS} AS mc
			ON mc.memberClassId = m.memberClassId
				LEFT JOIN ${DATABASE_TABLE_NAME.CLASS_CATEGORY} AS cc ON cc.classCategoryId = mc.classCategoryId
			WHERE m.memberId = :memberId
				LIMIT 1
		`,
		{
			replacements: {
				memberId,
				proposalStatus: MEMBER_INTERVIEW_STATUS.INTERVIEW_PROPOSAL,
			},
			type: QueryTypes.SELECT,
			plain: true,
			transaction,
		},
	) as any;

// db.members.findByPk(memberId, {
// 	attributes: { exclude: ['lineId'] },
// 	include: [
// 		{
// 			association: db.members.associations.registrations,
// 			attributes: {
// 				exclude: ['isRegistered', 'isFriends', 'isManual', 'isNotified1', 'isNotified2']
// 			},
// 			include: [{ association: db.registrations.associations.Occurrence }]
// 		},
// 		{
// 			separate: true,
// 			association: db.members.associations.campaignAnswers,
// 			attributes: ['campaignQuestionId', 'contents']
// 		}
// 	],
// 	transaction
// })

export const countOtherMemberByEmail = async (memberId: number, email: string, transaction?: Transaction) =>
	db.members.count({
		where: { memberId: { [Op.ne]: memberId }, email: email, memberCode: { [Op.not]: null } },
		transaction,
	});

export const browseMembersHandler = async (
	filters: WhereAttributeHash<Member> &
		Partial<{
			createdAtMax: Date;
			createdAtMin: Date;
			friendAddedDateMax: Date;
			friendAddedDateMin: Date;
			displayName: string;
			isFriends: 'true' | 'false';
			isRegistered: 'true' | 'false';
			notes: string;
			lastVisitMax: Date;
			lastVisitMin: Date;
			memberSinceMax: Date;
			memberSinceMin: Date;
			currentPointsMin: Date;
			currentPointsMax: Date;
			messages: 'read' | 'unread';
			memberId: string;
			studentType: string;
			classCategoryId: string;
			interviewOfficeCountMin: number;
			interviewOfficeCountMax: number;
			minimumFirstRoundInterviewsMin: number;
			minimumFirstRoundInterviewsMax: number;
		}>,
	pagination?: { pp: number; p: number; sort: 'asc' | 'desc'; sortKey: string },
): Promise<{ members: Member[]; count?: number }> => {
	const {
		notes,
		createdAtMax,
		createdAtMin,
		displayName,
		isFriends,
		isRegistered,
		lastVisitMin,
		lastVisitMax,
		memberSinceMax,
		memberSinceMin,
		currentPointsMin,
		currentPointsMax,
		messages,
		memberCode,
		friendAddedDateMin,
		friendAddedDateMax,
		memberId,
		studentType,
		classCategoryId,
		interviewOfficeCountMin,
		interviewOfficeCountMax,
		minimumFirstRoundInterviewsMin,
		minimumFirstRoundInterviewsMax,
		...queries
	} = filters;
	const customerRegistrationFilter: WhereAttributeHash<CustomerRegistration> = {
		isAdminDisplayed: true,
	};
	let customerRegistrations = await db.customerRegistrations.findAll({
		where: customerRegistrationFilter,
		order: [['showOrder', 'asc']],
		include: {
			association: db.customerRegistrations.associations.campaignChoices,
			attributes: ['campaignChoiceId', 'type'],
		},
		attributes: ['customerRegistrationId', 'type', 'name'],
	});

	customerRegistrations = customerRegistrations.map((customerRegistration) => customerRegistration.toJSON());

	const customerRegistrationsQueries = customerRegistrations.reduce((prev, currentValue) => {
		const { customerRegistrationId, type, campaignChoices, name } = currentValue as any;
		let condition;
		const key = `customerRegistrationId${customerRegistrationId}`;
		const value = get(queries, key);
		const isClass = name === 'class';

		if (type !== CUSTOMER_REGISTRATION_FIELD_TYPE.DATE_PICKER && (!value || isEmpty(value))) {
			return prev;
		}

		switch (type) {
			case CUSTOMER_REGISTRATION_FIELD_TYPE.TEXT:
			case CUSTOMER_REGISTRATION_FIELD_TYPE.NUMBER:
				condition = isClass ? { [Op.in]: value } : { [Op.like]: `%${value}%` };
				break;

			case CUSTOMER_REGISTRATION_FIELD_TYPE.CHECKBOX:
			case CUSTOMER_REGISTRATION_FIELD_TYPE.RADIO:
				condition = {
					[Op.or]: (value as string[]).map((val) => {
						if (val === CONTENT_TYPE_SURVEY_OTHER) {
							const campaignChoice = campaignChoices.find((it: any) => it.type === 'other');
							return literal(`JSON_CONTAINS(\`${key}\`, '{"checked": ${campaignChoice?.campaignChoiceId}}', '$')`);
						}
						return literal(`JSON_CONTAINS(\`${key}\`, '{"value": "${val}"}', '$')`);
					}),
				};
				break;

			case CUSTOMER_REGISTRATION_FIELD_TYPE.IMAGE:
				if (value === 'true') condition = { [Op.not]: null };
				if (value === 'false') condition = { [Op.is]: null };
				break;

			case CUSTOMER_REGISTRATION_FIELD_TYPE.DATE_PICKER: {
				const min = get(queries, `${key}Min`);
				const max = get(queries, `${key}Max`);

				if (min || max) {
					condition = {
						...(min ? { [Op.gte]: min } : {}),
						...(max ? { [Op.lte]: max } : {}),
					};
				}

				break;
			}

			default:
				break;
		}

		return {
			...prev,
			...(condition ? { [key]: condition } : {}),
		};
	}, {});

	const interviewOfficeCountSubquery = `(
		SELECT COUNT(DISTINCT o.categoryId)
		FROM ${DATABASE_TABLE_NAME.OCCASION_MEMBER} om
		INNER JOIN ${DATABASE_TABLE_NAME.OCCASION} o ON om.occasionId = o.occasionId
		WHERE om.memberId = Member.memberId
		AND om.interviewStatus != '${MEMBER_INTERVIEW_STATUS.INTERVIEW_PROPOSAL}'
	)`;

	const conditions: FindOptions<Member> = {
		...(pagination?.pp ? { limit: pagination.pp } : {}),
		...(pagination?.p ? { offset: (pagination.p - 1) * pagination.pp } : {}),
		order: buildOrderBySortKey(pagination),
		attributes: {
			exclude: ['updatedAt'],
			include: [
				...customerRegistrations.map(({ customerRegistrationId }) => `customerRegistrationId${customerRegistrationId}`),
				[literal(interviewOfficeCountSubquery), 'interviewOfficeCount'],
			],
		},
		group: ['Member.memberId'],
		having: buildHavingRangeCondition(interviewOfficeCountSubquery, interviewOfficeCountMin, interviewOfficeCountMax),
		where: {
			[Op.and]: [{ lineId: { [Op.not]: null } }],
			...(memberCode ? { memberCode } : {}),
			...(studentType ? { studentType } : {}),
			...(memberId ? { memberId: parseInt(memberId) } : {}),
			...generateWhereClauseBetween('createdAt', [createdAtMin, createdAtMax]),
			...generateWhereClauseBetween('friendAddedDate', [friendAddedDateMin, friendAddedDateMax]),
			...generateWhereClauseBetween('memberSince', [memberSinceMin, memberSinceMax]),
			...generateWhereClauseBetween('lastVisit', [lastVisitMin, lastVisitMax]),
			...generateWhereClauseBetween('currentPoints', [currentPointsMin, currentPointsMax]),
			...(displayName ? { displayName: { [Op.like]: `%${displayName}%` } } : {}),
			...(notes ? { notes: { [Op.like]: `%${notes}%` } } : {}),
			...(isFriends ? { isFriends: JSON.parse(isFriends) } : {}),
			...(isRegistered ? { isRegistered: JSON.parse(isRegistered) } : {}),
			// ...(lastVisitMax ? { lastVisit: { [Op.lte]: lastVisitMax } } : {}),
			// ...(lastVisitMin ? { lastVisit: { [Op.gte]: lastVisitMin } } : {}),
			...(messages ? { unreadCount: { [messages === 'read' ? Op.eq : Op.gt]: 0 } } : {}),
			...customerRegistrationsQueries,
		},
		include: [
			{
				model: db.memberClassModel,
				required: !!minimumFirstRoundInterviewsMin || !!minimumFirstRoundInterviewsMax || !!classCategoryId,
				where: {
					...(isNumberType(minimumFirstRoundInterviewsMin)
						? { minimumFirstRoundInterviews: { [Op.gte]: Number(minimumFirstRoundInterviewsMin) } }
						: {}),
					...(isNumberType(minimumFirstRoundInterviewsMax)
						? {
								minimumFirstRoundInterviews: {
									...(minimumFirstRoundInterviewsMin !== undefined
										? { [Op.gte]: Number(minimumFirstRoundInterviewsMin) }
										: {}),
									[Op.lte]: Number(minimumFirstRoundInterviewsMax),
								},
						  }
						: {}),
					...(isNumberType(classCategoryId) ? { classCategoryId: Number(classCategoryId) } : {}),
				},
				include: [
					{
						model: db.classCategoryModel,
						attributes: ['classCategoryId', 'name'],
					},
				],
			},
		],
	};

	const members = await db.members.findAll(conditions);

	if (!pagination) {
		return { members };
	}

	const countResult = await db.members.count({ ...conditions, distinct: true, col: 'memberId' });

	return { members, count: (countResult as any).length };
};

export function buildHavingRangeCondition(subQueryKey: string, min?: number, max?: number) {
	if (isNumberType(min) && isNumberType(max)) {
		return literal(`${subQueryKey} BETWEEN ${min} AND ${max}`);
	} else if (isNumberType(min)) {
		return literal(`${subQueryKey} >= ${min}`);
	} else if (isNumberType(max)) {
		return literal(`${subQueryKey} <= ${max}`);
	}
	return undefined;
}

export function buildOrderBySortKey(pagination?: { sortKey: string; sort: 'asc' | 'desc' }): Order {
	const sortKey = pagination?.sortKey || 'memberId';
	const sortOrder = pagination?.sort || 'desc';

	if (sortKey === 'classCategoryName') {
		return [
			[literal('`MemberClass->classCategory`.`name` IS NULL'), SORT.ASCENDING] as OrderItem,
			[Sequelize.col('MemberClass.classCategory.name'), sortOrder] as OrderItem,
		];
	}

	if (sortKey === 'minimumFirstRoundInterviews') {
		return [
			[literal('`MemberClass`.`minimumFirstRoundInterviews` IS NULL'), SORT.ASCENDING] as OrderItem,
			[Sequelize.col('MemberClass.minimumFirstRoundInterviews'), sortOrder] as OrderItem,
		];
	}

	if (/^customerRegistrationId\d+$/.test(sortKey)) {
		// 	const cleaned = `
		// 		REPLACE(
		// 			REPLACE(
		// 				REPLACE(
		// 					REPLACE(
		// 						REPLACE(
		// 							REPLACE(
		// 								REPLACE(
		// 									REGEXP_REPLACE(
		// 										JSON_UNQUOTE(\`${sortKey}\`),
		// 										'"checked":[0-9]+',
		// 										''
		// 									),
		// 									'"value":', ''
		// 								),
		// 								'{', ''
		// 							),
		// 							'}', ''
		// 						),
		// 						'[', ''
		// 					),
		// 					']', ''
		// 				),
		// 				'},{', ','
		// 			),
		// 			'"', ''
		// 		)
		// 	`;
		const cleaned = `
		CASE
			WHEN JSON_VALID(${sortKey})
			THEN (
				SELECT GROUP_CONCAT(JSON_UNQUOTE(JSON_EXTRACT(j.value, '$.value')))
				FROM JSON_TABLE(${sortKey}, '$[*]'
					COLUMNS (value JSON PATH '$')
				) AS j
			)
			ELSE ${sortKey}
		END
	`;

		return [
			[literal(`${cleaned} IS NULL OR ${cleaned} = ''`), SORT.ASCENDING] as OrderItem,
			[literal(cleaned), sortOrder] as OrderItem,
		];
	}

	if (sortKey === 'rollNumber') {
		return [
			[literal(`CAST(${sortKey} AS UNSIGNED) IS NULL`), SORT.ASCENDING] as OrderItem,
			[literal(`CAST(${sortKey} AS UNSIGNED)`), sortOrder] as OrderItem,
		];
	}

	return [[literal(`${sortKey} IS NULL`), SORT.ASCENDING] as OrderItem, [sortKey, sortOrder] as OrderItem];
}

export const listMembers = async (data: GetAllMemberSchema, transaction?: Transaction) => {
	let memberIds = [] as any;
	const { occasionId, textSearch, isRegistered } = data;
	let conditionSearch = { isRegistered } as WhereAttributeHash;
	let classCategoryId = null;
	let order = [] as any;
	if (occasionId) {
		conditionSearch = { ...conditionSearch, memberClassId: { [Op.not]: null } };
		const currentOccasion = await db.occasions.findByPk(occasionId, {
			include: [
				{
					model: db.classCategoryModel,
				},
			],
		});
		classCategoryId = currentOccasion?.classCategoryId;
		const occasionMembers = await db.occasionMembers.findAll({
			where: {
				occasionId,
			},
			include: [
				{
					model: db.members,
					required: true,
				},
			],
			attributes: ['memberId'],
		});
		memberIds = occasionMembers.map((occasionMember) => occasionMember.memberId);
		const membersAssignedToOffice = await db.registrations.findAll({
			where: {
				memberActionStatus: MEMBER_ACTIONS_STATUS.MEMBER_ACCEPTED,
			},
		});
		const memberIdsAssignedToOffice = membersAssignedToOffice.map(
			(memberAssignedToOffice) => memberAssignedToOffice.memberId,
		);
		memberIds = [...memberIds, ...memberIdsAssignedToOffice];
		if (memberIds.length > 0) {
			conditionSearch = { ...conditionSearch, memberId: { [Op.notIn]: memberIds } };
		}
		order = [[cast(col('rollNumber'), 'INTEGER'), 'ASC']];
	}
	if (textSearch) {
		conditionSearch = {
			...conditionSearch,
			[Op.or]: [{ rollNumber: { [Op.like]: `%${textSearch}%` } }, { fullName: { [Op.like]: `%${textSearch}%` } }],
		};
	}
	let includeMember = [] as Includeable;
	if (classCategoryId) {
		includeMember = [
			{
				model: db.memberClassModel,
				where: {
					classCategoryId,
				},
			},
		] as Includeable;
	}
	return db.members.findAll({
		where: conditionSearch,
		attributes: { exclude: ['lineId'] },
		include: includeMember,
		order,
		transaction,
	});
};

export const createMember = async (params: CreationAttributes<Member>, transaction?: Transaction) =>
	db.members.create(params, { transaction });

export async function updateMember(
	{ memberId, params, filesRequest }: { memberId: number; params: UpdateMember; filesRequest: any },
	transaction?: Transaction,
) {
	const [memberData] = await db.sequelize.query<{ [key: string]: any }>('SELECT * FROM members WHERE memberId = :id', {
		replacements: { id: memberId },
		type: QueryTypes.SELECT,
		transaction,
	});
	const member = Member.build(memberData as any, { isNewRecord: false });
	if (!member) {
		throw new AppError(SYSTEM_ERROR, 'invalid member', false);
	}
	if (!_.isNil(params.activeUntil) && moment(params.activeUntil).isValid()) {
		member.set({ activeUntil: params.activeUntil });
	}
	if (!_.isNil(params.pointIsAdd) && _.isNumber(params.points)) {
		const newPoints = member.addOrDeductPoint(params.points, params.pointIsAdd);
		if (newPoints < 0) {
			throw new AppError(SYSTEM_ERROR, 'total point amount cannot be negative');
		}
		member.set({ currentPoints: newPoints });
	}
	if (!_.isNil(params.notes)) {
		member.set({ notes: params.notes });
	}
	const filesDelete = [] as string[];
	if (CommonUtils.isTrue(params?.isEditMember)) {
		const customerRegistrations = await db.customerRegistrations.findAll({
			where: {
				isAdminDisplayed: true,
				studentType: member.studentType,
			},
			include: {
				association: db.customerRegistrations.associations.campaignChoices,
				attributes: { exclude: ['customerRegistrationId', 'campaignQuestionId'] },
			},
			order: [['showOrder', 'asc']],
		});
		const dataMember: any = {};

		customerRegistrations.forEach((item) => {
			const column = `customerRegistrationId${item?.customerRegistrationId}`;
			const data = _.get(params, column);
			const customerRegistrationName = item.name || '';

			if (item?.required) {
				if (!data) {
					throw new AppError(BAD_REQUEST, `${item.label} not null`, false);
				}
			}

			const setData = (data: any) => {
				_.set(dataMember, column, data);
			};

			if (customerRegistrationName === CUSTOMER_REGISTRATION_NAME.ROLL_NUMBER) {
				dataMember.rollNumber = data?.trim() || null;
			}

			if (
				[
					CUSTOMER_REGISTRATION_NAME.REPRESENTATIVE_NAME,
					CUSTOMER_REGISTRATION_NAME.FULL_NAME_ENROLLED_STUDENT,
				].includes(customerRegistrationName)
			) {
				dataMember.fullName = data?.trim() || null;
			}

			if (customerRegistrationName === CUSTOMER_REGISTRATION_NAME.REPRESENTATIVE_NAME) {
				dataMember[CUSTOMER_REGISTRATION_NAME.REPRESENTATIVE_NAME] = data?.trim() || null;
			}

			if (customerRegistrationName === CUSTOMER_REGISTRATION_NAME.UNIVERSITY_STUDENT_EMAIL) {
				dataMember[CUSTOMER_REGISTRATION_NAME.UNIVERSITY_STUDENT_EMAIL] = data?.trim() || null;
			}

			switch (item.type) {
				case CUSTOMER_REGISTRATION_FIELD_TYPE.ADDRESS:
					setData(data);
					break;

				case CUSTOMER_REGISTRATION_FIELD_TYPE.TEXT:
					setData(data?.trim() || null);
					break;
				case CUSTOMER_REGISTRATION_FIELD_TYPE.NUMBER:
					if (data || data === 0) {
						const valueNumber = data;

						if (!valueNumber || isNaN(valueNumber)) {
							throw new AppError(BAD_REQUEST, 'validate number', false);
						}
						setData(valueNumber);
						if (
							[CUSTOMER_REGISTRATION_NAME.CLASS, CUSTOMER_REGISTRATION_NAME.CLASS_UNIVERSITY].includes(
								customerRegistrationName,
							)
						) {
							dataMember.memberClassId = valueNumber || null;
							member.dataValues['memberClassId'] = valueNumber || null;
						}
					} else {
						setData(null);
					}

					break;
				case CUSTOMER_REGISTRATION_FIELD_TYPE.DATE_PICKER:
					if (data) {
						const isValid = !isNaN(new Date(data).getTime());
						if (!isValid) {
							throw new AppError(BAD_REQUEST, 'validate datepicker', false);
						}
					}

					setData(data || null);
					break;
				case CUSTOMER_REGISTRATION_FIELD_TYPE.CHECKBOX: {
					if (data && data !== 'undefined') {
						const dataParsed = JSON.parse(data);
						const dataMap = _.keyBy(dataParsed, 'checked');

						const selectCheckbox = item.campaignChoices?.filter((item) => dataMap[item.campaignChoiceId]);

						if (item?.required && !selectCheckbox?.length) {
							throw new AppError(BAD_REQUEST, 'validate checkbox', false);
						}

						const campaignChoiceContents = (selectCheckbox || [])
							.map((r) => r.toJSON())
							.map(({ campaignChoiceId, contents, type }) => ({
								checked: campaignChoiceId,
								value: type === CAMPAIGN_CHOICES_TYPE_OTHER ? _.get(dataMap, campaignChoiceId).value : contents,
							}));

						setData(campaignChoiceContents.length ? JSON.stringify(campaignChoiceContents) : null);
					}
					break;
				}
				case CUSTOMER_REGISTRATION_FIELD_TYPE.RADIO: {
					if (data && data !== 'undefined') {
						const dataParsed = JSON.parse(data);
						const campaignChoiceId = _.get(dataParsed, 'checked');

						const selectRadio: any = item.campaignChoices?.find(
							(item) => item.campaignChoiceId === Number(campaignChoiceId),
						);

						if (item?.required && !selectRadio) {
							throw new AppError(BAD_REQUEST, 'validate radio', false);
						}

						if (customerRegistrationName === CUSTOMER_REGISTRATION_NAME.ENTRY_CITY) {
							dataMember.entryCity = JSON.stringify({
								checked: campaignChoiceId,
								value: selectRadio.type === 'other' ? get(dataParsed, 'value') : get(selectRadio, 'contents'),
							});
						}

						setData(
							JSON.stringify({
								checked: campaignChoiceId,
								value:
									selectRadio?.type === CAMPAIGN_CHOICES_TYPE_OTHER
										? _.get(dataParsed, 'value')
										: _.get(selectRadio, 'contents'),
							}),
						);
					}
					break;
				}
				case CUSTOMER_REGISTRATION_FIELD_TYPE.IMAGE:
					const nameImageOld = memberData[column];

					if (item?.required) {
						if (!Array.isArray(filesRequest)) {
							throw new AppError(BAD_REQUEST, 'validate date', false);
						}
						const name = data;
						const files = filesRequest.find((item) => item?.originalname === name);

						if (!files || !name) {
							throw new AppError(BAD_REQUEST, 'validate date', false);
						}

						if (customerRegistrationName === CUSTOMER_REGISTRATION_NAME.IMAGE_PROFILE) {
							dataMember.imageProfile = files.filename;
							member.dataValues['imageProfile'] = files.filename;
						}

						setData(files.filename);
					} else {
						let files: any;
						if (Array.isArray(filesRequest)) {
							const name = data;
							if (name) {
								files = filesRequest.find((item) => item?.originalname === name);
							}
						}
						if (customerRegistrationName === CUSTOMER_REGISTRATION_NAME.IMAGE_PROFILE) {
							dataMember.imageProfile = files ? files.filename : null;
							member.dataValues['imageProfile'] = files ? files.filename : null;
						}
						setData(files ? files.filename : null);
					}
					if (nameImageOld) {
						filesDelete.push(nameImageOld);
					}
					break;
				default:
					throw new AppError(SYSTEM_ERROR, 'data error', false);
			}
		});

		const listKeys = Object.keys(member.dataValues);

		if (!_.isEmpty(dataMember)) {
			let updateQuery = 'UPDATE members SET';
			Object.keys(dataMember)?.forEach((item, index) => {
				if (listKeys.includes(item)) {
					member.set(item as any, dataMember[item]);
				}
				if (Object.keys(dataMember)?.length === index + 1) {
					updateQuery += ` ${item} = :${item}`;
					return;
				}
				updateQuery += ` ${item} = :${item},`;
			});
			updateQuery += ' WHERE memberId = :memberId';

			await db.sequelize.query(updateQuery, {
				replacements: { ...dataMember, memberId: member.memberId },
				type: QueryTypes.UPDATE,
				transaction,
			});
		}
	}

	member.set(
		_.pickBy(_.pick(params, ['firstName', 'lastName', 'companyName', 'telephone', 'address']), (value) =>
			_.isUndefined(value),
		),
	);
	await member.save({ transaction });
	deleteFilesOldLocal(filesDelete);
	return member;
}

export const deleteMember = async (memberId: number, transaction?: Transaction) => {
	const imageFilesToDelete: string[] = [];
	let registrationIds: number[] = [];
	const modelAttributes = Object.keys(db.members.rawAttributes);
	const customerRegistrations = await db.customerRegistrations.findAll({
		where: {
			isDisplayed: true,
			type: 'image',
		},
	});
	const attributes = customerRegistrations.map((item) => {
		return `customerRegistrationId${item?.customerRegistrationId}`;
	});
	const allAttributes = [...modelAttributes, ...attributes];
	return db.members
		.findByPk(memberId, {
			include: [
				{
					association: db.members.associations.chats,
				},
				{
					association: db.members.associations.registrations,
					required: false,
					paranoid: false,
				},
			],
			attributes: allAttributes,
			transaction,
		})
		.then((member) => {
			if (member == null) {
				throw new AppError(SYSTEM_ERROR, `member ${memberId} not exist`, false);
			}
			attributes.forEach((e) => {
				const imagesOld = (member.dataValues as { [key: string]: any })[e];
				if (imagesOld) {
					imageFilesToDelete.push(imagesOld);
				}
			});
			return member;
		})
		.then(async (member) => {
			if (member.registrations && member.registrations.length > 0) {
				registrationIds = member.registrations.map((r) => r.registrationId);
				await removeRegistrationPersonalInfos({ registrationIds }, transaction);
			}
			if (member.curRM !== RICH_MENU_TYPE.DEFAULT && member.lineId) {
				await unlinkRichMenuFromUser({ userId: member.lineId });
			}
			const memberIds = [member.memberId];
			await MemberService.updateMembersAfterRemoveMemberClass(memberIds, transaction);
			await RegistrationService.updateRegistrationsAfterRemoveMemberClass(memberIds, transaction);
			await OccasionMemberService.updateOccasionMemberAfterRemoveMemberClass(memberIds, transaction);
			return member;
		})
		.then((member) => Promise.all([member.destroy({ transaction }), removeMemberAndKidsFiles(imageFilesToDelete)]));
};

const removeRegistrationPersonalInfos = async (
	{ registrationIds }: { registrationIds: number[] },
	transaction?: Transaction,
) =>
	db.registrations.update(
		{ memberId: null },
		{
			where: { registrationId: { [Op.in]: registrationIds } },
			paranoid: false,
			transaction,
		},
	);

const removeMemberAndKidsFiles = async (imageFileNames: string[]) =>
	Promise.all(
		imageFileNames.map((imageFileName) =>
			FileOps.deleteFile(path.join(systemConfig.PATH_FILE_UPLOAD_MEMBER, imageFileName)),
		),
	);

export const setRichmenuOfMember = async (
	{ member, type }: { member: Member; type: richmenuType },
	transaction?: Transaction,
) => {
	const memberRM = await db.richmenus.findOne({ where: { type: type, isDisplayed: true }, transaction });
	if (memberRM != null && memberRM.isDisplayed == true && memberRM.richMenuId) {
		await linkRichMenuToUser({ userId: member.lineId as string, richmenuId: memberRM.richMenuId });
	}
};

function safeNumber(val: any) {
	return val !== undefined && val !== null && val !== '' ? Number(val) : val;
}

export const getMemberCsvData = async (
	memberWhere: Parameters<typeof browseMembersHandler>[0],
	pagination?: paginationParams,
): Promise<ReturnType<typeof json2csv.parse>> => {
	const customerRegistrations = await db.customerRegistrations.findAll({
		where: [
			{
				isAdminDisplayed: true,
				studentType: memberWhere.studentType,
			},
		],
		attributes: ['customerRegistrationId', 'type', 'label', 'name'],
		include: [{ association: db.customerRegistrations.associations.campaignChoices }],
		order: [['showOrder', 'asc']],
	});

	const { members } = await browseMembersHandler(memberWhere, pagination);
	const classesMapByClassId: Record<string, string> = {};
	const classCategoryNameById: Record<string, string> = {};
	const minimumFirstRoundInterviewsByClassId: Record<string, number> = {};
	if (memberWhere.studentType === STUDENT_TYPE.ENROLLED_STUDENT) {
		const classes = await db.memberClassModel.findAll({
			include: {
				association: db.memberClassModel.associations.classCategory,
				attributes: ['name'],
			},
			attributes: ['memberClassId', 'className', 'minimumFirstRoundInterviews'],
			raw: true,
		});

		for (const cls of classes) {
			const memberClassId = cls.memberClassId;
			const className = cls.className;
			const minimumFirstRoundInterviews = cls.minimumFirstRoundInterviews;
			const classCategoryName = (cls as any)['classCategory.name'];

			classesMapByClassId[memberClassId] = className;
			minimumFirstRoundInterviewsByClassId[memberClassId] = minimumFirstRoundInterviews;
			classCategoryNameById[memberClassId] = classCategoryName;
		}
	}

	const dataCategories = await db.categories.findAll({
		where: {
			deletedAt: null,
		},
		include: {
			association: db.categories.associations.occasions,
			attributes: ['occasionId'],
			where: {
				deletedAt: null,
			},
			required: false,
		},
		attributes: ['categoryId', 'title'],
	});

	const listIdOccasions = [] as number[];
	const listTitleOffice = [] as string[];

	dataCategories.forEach((e: any) => {
		const { title, occasions } = e;
		occasions.forEach((item: Occasion) => {
			listIdOccasions.push(item.occasionId);
		});
		listTitleOffice.push(title);
	});

	const memberIds = members
		.map((member) => member.toJSON())
		.map((member) => {
			return member.memberId;
		});

	const occasionMembers = await db.occasionMembers.findAll({
		where: {
			memberId: { [Op.in]: memberIds },
			occasionId: { [Op.in]: listIdOccasions },
		},
		include: [
			{
				model: db.occasions,
				as: 'Occasion',
				attributes: ['occasionId'],
				include: [
					{
						model: db.categories,
						as: 'Category',
						attributes: ['title'],
					},
				],
			},
		],
		attributes: ['occasionId', 'memberId', 'occurrenceIdsRejected', 'interviewStatus', 'memberInterviewCurrentStep'],
	});

	const listIdMemberHaveOffficeSendInterview: Record<string, string[]> = {};
	const listIdCategoryHaveMemberInterview = listTitleOffice.reduce((acc, title) => {
		acc[title] = [];
		return acc;
	}, {} as Record<string, number[]>);

	const membersInterviewNOT_ACCEPTED: Record<string, number> = {};
	const membersInterviewDECLINED: Record<string, number> = {};
	occasionMembers.forEach((member: OccasionMemberData) => {
		const { memberId, interviewStatus, memberInterviewCurrentStep } = member;
		const categoryTitle = member.Occasion?.Category?.title || '';

		// Xử lý đếm trạng thái phỏng vấn cho bước phỏng vấn đầu tiên
		if (memberInterviewCurrentStep === INTERVIEW_STEP.FIRST_INTERVIEW) {
			if (
				interviewStatus === MEMBER_INTERVIEW_STATUS.NOT_ACCEPTED ||
				interviewStatus === MEMBER_INTERVIEW_STATUS.AUTO_DECLINED
			) {
				incrementCounter(membersInterviewNOT_ACCEPTED, memberId);
			} else if (interviewStatus === MEMBER_INTERVIEW_STATUS.DECLINED) {
				incrementCounter(membersInterviewDECLINED, memberId);
			}
		}

		if (
			!(
				memberInterviewCurrentStep === INTERVIEW_STEP.FIRST_INTERVIEW &&
				interviewStatus === MEMBER_INTERVIEW_STATUS.INTERVIEW_PROPOSAL
			)
		) {
			// Xử lý danh sách danh mục có thành viên phỏng vấn
			if (listIdCategoryHaveMemberInterview[categoryTitle]) {
				addUniqueItem(listIdCategoryHaveMemberInterview[categoryTitle], memberId);
			} else {
				listIdCategoryHaveMemberInterview[categoryTitle] = [memberId];
			}
			// Xử lý danh sách thành viên có văn phòng gửi phỏng vấn

			if (listIdMemberHaveOffficeSendInterview[memberId]) {
				addUniqueItem(listIdMemberHaveOffficeSendInterview[memberId], categoryTitle);
			} else {
				listIdMemberHaveOffficeSendInterview[memberId] = [categoryTitle];
			}
		}
	});
	const fieldKeys = JSON.parse(JSON.stringify(fieldKeysCsv));
	const validNames = [
		CUSTOMER_REGISTRATION_NAME.FULL_NAME_ENROLLED_STUDENT,
		CUSTOMER_REGISTRATION_NAME.ROLL_NUMBER,
		CUSTOMER_REGISTRATION_NAME.IMAGE_PROFILE,
		CUSTOMER_REGISTRATION_NAME.VOCAL_DEMO,
		CUSTOMER_REGISTRATION_NAME.CLASS,
	];
	customerRegistrations.forEach((item) => {
		const { name, label } = item.dataValues as CustomerRegistration;
		if (!!name && validNames.includes(name)) {
			fieldKeys[name].label = label;
		}
	});

	const {
		fullNameEnrolledStudent,
		rollNumber,
		imageProfile,
		vocalDemo,
		class: classGet,
		participationYear,
	} = fieldKeys;
	const allowedLabels = [
		{
			label: fullNameEnrolledStudent.label,
			value: fullNameEnrolledStudent.value,
			checkIsGet: false,
		},
		{
			label: rollNumber.label,
			value: rollNumber.value,
			checkIsGet: false,
		},
		{
			label: imageProfile.label,
			value: imageProfile.value,
			checkIsGet: false,
		},
		{
			label: vocalDemo.label,
			value: vocalDemo.value,
			checkIsGet: false,
		},
		{
			label: classGet.label,
			value: classGet.value,
			checkIsGet: false,
		},
		{
			label: participationYear.label,
			value: participationYear.value,
			checkIsGet: false,
		},
	];

	const csvData = members
		.map((member) => member.toJSON())
		.map((member) => {
			const friendAddedDate = member.friendAddedDate || member.createdAt;
			return transformUserCsvDataMember(
				member as Member,
				customerRegistrations,
				{
					memberId: safeNumber(member.memberId),
					memberCode: member.memberCode ?? '',
					lineName: member.displayName ?? '',
					memberRegisterDate: formatDate(member?.memberSince),
					note: get(member, 'notes', '') as string,
					friendRegisterDate: formatDate(friendAddedDate),
				},
				classesMapByClassId,
				classCategoryNameById,
				minimumFirstRoundInterviewsByClassId,
				membersInterviewNOT_ACCEPTED,
				membersInterviewDECLINED,
				listIdMemberHaveOffficeSendInterview,
				listIdCategoryHaveMemberInterview,
				allowedLabels,
				fieldKeys,
			);
		});

	const dynamicFields = [] as ItemDynamicField[];
	customerRegistrations.forEach((cR, idx) => {
		const label = cR?.label || 'Unknown';
		const safeKey = `dynamic_${idx}`;
		const data = {
			label: label,
			value: safeKey,
		};
		dynamicFields.push(data);
	});
	const fieldDatasRaw = [
		fieldKeys.memberId,
		fieldKeys.memberCode,
		fieldKeys.lineName,
		fieldKeys.category,
		fieldKeys.fullNameEnrolledStudent,
		fieldKeys.rollNumber,
		fieldKeys.participationYear,
		fieldKeys.imageProfile,
		fieldKeys.class,
		fieldKeys.vocalDemo,
		...dynamicFields,
		fieldKeys.interviewRequestCount,
		fieldKeys.minInterviewCount,
		fieldKeys.minInterviewDiff,
		fieldKeys.firstInterviewSelfCancel,
		fieldKeys.firstInterviewExpired,
	];

	const tailFields = [
		fieldKeys.note,
		fieldKeys.friendRegisterDate,
		fieldKeys.memberRegisterDate,
		...listTitleOffice.map((title, idx) => ({
			label: title,
			value: `officeTitle_${idx}`,
		})),
	];
	const fieldDatas = [...fieldDatasRaw, ...tailFields];

	allowedLabels.forEach((target: ItemDynamicField) => {
		const duplicatedIndex = fieldDatas
			.map((item, index) => (item.label === target.label ? index : -1))
			.filter((index) => index !== -1)[1];
		if (duplicatedIndex !== undefined) {
			fieldDatas.splice(duplicatedIndex, 1);
		}
	});

	const opts = { fields: fieldDatas, withBOM: true, excelStrings: false };

	const csv = json2csv.parse(csvData, opts);

	return csv;
};

export const browseSurveyAnswerHistoriesHandler = async (queries: {
	memberId: number;
	pagination: paginationParams;
}): Promise<{
	count: number;
	rows: MemberSurveyReward[];
}> => {
	const { memberId, pagination } = queries;

	const sharedFindOptions: FindOptions<MemberSurveyReward> = {
		where: {
			memberId,
		},
		...buildPaginationParams(pagination),
	};

	const rows = await db.memberSurveyRewardModel.findAll({
		// raw: true,
		include: [
			{
				model: db.surveys,
				attributes: [],
			},
		],
		attributes: [
			//
			['memberSurveyRewardId', 'id'],
			'surveyId',
			'surveyRewardCode',
			[Sequelize.literal('Survey.svname'), 'surveyName'],
		],
		...sharedFindOptions,
	});

	const count = await db.memberSurveyRewardModel.count({
		...sharedFindOptions,
	});

	return {
		count,
		rows,
	};
};

export const browseLotteryDrawHistoriesHandler = async (queries: {
	memberId: number;
	pagination: paginationParams;
}): Promise<{
	count: number;
	rows: DrawModel[];
}> => {
	const { memberId, pagination } = queries;

	const sharedFindOptions: FindOptions<DrawModel> = {
		where: {
			customerId: memberId,
		},
		include: [
			{
				model: db.lotteries,
				attributes: [],
			},
			{
				model: db.lotteryPrizes,
				attributes: [],
				include: [
					{
						model: db.coupons,
					},
				],
			},
		],
		...buildPaginationParams(pagination),
	};

	const rows = await db.draws.findAll({
		...sharedFindOptions,
		attributes: [
			//
			'drawId',
			[Sequelize.col('`Lottery`.`title`'), 'lotteryName'],
			[Sequelize.col('`LotteryPrize`.`name`'), 'prizeName'],
			[Sequelize.col('`LotteryPrize->Coupon`.`title`'), 'couponName'],
		],
	});

	const count = await db.draws.count(sharedFindOptions);

	return { rows, count };
};

export const updateCustomerRegistrationByMemberById = async (
	memberId: number,
	dataUpdate: Record<string, string>,
	transaction: Transaction | undefined,
) => {
	let updateQuery = 'UPDATE members SET';
	Object.keys(dataUpdate)?.forEach((item, index) => {
		if (Object.keys(dataUpdate)?.length === index + 1) {
			updateQuery += ` ${item} = :${item}`;
			return;
		}
		updateQuery += ` ${item} = :${item},`;
	});
	updateQuery += ' WHERE memberId = :memberId';

	return db.sequelize.query(updateQuery, {
		replacements: { ...dataUpdate, memberId },
		type: QueryTypes.UPDATE,
		transaction,
	});
};

export const interviewHistoriesHandler = async (filters: GetInterviewHistoriesByMemberSchema) => {
	const { memberId } = filters;

	return db.occasionMembers.findAll({
		where: {
			memberId,
			interviewStatus: {
				[Op.notIn]: [MEMBER_INTERVIEW_STATUS.INTERVIEW_PROPOSAL],
			},
		},
		include: [
			{
				association: new HasOne(db.occasionMembers, db.registrations, {
					foreignKey: 'occasionId',
				}),
				on: literal(
					'`OccasionMember`.`occasionId` = `Registration`.`occasionId` AND `OccasionMember`.`memberId` = `Registration`.`memberId` AND cancelledAt IS NULL',
				),
				include: [
					{
						model: db.occurrenceDetailModel,
						attributes: [],
						include: [
							{
								model: db.occurrences,
								attributes: ['responseDeadlineDate'],
							},
						],
					},
				],
				attributes: [],
				required: false,
				where: {
					createdAt: {
						[Op.eq]: Sequelize.literal(`(
							SELECT MAX(createdAt)
							FROM registrations AS r2
							WHERE r2.occasionId = Registration.occasionId
							AND r2.memberId = Registration.memberId
						)`),
					},
				},
			},
			{
				model: db.occasions,
				required: true,
				attributes: [],
				include: [
					{
						model: db.categories,
						attributes: [],
					},
					{
						model: db.classCategoryModel,
					},
					{
						model: db.occurrences,
						required: true,
						where: Sequelize.where(
							col('`Occasion->occurrences`.`interviewStep`'),
							col('OccasionMember.memberInterviewCurrentStep'),
						),
					},
				],
			},
		],
		raw: true,
		attributes: [
			[literal('DISTINCT `OccasionMember`.`occasionMemberId`'), 'id'],
			[literal('`OccasionMember`.`interviewStatus`'), 'interviewStatus'],
			[literal('`Registration`.`memberActionStatus`'), 'memberActionStatus'],
			[literal('`OccasionMember`.`memberInterviewCurrentStep`'), 'step'],
			[literal('`Occasion->Category`.`title`'), 'officeName'],
			[literal('`Occasion->Category`.`categoryId`'), 'categoryId'],
			[literal('`Occasion`.`title`'), 'name'],
			[literal('`Occasion`.`occasionId`'), 'occasionId'],
			[literal('`Registration`.`cancelledAt`'), 'cancelledAt'],
			[
				literal(
					// eslint-disable-next-line quotes
					"CASE WHEN `Registration`.`cancelledAt` IS NULL AND `Registration`.`attended` = 0 THEN CONCAT(`Registration->occurrenceDetail`.`eventDay`, ' ', `Registration->occurrenceDetail`.`startTime`, '～', `Registration->occurrenceDetail`.`endTime`) ELSE NULL END",
				),
				'schedule',
			],
			[literal('`Registration->occurrenceDetail`.`interviewType`'), 'interviewType'],
			[literal('`Registration->occurrenceDetail`.`occurrenceDetailId`'), 'occurrenceDetailId'],
			[literal('`Registration->occurrenceDetail`.`interviewLocation`'), 'interviewLocation'],
			[literal('`Registration->occurrenceDetail`.`notes`'), 'occurrenceDetailNotes'],
			[literal('`OccasionMember`.`notes`'), 'occasionMemberNotes'],
			[literal('`Occasion->occurrences`.`responseDeadlineDate`'), 'responseDeadlineDate'],
			[literal('`Registration`.`responseDeadlineDatePassInterview`'), 'responseDeadlineDatePassInterview'],
		],
		group: [
			'`OccasionMember`.`occasionMemberId`',
			'`OccasionMember`.`interviewStatus`',
			'`Registration`.`memberActionStatus`',
			'`OccasionMember`.`memberInterviewCurrentStep`',
			'`Occasion->Category`.`title`',
			'`Occasion->Category`.`categoryId`',
			'`Occasion`.`title`',
			'`Occasion`.`occasionId`',
			'Registration->occurrenceDetail->occurrence.occurrenceId',
			'`Registration`.`cancelledAt`',
			'`Registration->occurrenceDetail`.`eventDay`',
			'`Registration->occurrenceDetail`.`occurrenceDetailId`',
			'`Registration->occurrenceDetail`.`startTime`',
			'`Registration->occurrenceDetail`.`endTime`',
			'`Registration->occurrenceDetail`.`interviewType`',
			'`Registration->occurrenceDetail`.`interviewLocation`',
			'`Registration->occurrenceDetail`.`notes`',
			'`OccasionMember`.`notes`',
			'`Registration->occurrenceDetail->occurrence`.`responseDeadlineDate`',
			'`Registration`.`responseDeadlineDatePassInterview`',
			'`Registration`.`attended`',
			'`Occasion->occurrences`.`responseDeadlineDate`',
			'`Occasion->occurrences`.`occurrenceId`',
		],
	});
};

export const getMembersByMemberClassId = (memberClassId: number) => db.members.findAll({ where: { memberClassId } });

export const updateMembersAfterRemoveMemberClass = async (memberIds: number[], transaction?: Transaction) => {
	const customerRegistration = await db.customerRegistrations.findOne({
		where: {
			name: CUSTOMER_REGISTRATION_NAME.CLASS,
		},
	});
	if (!customerRegistration) {
		throw new AppError(BAD_REQUEST, 'class customer registration is not found.', false);
	}
	const customerRegistrationId = `customerRegistrationId${customerRegistration.customerRegistrationId}`;
	const updateQuery = `
		UPDATE ${DATABASE_TABLE_NAME.MEMBERS}
		SET memberClassId             = NULL,
				${customerRegistrationId} = NULL
		WHERE memberId IN (:memberIds)
	`;

	await db.sequelize.query(updateQuery, {
		replacements: { memberIds },
		type: QueryTypes.UPDATE,
		transaction,
	});
	return true;
};

export const countMembersByMemberClassId = (memberClassId: number) => db.members.count({ where: { memberClassId } });

import fs from 'fs/promises';
import path from 'path';
import { writeLog } from '~utilities';
import { systemConfig } from '../config';

const deleteFilesOldLocal = async (files: string[]) => {
	if (!Array.isArray(files) || files.length === 0) return;

	for (const file of files) {
		const filePath = path.join(systemConfig.PATH_FILE_UPLOAD_MEMBER, file);
		try {
			await fs.unlink(filePath);
		} catch (err: any) {
			if (err.code === 'ENOENT') {
				writeLog(`⚠️ File not found (already deleted or never existed): ${file}`, 'error');
			} else {
				writeLog(`❌ Error deleting file ${file}: ${err.message}`, 'error');
			}
		}
	}
};

export default deleteFilesOldLocal;
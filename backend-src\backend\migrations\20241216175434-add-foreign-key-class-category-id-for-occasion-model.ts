import { DATABASE_TABLE_NAME } from '../config';
import { SequelizeUtility } from '../utilities';

import type { Migration } from 'sequelize-cli';

module.exports = {
	async up(queryInterface, Sequelize) {
		if (await SequelizeUtility.columnNotExists(queryInterface, DATABASE_TABLE_NAME.OCCASION, 'classCategoryId')) {
			await queryInterface.addColumn(DATABASE_TABLE_NAME.OCCASION, 'classCategoryId', {
				type: Sequelize.INTEGER({ unsigned: true }),
				allowNull: true,
				references: {
					model: DATABASE_TABLE_NAME.CLASS_CATEGORY,
					key: 'classCategoryId',
				},
			});
		}
	},

	async down(queryInterface) {
		if (await SequelizeUtility.columnExists(queryInterface, DATABASE_TABLE_NAME.OCCASION, 'classCategoryId')) {
			await queryInterface.removeColumn(DATABASE_TABLE_NAME.OCCASION, 'classCategoryId');
		}
	},
} satisfies Migration;

import { literal, Op, Transaction, where } from 'sequelize';
import { db } from '../models';
import { Manager } from '../models/managerModel';
import { GetManagerListSchema } from '~schemas/manager';

export const getManager = async (managerId: number) => db.managers.findByPk(managerId);
export const findManagerByUsername = async (username: string, transaction?: Transaction) =>
	db.managers.findOne({ where: where(literal('BINARY `username`'), username), transaction });

export const createNewManager = async (newData: Manager) => db.managers.create(newData);

export const removeManagerByUserName = async (username: string, transaction?: Transaction) =>
	db.managers.destroy({ where: { username }, transaction });

export const updateManagerByUserName = async (username: string, newData: Partial<Manager>, transaction?: Transaction) =>
	db.managers.update(newData, { where: { username }, transaction });

export const findManagerByEmail = async (email: string, transaction?: Transaction) =>
	db.managers.findOne({ where: { recoveryMail: email }, transaction });

export const getManagerList = async ({ page, pageSize }: GetManagerListSchema) => {
	const currentPage = parseInt(page) || 1;
	const limit = parseInt(pageSize) || 10;
	const offset = (currentPage - 1) * limit;
	const totalMemberClass = await db.managers.count();

	const memberClasses = await db.managers.findAll({
		limit,
		offset,
		order: [['managerId', 'desc']],
	});
	return {
		total: totalMemberClass,
		list: memberClasses,
		page: currentPage,
		pageSize: limit,
	};
};

export const existingManagerEmailContact = async (emailContact: string, managerId?: number) =>
	db.managers.count({
		where: {
			emailContact: { [Op.like]: `%${emailContact}%` },
			...(managerId ? { managerId: { [Op.ne]: managerId } } : {}),
		},
	});

export const updateManagerById = async (managerId: number, newData: Partial<Manager>, transaction?: Transaction) =>
	db.managers.update(newData, { where: { managerId }, transaction });

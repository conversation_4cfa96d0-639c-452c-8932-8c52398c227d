import moment = require('moment');
import { cast, col, fn, <PERSON>One, Op, Sequelize, Transaction, WhereAttributeHash } from 'sequelize';
import { BAD_REQUEST, CUSTOM_SHOW_MESSAGE, INTERVIEW_STEP, SYSTEM_ERROR, TIME_FORMAT } from '../config';
import { db } from '../models';
import { AppError, DATE_FORMAT_TYPE, getDateNowWithTimezone, writeLog } from '../utilities';
import _, { isEmpty, omit } from 'lodash';
import { redisCacheService } from '~services/redisCacheService';
import { CreateOccurrenceSchema, UpdateOccurrenceDataDetailSchema, UpdateOccurrenceSchema } from '~schemas/occurrences';
import { OccurrenceDetail } from '~models/occurrenceDetailModel';
import { OccurrenceDetailService } from '~services';

export const occurrenceDetail = async (occurrenceId: number, transaction?: Transaction) => {
	const occurrence = await db.occurrences.findByPk(occurrenceId, {
		attributes: {
			exclude: ['createdAt', 'updatedAt', 'deletedAt'],
		},
		include: [
			{
				association: db.occurrences.associations.registrations,
				attributes: {
					exclude: ['isRegistered', 'isFriends', 'isNotified1', 'isNotified2', 'updatedAt', 'deletedAt'],
					include: [
						[Sequelize.literal('`registrations->Transaction`.`order_id`'), 'paymentOrderId'],
						[Sequelize.literal('`registrations->Transaction`.`amount`'), 'paymentAmount'],
						[Sequelize.literal('`registrations->Transaction`.`status`'), 'paymentStatus'],
						[Sequelize.literal('`registrations->Transaction`.`type`'), 'paymentType'],
					],
				},
				include: [
					{
						association: db.registrations.associations.Member,
						attributes: [
							'customerRegistrationId1',
							'customerRegistrationId2',
							'memberId',
							'memberCode',
							'displayName',
							'picUrl',
							'memberSince',
							'curRM',
							'isCampaign',
							'candidateAt',
							'isRegistered',
							'isFriends',
							'unreadCount',
							'createdAt',
							'updatedAt',
							'building',
							'address',
							'postalCode',
							'telephone',
							'email',
							'lastNameKana',
							'firstNameKana',
							'lastName',
							'firstName',
						],
						include: [
							{
								separate: true,
								association: db.members.associations.campaignAnswers,
								attributes: { exclude: ['memberId', 'campaignQuestionId'] },
							},
						],
						required: false,
					},
					{
						attributes: [],
						association: new HasOne(db.registrations, db.transaction, {
							foreignKey: 'occurrenceId',
						}),
						where: {
							status: 'FULFILLED',
							type: 'purchase',
						},
						required: false,
						on: Sequelize.literal(
							'`registrations`.`occurrenceId` = `registrations->Transaction`.`occurrence_id` AND `registrations`.`memberId` = `registrations->Transaction`.member_id',
						),
					},
				],
			},
			{
				association: db.occurrences.associations.Category,
				attributes: ['groupBooking', 'fee'],
			},
			{
				association: db.occurrences.associations.Occasion,
				attributes: ['groupBooking', 'fee'],
			},
		],
		order: [[db.registrations, 'registrationId', 'asc']],
		transaction,
	});
	if (occurrence == null) {
		throw new AppError(SYSTEM_ERROR, `occurrence ${occurrenceId} does not exist`, false);
	}
	const occuDetails = {
		sumExpected: 0,
		sumAttended: 0,
	};
	occurrence.registrations?.forEach((r) => {
		occuDetails.sumExpected += r.expected ?? 0;
		occuDetails.sumAttended +=
			occurrence?.Category?.groupBooking || occurrence?.Occasion?.groupBooking
				? (r.actualCompanionCount ?? 0) + (r.actualParticipantCount ?? 0)
				: r.attended ?? 0;
	});
	const cancels = await db.registrations.findAll({
		where: {
			occurrenceId: occurrenceId,
			cancelledAt: { [Op.not]: null },
		},
		attributes: {
			exclude: ['isRegistered', 'isFriends', 'isNotified1', 'isNotified2', 'createdAt', 'updatedAt', 'deletedAt'],
		},
		include: [
			{
				association: db.registrations.associations.Member,
				attributes: {
					exclude: ['lineId'],
					include: [
						//
						'customerRegistrationId1',
						'customerRegistrationId2',
					],
				},
				include: [
					{
						separate: true,
						association: db.members.associations.campaignAnswers,
						attributes: { exclude: ['memberId', 'campaignQuestionId'] },
					},
				],
				required: false,
			},
		],
		order: [['registrationId', 'asc']],
		paranoid: false,
		transaction,
	});
	return { ...occuDetails, ...occurrence.toJSON(), cancels };
};

export const editOccurrences = async (
	categoryId: number | null,
	occasionId: number | null,
	occurrenceData: {
		maxAttendee: number;
		startAt: string;
		endAt: string;
		remarks: string | null;
		isDisplayed: boolean;
	}[],
	transaction?: Transaction,
) => {
	const confirmedOccurrences: {
		maxAttendee: number;
		categoryId: number | null;
		occasionId: number | null;
		startAt: string | Date;
		endAt: string;
		startDate?: string | Date;
		remarks: string | null;
		isDisplayed: boolean;
		deletedAt: null;
	}[] = [];
	const occurrences = await db.occurrences.findAll({
		attributes: ['occurrenceId', 'startAt', 'endAt', 'deletedAt'],
		where: {
			categoryId: categoryId,
			occasionId: occasionId,
			startAt: {
				[Op.in]: occurrenceData.map((o) => o.startAt),
			},
		},
		paranoid: false,
		transaction,
	});
	const result: { categoryId: number | null; occasionId: number | null; occurrenceIds: number[] } = {
		categoryId,
		occasionId,
		occurrenceIds: [],
	};
	occurrenceData.forEach((orb) => {
		const orbDate = new Date(orb.startAt).getMinutes();
		const startDate = moment(orb.startAt).format('YYYY-MM-DD');
		const odb = occurrences.find((o) => {
			return 0 == new Date(o.startAt).getMinutes() - orbDate;
		});
		if (odb == null || odb.deletedAt != null) {
			confirmedOccurrences.push({
				maxAttendee: orb.maxAttendee,
				categoryId,
				occasionId,
				startAt: orb.startAt,
				endAt: orb.endAt,
				startDate: startDate,
				isDisplayed: orb.isDisplayed,
				remarks: orb.remarks,
				deletedAt: null,
			});
		}
	});
	if (confirmedOccurrences.length > 0) {
		const createdOccurrences = await db.occurrences.bulkCreate(confirmedOccurrences as any, {
			fields: ['maxAttendee', 'categoryId', 'occasionId', 'startDate', 'startAt', 'endAt', 'remarks', 'isDisplayed'],
			updateOnDuplicate: ['maxAttendee', 'deletedAt', 'isDisplayed'],
			transaction,
		});
		result.occurrenceIds.push(...createdOccurrences.map((o) => o.occurrenceId));
		return result;
	} else {
		return result;
	}
};

export const updateOccurrence = async (
	{ occurrenceId, params }: { occurrenceId: number; params: WhereAttributeHash },
	transaction?: Transaction,
) =>
	db.occurrences
		.findByPk(occurrenceId, {
			attributes: {
				include: [[cast(fn('IFNULL', fn('SUM', col('registrations.expected')), 0), 'signed'), 'sumExpected']],
			},
			include: {
				association: db.occurrences.associations.registrations,
				attributes: [],
			},
			transaction,
		})
		.then((occurrence) => {
			if (occurrence == null) {
				throw new AppError(SYSTEM_ERROR, `occurrence ${occurrenceId} does not exist`, false);
			} else if (params.maxAttendee != null && (occurrence.sumExpected as number) > params.maxAttendee) {
				throw new AppError(
					SYSTEM_ERROR,
					`occurrence.sumExpected ${occurrence.sumExpected} > maxAttendee ${params.maxAttendee}`,
					false,
				);
			} else if (params.maxAttendee != null) {
				occurrence.set({ maxAttendee: params.maxAttendee });
			}
			if (params.isDisplayed != null) {
				occurrence.set({ isDisplayed: params.isDisplayed });
			}
			if (params.remarks != undefined) {
				occurrence.set({ remarks: params.remarks });
			}
			return occurrence.save({ transaction });
		});

export const deleteOccurrence = async (occurrenceId: number, transaction?: Transaction) =>
	db.occurrences.findByPk(occurrenceId, { transaction }).then(async (occurrence) => {
		if (occurrence == null) {
			throw new AppError(SYSTEM_ERROR, `occurrence ${occurrenceId} does not exist`, false);
		} else {
			const { categoryId, occasionId, occurrenceId } = occurrence;
			await occurrence.destroy({ transaction });
			return { categoryId, occasionId, occurrenceId };
		}
	});

export const bulkDeleteOccurrences = async (occurrenceWhere: WhereAttributeHash, transaction?: Transaction) => {
	const registrations = await db.registrations.findAll({
		attributes: ['registrationId'],
		include: {
			association: db.registrations.associations.Occurrence,
			where: occurrenceWhere,
		},
		transaction,
	});
	await Promise.all([
		db.occurrences.destroy({ where: occurrenceWhere, transaction }),
		db.registrations.destroy({
			where: { registrationId: { [Op.in]: registrations.map((r) => r.registrationId as number) } },
			transaction,
		}),
	]);
	return;
};

export const countOccurrencesToBeDeleted = async (occurrenceWhere: WhereAttributeHash, transaction?: Transaction) =>
	db.occurrences.count({ where: occurrenceWhere, transaction });

export const pushEventAndCountClientRegistrationToRedis = async () => {
	try {
		const allOccurrence = await db.occurrences.findAll({
			include: [
				{
					association: db.occurrences.associations.registrations,
					attributes: ['expected'],
				},
			],
		});
		if (!isEmpty(allOccurrence)) {
			for (const occurrence of allOccurrence) {
				const sum = occurrence.registrations!.reduce((p, c) => p + c.expected, 0);

				if (moment().isBefore(occurrence.endAt) && sum < occurrence.maxAttendee) {
					if (occurrence?.registrations) {
						await redisCacheService.setOccurrenceRegisterCount(occurrence.occurrenceId, sum);
					}
				}
			}
		}
		return true;
	} catch (err) {
		writeLog({ msg: 'pushEventAndCountClientRegistrationToRedis error', err: err }, 'error');
	}
};

export const createOccurrence = async (data: CreateOccurrenceSchema, transaction?: Transaction) => {
	return db.occurrences.create(omit(data, 'occurrenceId'), {
		include: [OccurrenceDetail],
		transaction,
	});
};

export const updateOccurrenceV2 = async (data: UpdateOccurrenceSchema, transaction?: Transaction) => {
	const { occurrenceId, maxAttendee, notes, occurrenceDetails, responseDeadlineDate } = data;
	for (const occurrenceDetail of occurrenceDetails) {
		const validateNewMaxAttendee = await OccurrenceDetailService.findAllOccurrenceDetailByWhereCondition({
			totalAttendees: { [Op.gt]: maxAttendee },
			occurrenceDetailId: occurrenceDetail.occurrenceDetailId,
		});
		if (!_.isEmpty(validateNewMaxAttendee)) {
			throw new AppError(BAD_REQUEST, 'The new max attendees must be greater than the old max attendees.');
		}
	}
	await db.occurrences.update(
		{ maxAttendee, notes, responseDeadlineDate },
		{
			where: { occurrenceId },
			transaction,
		},
	);
	for (const occurrenceDetail of occurrenceDetails) {
		await OccurrenceDetailService.updateOccurrenceDetail({ occurrenceDetail }, transaction);
	}
	return true;
};

export const getOccurrenceDetailById = async (occurrenceId: number) =>
	db.occurrences.findOne({
		where: { occurrenceId },
	});

export const checkMaxAttendees = async (
	occasionId: number,
	nextInterviewStep: INTERVIEW_STEP,
	totalAttendee: number,
) => {
	let isMaxAttendees = false;
	const today = getDateNowWithTimezone().format(DATE_FORMAT_TYPE.ISO_DATE);
	const timeNow = getDateNowWithTimezone().format(TIME_FORMAT);
	const occurrenceNextStep = await db.occurrences.findAll({
		where: {
			occasionId,
			interviewStep: nextInterviewStep,
		},
		include: [
			{
				model: db.occurrenceDetailModel,
				where: {
					[Op.or]: [
						{
							eventDay: { [Op.gt]: today },
						},
						{
							eventDay: today,
							endTime: { [Op.gt]: timeNow },
						},
					],
				},
				attributes: ['maxAttendee', 'totalAttendees'],
			},
		],
	});

	if (!occurrenceNextStep.length) return { status: true, reason: 'not_time_invalid' };
	for (const occurrence of occurrenceNextStep) {
		const { occurrenceDetails } = occurrence;
		const totalRemain =
			occurrenceDetails?.reduce((acc, item) => {
				const remain = item.maxAttendee - item.totalAttendees;
				return acc + remain;
			}, 0) || 0;
		if (totalRemain < totalAttendee) {
			isMaxAttendees = true;
			break;
		}
	}
	return { status: isMaxAttendees, reason: 'full_slot' };
};

export const updateOccurrenceDataDetail = async (data: UpdateOccurrenceDataDetailSchema, transaction?: Transaction) => {
	const { occurrenceId, maxAttendee, notes, responseDeadlineDate, interviewStep } = data;
	const currentOccurrence = await db.occurrences.findOne({
		where: {
			occurrenceId,
		},
		include: [{ model: db.occasions }],
	});
	if (!currentOccurrence) {
		throw new AppError(BAD_REQUEST, `The occurrence not found ${CUSTOM_SHOW_MESSAGE}`);
	}

	await db.occurrences.update(
		{ maxAttendee, notes, responseDeadlineDate },
		{
			where: { occurrenceId },
			transaction,
		},
	);
	return true;
};

export const deleteOccurrenceIfNoOccurrenceDetail = async (occurrenceId: number) => {
	const count = await db.occurrenceDetailModel.count({ where: { occurrenceId } });
	if (count === 0) {
		await db.occurrences.destroy({ where: { occurrenceId } });
	}
};

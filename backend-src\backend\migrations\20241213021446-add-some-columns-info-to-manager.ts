import { DATABASE_TABLE_NAME } from '../config';
import { SequelizeUtility } from '../utilities';

import type { Migration } from 'sequelize-cli';

module.exports = {
	async up(queryInterface, Sequelize) {
		const NEW_COLUMNS = ['officeName', 'phoneNumber', 'managerName', 'website', 'emailContact'];
		for (const newColumn of NEW_COLUMNS) {
			if (await SequelizeUtility.columnNotExists(queryInterface, DATABASE_TABLE_NAME.MANAGER, newColumn)) {
				await queryInterface.addColumn(DATABASE_TABLE_NAME.MANAGER, newColumn, {
					type: Sequelize.STRING,
					allowNull: true,
				});
			}
		}
	},

	async down(queryInterface) {
		const NEW_COLUMNS = ['officeName', 'phoneNumber', 'managerName', 'website', 'emailContact'];
		for (const newColumn of NEW_COLUMNS) {
			if (await SequelizeUtility.columnExists(queryInterface, DATABASE_TABLE_NAME.MANAGER, newColumn)) {
				await queryInterface.removeColumn(DATABASE_TABLE_NAME.MANAGER, newColumn);
			}
		}
	},
} satisfies Migration;

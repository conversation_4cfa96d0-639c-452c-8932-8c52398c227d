import { DATABASE_TABLE_NAME, STUDENT_TYPE } from '../config';
import { SequelizeUtility } from '../utilities';

import type { Migration } from 'sequelize-cli';

module.exports = {
	async up(queryInterface, Sequelize) {
		if (
			await SequelizeUtility.columnNotExists(queryInterface, DATABASE_TABLE_NAME.CUSTOMER_REGISTRATIONS, 'studentType')
		) {
			await queryInterface.addColumn(DATABASE_TABLE_NAME.CUSTOMER_REGISTRATIONS, 'studentType', {
				type: Sequelize.ENUM(...Object.values(STUDENT_TYPE)),
				allowNull: false,
				defaultValue: STUDENT_TYPE.ENROLLED_STUDENT,
			});
		}
	},

	async down(queryInterface) {
		if (
			await SequelizeUtility.columnExists(queryInterface, DATABASE_TABLE_NAME.CUSTOMER_REGISTRATIONS, 'studentType')
		) {
			await queryInterface.removeColumn(DATABASE_TABLE_NAME.CUSTOMER_REGISTRATIONS, 'studentType');
		}
	},
} satisfies Migration;

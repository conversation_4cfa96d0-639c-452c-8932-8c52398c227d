import {
	CreationOptional,
	DataTypes,
	ForeignKey,
	InferAttributes,
	InferCreationAttributes,
	Model,
	NonAttribute,
	Sequelize,
} from 'sequelize';
import { DATABASE_TABLE_NAME, INTERVIEW_STEP, MEMBER_INTERVIEW_STATUS } from '~config';
import { Member } from '~models/memberModel';
import { Occasion } from '~models/occasionModel';

export class OccasionMember extends Model<InferAttributes<OccasionMember>, InferCreationAttributes<OccasionMember>> {
	//ATTRIBUTES
	declare occasionMemberId: CreationOptional<number>;
	declare interviewStatus: CreationOptional<MEMBER_INTERVIEW_STATUS>;
	declare memberId: ForeignKey<Member['memberId']>;
	declare occasionId: ForeignKey<Occasion['occasionId']>;
	declare memberInterviewCurrentStep: CreationOptional<INTERVIEW_STEP>;
	declare occurrenceIdsRejected: CreationOptional<number[]>;
	declare expiredAt: Date | null;
	declare notes: CreationOptional<string> | null;

	declare Member?: NonAttribute<Member>;

	//TIMESTAMPS
	declare createdAt: CreationOptional<Date>;
	declare updatedAt: CreationOptional<Date>;

	static initClass = (sequelize: Sequelize) =>
		OccasionMember.init(
			{
				occasionMemberId: {
					type: DataTypes.INTEGER({ unsigned: true }),
					allowNull: false,
					primaryKey: true,
					autoIncrement: true,
				},
				interviewStatus: {
					type: DataTypes.ENUM(...Object.values(MEMBER_INTERVIEW_STATUS)),
					allowNull: false,
					defaultValue: MEMBER_INTERVIEW_STATUS.INTERVIEW_PROPOSAL,
				},
				memberInterviewCurrentStep: {
					type: DataTypes.ENUM(...Object.values(INTERVIEW_STEP)),
					allowNull: false,
					defaultValue: INTERVIEW_STEP.FIRST_INTERVIEW,
				},
				occurrenceIdsRejected: {
					type: DataTypes.JSON,
					allowNull: false,
					defaultValue: [],
				},
				notes: {
					type: DataTypes.STRING(5000),
					allowNull: true,
				},
				expiredAt: { type: DataTypes.DATE, allowNull: true, defaultValue: null },
				createdAt: DataTypes.DATE,
				updatedAt: DataTypes.DATE,
			},
			{
				tableName: DATABASE_TABLE_NAME.OCCASION_MEMBER,
				timestamps: true,
				paranoid: false,
				sequelize: sequelize,
				charset: 'utf8mb4',
				collate: 'utf8mb4_general_ci',
				name: {
					singular: 'occasionMember',
					plural: DATABASE_TABLE_NAME.OCCASION_MEMBER,
				},
			},
		);
}

import { z } from 'zod';

export const createOrUpdateBufferEventTimeSettingSchema = z
	.object({
		startTime: z.number().optional().nullable(),
		endTime: z.number().optional().nullable(),
	})
	.refine(
		(data) => {
			const hasStartTime = data.startTime !== undefined && data.startTime !== null;
			const hasEndTime = data.endTime !== undefined && data.endTime !== null;
			return hasStartTime === hasEndTime;
		},
		{
			message: 'If startTime is provided, endTime must also be provided, and vice versa.',
			path: ['startTime'],
		},
	);

export type CreateOrUpdateBufferEventTimeSettingSchema = z.infer<typeof createOrUpdateBufferEventTimeSettingSchema>;

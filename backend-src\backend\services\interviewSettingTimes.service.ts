import { Op, Transaction } from 'sequelize';
import { db } from '~models';

export const createInterviewSettingTimes = async ({
	interviewSettingId,
	interviewSettingTimes,
	transaction,
}: {
	interviewSettingId: number;
	interviewSettingTimes: {
		date: Date;
		startTime: string;
		endTime: string;
		classCategoryId: number;
		isBlockedCategory: boolean;
	}[];
	transaction?: Transaction;
}) => {
	const interviewSettingTimesData = interviewSettingTimes.map((item) => ({
		interviewSettingId,
		...item,
	}));
	return db.interviewSettingTimes.bulkCreate(interviewSettingTimesData, { transaction });
};

export const deleteInterviewSettingTimesWithDateExists = async ({
	dateInterviewSettingHaveInDB,
	transaction,
}: {
	dateInterviewSettingHaveInDB: any[];
	transaction?: Transaction;
}) => {
	return db.interviewSettingTimes.destroy({
		where: {
			[Op.and]: [
				{ date: { [Op.in]: dateInterviewSettingHaveInDB.map((item) => item.date) } },
				{ classCategoryId: { [Op.in]: dateInterviewSettingHaveInDB.map((item) => item.classCategoryId) } },
			],
		},
		transaction,
	});
};

export const updateInterviewSettingTimesWithDateExists = async ({
	dateInterviewSettingOnlyInDB,
	transaction,
}: {
	dateInterviewSettingOnlyInDB: any[];
	transaction?: Transaction;
}) => {
	// return db.interviewSettingTimes.update(
	// 	{ isBlockedCategory: true },
	// 	{
	// 		where: {
	// 			[Op.and]: [
	// 				{ date: { [Op.in]: dateInterviewSettingOnlyInDB.map((item) => item.date) } },
	// 				{ classCategoryId: { [Op.in]: dateInterviewSettingOnlyInDB.map((item) => item.classCategoryId) } },
	// 			],
	// 		},
	// 		transaction,
	// 	},
	// );
	for (const item of dateInterviewSettingOnlyInDB) {
		await db.interviewSettingTimes.update(
			{
				startTime: item.startTime,
				endTime: item.endTime,
				isBlockedCategory: item.isBlockedCategory,
			},
			{
				where: {
					date: item.date,
					classCategoryId: item.classCategoryId,
				},
				transaction,
			},
		);
	}
	return true;
};

export const getInterviewSettingTimesFromToDate = async ({
	from,
	to,
	transaction,
}: {
	from: Date;
	to: Date;
	transaction?: Transaction;
}) => {
	return db.interviewSettingTimes.findAll({
		where: {
			date: { [Op.between]: [from, to] },
		},
		transaction,
	});
};

import { Migration } from 'sequelize-cli';
import { DATABASE_TABLE_NAME } from '~config';

module.exports = {
	async up(queryInterface, Sequelize) {
		await queryInterface.changeColumn(DATABASE_TABLE_NAME.MANAGER, 'emailContact', {
			type: Sequelize.STRING(5000),
			allowNull: true,
		});
	},

	async down(queryInterface, Sequelize) {
		await queryInterface.changeColumn(DATABASE_TABLE_NAME.MANAGER, 'emailContact', {
			type: Sequelize.STRING,
			allowNull: true,
		});
	},
} satisfies Migration;

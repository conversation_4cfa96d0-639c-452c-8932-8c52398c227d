import { CreationOptional, DataTypes, InferAttributes, InferCreationAttributes, Model, Sequelize } from 'sequelize';
import { DATABASE_TABLE_NAME } from '~config';

export class MaintenanceMode extends Model<InferAttributes<MaintenanceMode>, InferCreationAttributes<MaintenanceMode>> {
	//ATTRIBUTES
	declare id: CreationOptional<number>;
	declare isEnabled: boolean;
	declare imageFilename: string | null;
	declare createdAt: CreationOptional<Date>;
	declare updatedAt: CreationOptional<Date>;

	//ASSOCIATIONS
	static initClass = (sequelize: Sequelize) =>
		MaintenanceMode.init(
			{
				id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
				isEnabled: { type: DataTypes.BOOLEAN, allowNull: false, defaultValue: false },
				imageFilename: { type: DataTypes.STRING(500), allowNull: true, defaultValue: null },
				createdAt: { type: DataTypes.DATE, allowNull: false },
				updatedAt: { type: DataTypes.DATE, allowNull: false },
			},
			{
				sequelize: sequelize,
				tableName: DATABASE_TABLE_NAME.MAINTENANCE_MODE,
				timestamps: true,
				charset: 'utf8mb4',
				collate: 'utf8mb4_general_ci',
				name: {
					singular: DATABASE_TABLE_NAME.MAINTENANCE_MODE,
					plural: 'maintenanceMode',
				},
			},
		);
}

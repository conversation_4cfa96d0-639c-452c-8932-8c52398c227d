import { DATABASE_TABLE_NAME, INTERVIEW_STEP } from '../config';
import { SequelizeUtility } from '../utilities';

import type { Migration } from 'sequelize-cli';

module.exports = {
	async up(queryInterface, Sequelize) {
		const NEW_COLUMNS = ['memberInterviewCurrentStep'];
		for (const newColumn of NEW_COLUMNS) {
			if (await SequelizeUtility.columnNotExists(queryInterface, DATABASE_TABLE_NAME.OCCASION_MEMBER, newColumn)) {
				await queryInterface.addColumn(DATABASE_TABLE_NAME.OCCASION_MEMBER, newColumn, {
					type: Sequelize.ENUM(...Object.values(INTERVIEW_STEP)),
					allowNull: false,
					defaultValue: INTERVIEW_STEP.FIRST_INTERVIEW,
				});
			}
		}
	},

	async down(queryInterface) {
		const NEW_COLUMNS = ['memberInterviewCurrentStep'];
		for (const newColumn of NEW_COLUMNS) {
			if (await SequelizeUtility.columnExists(queryInterface, DATABASE_TABLE_NAME.OCCASION_MEMBER, newColumn)) {
				await queryInterface.removeColumn(DATABASE_TABLE_NAME.OCCASION_MEMBER, newColumn);
			}
		}
	},
} satisfies Migration;

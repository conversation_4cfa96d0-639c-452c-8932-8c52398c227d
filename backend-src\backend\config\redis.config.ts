export default {
	host: process.env.REDIS_HOST || 'localhost',
	port: parseInt(`${process.env.REDIS_PORT}`, 10) || 6379,
};

export const RedisKeys = {
	EVENT_CLIENT_REGISTRATION_COUNT: 'client_registration_event_count',
	EVENT_OCCURRENCE: 'event_occurrence',
	EVENT_OCCURRENCE_DETAIL: 'event_occurrence_detail',
	EVENT_OCCURRENCE_DETAIL_MAX_ATTENDEE: 'event_occurrence_detail_max_attendee',
	EVENT_OCCURRENCE_LOCK_CREATION: 'event_occurrence_lock_creation',
	NOTIFICATION_SETTING: 'notification_setting'
};

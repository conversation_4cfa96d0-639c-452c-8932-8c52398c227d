import { z } from 'zod';
import { MESSAGE_ZOD_VALIDATE_DATE, REGEX_DATE_VALIDATE } from '~config';
import { createOccurrenceDetailSchema, interviewStepSchema, occurrenceDetails } from '~schemas/occurrenceDetail';

export const createOccurrenceSchema = z.object({
	categoryId: z.number().min(1),
	occasionId: z.number().min(1),
	interviewStep: interviewStepSchema,
	responseDeadlineDate: z.string().regex(REGEX_DATE_VALIDATE, MESSAGE_ZOD_VALIDATE_DATE),
	maxAttendee: z.number().min(1),
	notes: z.string().optional().default(''),
	occurrenceDetails: z.array(createOccurrenceDetailSchema),
	occurrenceId: z.number().optional(),
});

export const updateOccurrenceSchema = z.object({
	occurrenceId: z.string().min(1),
	maxAttendee: z.number().min(1),
	notes: z.string().optional().default(''),
	occurrenceDetails: z.array(occurrenceDetails),
	responseDeadlineDate: z.string().min(1),
});

export const getOccurrenceByStepSchema = z.object({
	occasionId: z.string().min(1),
	interviewStep: interviewStepSchema,
});

export const updateOccurrenceDataDetailSchema = z.object({
	occurrenceId: z.number().min(1),
	maxAttendee: z.number().min(1),
	notes: z.string().optional().default(''),
	responseDeadlineDate: z.string().min(1),
	interviewStep: interviewStepSchema,
});

export type CreateOccurrenceSchema = z.infer<typeof createOccurrenceSchema>;
export type UpdateOccurrenceSchema = z.infer<typeof updateOccurrenceSchema>;
export type UpdateOccurrenceDataDetailSchema = z.infer<typeof updateOccurrenceDataDetailSchema>;

import { FindOptions, Op, QueryInterface, Sequelize } from 'sequelize';
import { SORT } from '~config';

export const generateWhereClauseBetween = <T = number | string | Date>(name: string, values: [T, T]) => {
	const [min, max] = values;

	if (!min && !max) return {};

	if (!min || !max) {
		if (min) return { [name]: { [Op.gte]: min } };
		if (max) return { [name]: { [Op.lte]: max } };
	}

	return { [name]: { [Op.between]: [min, max] } };
};

export const buildPaginationParams = (pagination: paginationParams): FindOptions => ({
	...(pagination?.pp ? { limit: pagination.pp } : {}),
	...(pagination?.p ? { offset: (pagination.p - 1) * pagination.pp } : {}),
	...(pagination?.sortKey
		? {
				order: [
					[pagination.sortKey, Sequelize.literal('IS NULL ASC')],
					[pagination.sortKey, pagination?.sort || SORT.DESCENDING],
				],
		  }
		: {}),
});

export const columnExists = async (
	queryInterface: QueryInterface,
	tableName: string,
	columnName: string,
): Promise<boolean> => {
	const tableDescription = await queryInterface.describeTable(tableName);
	return columnName in tableDescription;
};

export const columnNotExists = async (
	queryInterface: QueryInterface,
	tableName: string,
	columnName: string,
): Promise<boolean> => {
	return !(await columnExists(queryInterface, tableName, columnName));
};

export const tableExists = async (queryInterface: QueryInterface, tableName: string): Promise<boolean> => {
	return queryInterface.tableExists(tableName);
};

export const tableNotExists = async (queryInterface: QueryInterface, tableName: string): Promise<boolean> => {
	return !(await tableExists(queryInterface, tableName));
};

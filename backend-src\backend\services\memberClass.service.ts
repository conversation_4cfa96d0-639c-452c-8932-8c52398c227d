import { CreationAttributes, Op, Transaction } from 'sequelize';
import { db } from '~models';
import { MemberClass } from '~models/memberClassModel';
import { GetMemberClassListSchema } from '~schemas/memberClass';
import { AppError } from '~utilities';
import { BAD_REQUEST, CLASS_CATEGORY_NAME, CUSTOM_SHOW_MESSAGE } from '~config';

export const createMemberClass = async (data: CreationAttributes<MemberClass>, transaction?: Transaction) => {
	const { className } = data;
	const classExists = await db.memberClassModel.findOne({
		where: {
			className,
		},
	});
	if (classExists) {
		throw new AppError(BAD_REQUEST, `Class already exists ${CUSTOM_SHOW_MESSAGE}`, false);
	}
	return db.memberClassModel.create(data, { transaction });
};

export const getMemberClassDetails = async (managerId: number) => db.memberClassModel.findByPk(managerId);

export const deleteMemberClass = async (memberClassId: number, transaction?: Transaction) =>
	db.memberClassModel.destroy({
		where: { memberClassId },
		transaction,
	});

export const updateMemberClass = async (
	memberClassId: number,
	data: Partial<CreationAttributes<MemberClass>>,
	transaction?: Transaction,
) => {
	const { className } = data;
	const classExists = await db.memberClassModel.findOne({
		where: {
			className,
		},
	});
	if (classExists && className !== classExists.className) {
		throw new AppError(BAD_REQUEST, `Class already exists ${CUSTOM_SHOW_MESSAGE}`, false);
	}
	return db.memberClassModel.update(data, { where: { memberClassId }, transaction });
};

export const getMemberClassList = async ({
	page,
	pageSize,
	isFetchAll,
	isGeneralUniversity,
	isFetchAllCategory,
}: GetMemberClassListSchema) => {
	if (isFetchAll) {
		return db.memberClassModel.findAll({
			order: [['memberClassId', 'desc']],
			include: isGeneralUniversity
				? [
						{
							model: db.classCategoryModel,
							where: {
								name: CLASS_CATEGORY_NAME.COMEDIAN,
							},
						},
				  ]
				: [],
		});
	}
	const currentPage = parseInt(page);
	const limit = parseInt(pageSize);
	const offset = (currentPage - 1) * limit;
	const totalMemberClass = await db.memberClassModel.count();
	let queryClassCategory = {
		name: isGeneralUniversity ? { [Op.eq]: CLASS_CATEGORY_NAME.COMEDIAN } : { [Op.ne]: CLASS_CATEGORY_NAME.COMEDIAN },
	} as any;
	if (isFetchAllCategory) {
		queryClassCategory = {};
	}

	const memberClasses = await db.memberClassModel.findAll({
		limit,
		offset,
		order: [['memberClassId', 'desc']],
		include: [
			{
				model: db.classCategoryModel,
				where: queryClassCategory,
			},
		],
	});
	return {
		total: totalMemberClass,
		list: memberClasses,
		page: currentPage,
		pageSize: limit,
	};
};

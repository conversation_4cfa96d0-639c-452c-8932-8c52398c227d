import { DATABASE_TABLE_NAME } from '../config';
import { SequelizeUtility } from '../utilities';

import type { Migration } from 'sequelize-cli';

module.exports = {
	async up(queryInterface, Sequelize) {
		const NEW_COLUMNS = [
			{
				name: 'expiredAt',
				type: {
					type: Sequelize.DATE,
					allowNull: true,
					defaultValue: null,
				},
			},
		];
		for (const newColumn of NEW_COLUMNS) {
			if (await SequelizeUtility.columnNotExists(queryInterface, DATABASE_TABLE_NAME.REGISTRATIONS, newColumn.name)) {
				await queryInterface.addColumn(DATABASE_TABLE_NAME.REGISTRATIONS, newColumn.name, newColumn.type);
			}
			if (await SequelizeUtility.columnNotExists(queryInterface, DATABASE_TABLE_NAME.OCCASION_MEMBER, newColumn.name)) {
				await queryInterface.addColumn(DATABASE_TABLE_NAME.OCCASION_MEMBER, newColumn.name, newColumn.type);
			}
		}
	},

	async down(queryInterface) {
		const NEW_COLUMNS = ['expiredAt'];
		for (const newColumn of NEW_COLUMNS) {
			if (await SequelizeUtility.columnExists(queryInterface, DATABASE_TABLE_NAME.REGISTRATIONS, newColumn)) {
				await queryInterface.removeColumn(DATABASE_TABLE_NAME.REGISTRATIONS, newColumn);
			}
			if (await SequelizeUtility.columnExists(queryInterface, DATABASE_TABLE_NAME.OCCASION_MEMBER, newColumn)) {
				await queryInterface.removeColumn(DATABASE_TABLE_NAME.OCCASION_MEMBER, newColumn);
			}
		}
	},
} satisfies Migration;

import moment from 'moment';
import 'moment-timezone';

import { DATE_FORMAT, TIME_FORMAT, TIME_ZONE_DEFAULT } from '~config';

export const isDateAfterOrEqualDays = (dateISO = '', days: number, timeZone = TIME_ZONE_DEFAULT): boolean => {
	if (!dateISO) return false;
	const currentDate = moment().tz(timeZone).startOf('day');

	const momentDate = moment.tz(dateISO, timeZone).startOf('day');

	return momentDate.diff(currentDate, 'days') >= days;
};

export const subDaysToDate = (dateISO: string, days: number, timeZone = TIME_ZONE_DEFAULT): Date => {
	const currentDate = moment.tz(dateISO, timeZone);

	currentDate.subtract(days, 'days');

	return currentDate.toDate();
};

export const getStartOfDayBeforeDaysToDate = ({ timeZone = TIME_ZONE_DEFAULT, days = 0 }) => {
	return moment().tz(timeZone).add(days, 'days').startOf('day');
};

export const getEndOfDayBeforeDaysToDate = ({ timeZone = TIME_ZONE_DEFAULT, days = 0 }) => {
	return moment().tz(timeZone).add(days, 'days').endOf('day');
};

export const isDateBeforeOrEqualDays = (dateISO = '', days: number, timeZone = TIME_ZONE_DEFAULT): boolean => {
	if (!dateISO) return false;
	const currentDate = moment().tz(timeZone).startOf('day');

	const momentDate = moment.tz(dateISO, timeZone).startOf('day');

	return momentDate.diff(currentDate, 'days') <= days;
};

export const DATE_FORMAT_TYPE = {
	DATE_JP: 'YYYY年M月D日',
	ISO_DATE: 'YYYY-MM-DD',
	DATE_MONTH_DAY: 'MM/DD',
};

export const formatDate = (value: Date | null, formatType = DATE_FORMAT_TYPE.DATE_JP) => {
	if (!value) return 'ー';

	return moment(value).format(formatType);
};

export const isSameDay = (dateISO: Date | null, timeZone = TIME_ZONE_DEFAULT): boolean => {
	if (!dateISO) return false;

	const currentDate = moment.tz(timeZone);

	const momentDate = moment.tz(dateISO, timeZone);

	return momentDate.isSame(currentDate, 'day');
};

export const diffDayNowWithFormat = (dateStr: string, format = DATE_FORMAT) => {
	const targetDate = moment(dateStr, format);

	const currentDate = moment();
	return targetDate.diff(currentDate, 'days');
};

export const getDateNowWithTimezone = (timeZone = TIME_ZONE_DEFAULT) => {
	return moment.tz(timeZone);
};

export const formatTimeAdd = (time: string, offset: number) =>
	moment(time, TIME_FORMAT).add(offset, 'minutes').format(TIME_FORMAT);

export const formatTimeSub = (time: string, offset: number) =>
	moment(time, TIME_FORMAT).subtract(offset, 'minutes').format(TIME_FORMAT);

import { Migration } from 'sequelize-cli';

module.exports = {
	async up(queryInterface, Sequelize) {
		await queryInterface.changeColumn('chats', 'contents', {
			type: Sequelize.STRING(5000),
			allowNull: false,
		});
	},

	async down(queryInterface, Sequelize) {
		await queryInterface.changeColumn('chats', 'contents', {
			type: Sequelize.STRING(1000),
			allowNull: false,
		});
	},
} satisfies Migration;

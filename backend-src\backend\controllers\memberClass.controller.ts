import { NextFunction, Request, Response } from 'express';

import { BAD_REQUEST, RESPONSE_SUCCESS } from '~config';
import { db } from '~models';
import { memberClassService, MemberService, RegistrationService, OccasionMemberService } from '../services';
import { AppError } from '~utilities';
import {
	createMemberClassSchema,
	getMemberClassListSchema,
	queryParamMemberClassSchema,
	updateMemberClassSchema,
} from '~schemas/memberClass';
import { countMembersByMemberClassId } from '~services/memberService';
import { isTrue } from '~utilities/common.utils';

export const createMemberClass = async (req: Request, res: Response, next: NextFunction) => {
	const transaction = await db.sequelize.transaction();

	try {
		const validatedBody = createMemberClassSchema.parse(req.body);

		await memberClassService.createMemberClass(validatedBody, transaction);
		await transaction.commit();
		res.sendStatus(RESPONSE_SUCCESS);
	} catch (e) {
		await transaction.rollback();
		next(e);
	}
};

export const getMemberClassDetails = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const validParams = queryParamMemberClassSchema.parse(req.params);
		const { memberClassId } = validParams;

		const data = await memberClassService.getMemberClassDetails(parseInt(memberClassId));
		if (!data) {
			throw new AppError(BAD_REQUEST, 'member class is not found.');
		}
		const totalMemberRegisteredByClass = await MemberService.countMembersByMemberClassId(parseInt(memberClassId));

		res.status(RESPONSE_SUCCESS).json({ ...data.toJSON(), totalMemberRegisteredByClass });
	} catch (error) {
		next(error);
	}
};

export const deleteMemberClass = async (req: Request, res: Response, next: NextFunction) => {
	let transaction = null;

	try {
		const { memberClassId } = req.params;
		transaction = await db.sequelize.transaction();

		const memberClass = await memberClassService.getMemberClassDetails(parseInt(memberClassId));
		if (!memberClass) {
			throw new AppError(BAD_REQUEST, 'member class is not found.');
		}

		const members = await MemberService.getMembersByMemberClassId(parseInt(memberClassId));
		const memberIds = members.map((member) => member.memberId);

		await memberClassService.deleteMemberClass(parseInt(memberClassId), transaction);
		if (memberIds.length > 0) {
			await MemberService.updateMembersAfterRemoveMemberClass(memberIds, transaction);
			await RegistrationService.updateRegistrationsAfterRemoveMemberClass(memberIds, transaction);
			await OccasionMemberService.updateOccasionMemberAfterRemoveMemberClass(memberIds, transaction);
		}

		await transaction.commit();

		res.status(RESPONSE_SUCCESS).send('OK');
	} catch (error) {
		if (transaction !== null) {
			await transaction.rollback();
		}
		next(error);
	}
};

export const updateMemberClass = async (req: Request, res: Response, next: NextFunction) => {
	const transaction = await db.sequelize.transaction();

	try {
		const { memberClassId } = req.params;
		const memberClass = await memberClassService.getMemberClassDetails(parseInt(memberClassId));

		if (!memberClass) {
			throw new AppError(BAD_REQUEST, 'member class is not found.');
		}
		const validatedBody = updateMemberClassSchema.parse(req.body);

		await memberClassService.updateMemberClass(parseInt(memberClassId), validatedBody, transaction);
		await transaction.commit();

		res.sendStatus(RESPONSE_SUCCESS);
	} catch (e) {
		await transaction.rollback();
		next(e);
	}
};

export const getMemberClassList = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const { isGeneralUniversity, isFetchAllCategory } = req.query;

		const validatedQuery = getMemberClassListSchema.parse({
			...req.query,
			isGeneralUniversity: isTrue(isGeneralUniversity),
			isFetchAllCategory: isTrue(isFetchAllCategory),
		});

		const memberClassList = await memberClassService.getMemberClassList({ ...validatedQuery });

		res.status(RESPONSE_SUCCESS).send(memberClassList);
	} catch (error) {
		next(error);
	}
};

export const getMemberClassAll = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const { isGeneralUniversity } = req.query;
		const validatedQuery = getMemberClassListSchema.parse({
			...req.query,
			isGeneralUniversity: isTrue(isGeneralUniversity),
		});

		const memberClassList = await memberClassService.getMemberClassList({ ...validatedQuery, pageSize: '9999' });

		res.status(RESPONSE_SUCCESS).send(memberClassList);
	} catch (error) {
		next(error);
	}
};

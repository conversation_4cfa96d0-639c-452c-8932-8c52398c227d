import _, { get, omit } from 'lodash';
import moment from 'moment';
import { formatTimeAdd, formatTimeSub } from '../utilities/commonDateTime';
import { col, CreationAttributes, IncludeOptions, Op, Sequelize, Transaction, WhereAttributeHash } from 'sequelize';

import {
	BAD_REQUEST,
	CONFLICT_ERROR,
	CRON_JOB_CHANGE_INTERVIEW_STATUS,
	CUSTOM_SHOW_MESSAGE,
	DATE_FORMAT,
	EVENT_REGISTRATIONS_REPLACER_MESSAGE,
	INTERVIEW_TYPE,
	MEMBER_ACTIONS_STATUS,
	MEMBER_INTERVIEW_STATUS,
	NOT_ACCEPTABLE,
	PAYMENT_STATUS,
	PAYMENT_TYPE,
	REMINDER_NOTIFY_TYPES,
	SYSTEM_ERROR,
	systemConfig,
	TIME_FORMAT,
} from '../config';
import { db } from '../models';
import { Member } from '../models/memberModel';
import { Registration } from '../models/registrationModel';
import { AppError, DATE_FORMAT_TYPE, getDateNowWithTimezone, RegistrationUtility, writeLog } from '../utilities';
import { getBookingDeadline } from './settingService';
import {
	BufferEventTimeSettingService,
	LineService,
	MemberService,
	OccasionMemberService,
	OccurrenceDetailService,
	OccurrenceService,
	ReminderService,
	SettingService,
	SocketServerService,
} from '.';
import { CustomerRegistration } from '~models/customerRegistrationModel';
import { ProjectionAlias } from 'sequelize/types/model';
import { redisCacheService } from '~services/redisCacheService';
import { sendLineToAdminAfterRegistrationEvent } from '~services/lineService';
import {
	AdminRegistrationByInterviewStepSchema,
	AdminUpdateInterviewStatusSchema,
	MemberCancelledRegistrationSchema,
	MemberChangeMemberActionStatusSchema,
	MemberConfirmOfficeAttendSchema,
	MemberGetRegistration,
	MemberGetRegistrationByInterviewStatusesSchema,
	MemberRegistrationForEventSchema,
} from '~schemas/registration';
import { Occasion } from '~models/occasionModel';
import { OccurrenceDetail } from '~models/occurrenceDetailModel';
import { Occurrence } from '~models/occurrenceModel';
import { diffDayNowWithFormat } from '~utilities/commonDateTime';
import { OccasionMember } from '~models/occasionMemberModel';

const { replacerName, replacerDateTime, replacerTelephoneCompany, replacerConfirmationUrl, replacerBuilding } =
	EVENT_REGISTRATIONS_REPLACER_MESSAGE;

export const getBufferTimes = async (memberId: number) => {
	const bufferEventTimeSetting = await BufferEventTimeSettingService.getBufferEventTimeSetting();
	const registeredEventsAllType = await db.registrations.findAll({
		where: {
			cancelledAt: null,
			memberId,
			interviewStatus: MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED,
		},
		include: [
			{
				model: db.occurrenceDetailModel,
				required: true,
			},
		],
	});
	const registeredTimes = registeredEventsAllType.map((reg) => {
		if (!reg?.occurrenceDetail) return;
		/***
		 All event registered
		 ***/
		return {
			eventDay: reg.occurrenceDetail.eventDay,
			startTime: reg.occurrenceDetail.startTime,
			endTime: reg.occurrenceDetail.endTime,
		};
	});
	if (!bufferEventTimeSetting) return registeredTimes;

	/***
	 Chỉ tính cho trường hợp OFFLINE điều kiện thoã mãn:
		 Thời gian bắt đầu event mới > event kết thúc trước + bộ đệm trước + bộ đệm sau
	   Thời gian kết thúc event mới < event bắt đầu của trước đó - bộ đệm trước - bộ đệm sau
		 Slide: https://docs.google.com/presentation/d/1mZxnSBHOcer06sLZdfpCqFZqR6ZIUNyDNXko89wJpso/edit#slide=id.g322f12ed8e9_0_43
		 EX: user đăng kí event 11/01 với thời gian: 12:00〜13:00 phía admin setting bộ đệm 30, 40 thì
	 			 thời gian đệm là 11/01 12:30〜13:40. Vậy event ngày 11/01 với thời gian 14:00〜15:00 không thoã mãn
	   off vs off thì vẫn 1h như bình thường
     onl vs onl thì không tính bộ đệm
     onl vs off thì sẽ tính bộ đệm 30p (30p này là do bộ đệm trc sau của off đấy ạ)
	   off vs onl thì sẽ tính bộ đệm 30p (30p này là do bộ đệm trc sau của off đấy ạ)
	 ***/
	const registeredEventsOffLine = await db.registrations.findAll({
		where: {
			cancelledAt: null,
			memberId,
			interviewStatus: MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED,
		},
		include: [
			{
				model: db.occurrenceDetailModel,
				required: true,
				where: { interviewType: INTERVIEW_TYPE.OFFLINE },
			},
		],
	});
	const bufferTimesOffline = [] as any;
	registeredEventsOffLine.forEach((reg) => {
		if (!reg?.occurrenceDetail) return;
		const totalTimeBufferOffLine = bufferEventTimeSetting.startTime + bufferEventTimeSetting.endTime;
		const bufferStartOffline = moment(reg.occurrenceDetail.endTime, TIME_FORMAT)
			.add(totalTimeBufferOffLine, 'minutes')
			.format(TIME_FORMAT);
		const bufferEndOffline = moment(reg.occurrenceDetail.startTime, TIME_FORMAT)
			.subtract(totalTimeBufferOffLine, 'minutes')
			.format(TIME_FORMAT);
		bufferTimesOffline.push({
			eventDay: reg.occurrenceDetail.eventDay,
			[Op.and]: [{ startTime: { [Op.lt]: bufferStartOffline } }, { endTime: { [Op.gt]: bufferEndOffline } }],
			interviewType: INTERVIEW_TYPE.OFFLINE,
		});
		const totalTimeBufferOnlineStart = bufferEventTimeSetting.startTime;
		const totalTimeBufferOnlineEnd = bufferEventTimeSetting.endTime;
		const bufferStartOnline = moment(reg.occurrenceDetail.endTime, TIME_FORMAT)
			.add(totalTimeBufferOnlineEnd, 'minutes')
			.format(TIME_FORMAT);
		const bufferEndOnline = moment(reg.occurrenceDetail.startTime, TIME_FORMAT)
			.subtract(totalTimeBufferOnlineStart, 'minutes')
			.format(TIME_FORMAT);
		bufferTimesOffline.push({
			eventDay: reg.occurrenceDetail.eventDay,
			[Op.and]: [{ startTime: { [Op.lt]: bufferStartOnline } }, { endTime: { [Op.gt]: bufferEndOnline } }],
			interviewType: INTERVIEW_TYPE.ONLINE,
		});
	});
	const registeredEventsOnline = await db.registrations.findAll({
		where: {
			cancelledAt: null,
			memberId,
			interviewStatus: MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED,
		},
		include: [
			{
				model: db.occurrenceDetailModel,
				required: true,
				where: { interviewType: INTERVIEW_TYPE.ONLINE },
			},
		],
	});
	const bufferTimesOnline = registeredEventsOnline.map((reg) => {
		if (!reg?.occurrenceDetail) return;
		const startTimeBuffer = bufferEventTimeSetting.startTime;
		const endTimeBuffer = bufferEventTimeSetting.endTime;
		const bufferStartOnline = moment(reg.occurrenceDetail.endTime, TIME_FORMAT)
			.add(endTimeBuffer, 'minutes')
			.format(TIME_FORMAT);
		const bufferEndOnline = moment(reg.occurrenceDetail.startTime, TIME_FORMAT)
			.subtract(startTimeBuffer, 'minutes')
			.format(TIME_FORMAT);
		return {
			eventDay: reg.occurrenceDetail.eventDay,
			[Op.and]: [{ startTime: { [Op.lt]: bufferStartOnline } }, { endTime: { [Op.gt]: bufferEndOnline } }],
			interviewType: INTERVIEW_TYPE.OFFLINE,
		};
	});
	return [...bufferTimesOffline, ...registeredTimes, ...bufferTimesOnline];
};

// 1) Hàm sinh danh sách buffer times
// 1) Sinh danh sách buffer times (không thay đổi)
export const getBufferTimesV2 = async (memberId: number) => {
	const bufferEventTimeSetting = await BufferEventTimeSettingService.getBufferEventTimeSetting();
	const registeredEventsAllType = await db.registrations.findAll({
		where: {
			cancelledAt: null,
			memberId,
			interviewStatus: MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED,
		},
		include: [
			{
				model: db.occurrenceDetailModel,
				required: true,
				attributes: ['eventDay', 'startTime', 'endTime', 'interviewType'],
			},
		],
		attributes: ['registrationId'],
	});

	const registeredTimes = registeredEventsAllType
		.filter((reg) => reg.occurrenceDetail)
		.map((reg) => {
			const { eventDay, startTime, endTime } = reg.occurrenceDetail as any;
			return { eventDayQuery: eventDay, startTimeBuffer: startTime, endTimeBuffer: endTime };
		});

	if (!bufferEventTimeSetting) return registeredTimes;

	const { startTime: startBuffer, endTime: endBuffer } = bufferEventTimeSetting;
	const totalBuffer = startBuffer + endBuffer;

	const bufferTimes = registeredEventsAllType
		.filter((reg) => reg.occurrenceDetail)
		.flatMap((reg) => {
			const { interviewType, eventDay, startTime, endTime } = reg.occurrenceDetail as any;
			const buffers: Array<{
				eventDayQuery: string;
				startTimeBuffer: string;
				endTimeBuffer: string;
				interviewTypeQuery: INTERVIEW_TYPE;
			}> = [];

			const matchTypes = [INTERVIEW_TYPE.ONLINE, INTERVIEW_TYPE.OFFLINE] as const;

			for (const compareType of matchTypes) {
				let before = 0;
				let after = 0;

				if (interviewType === INTERVIEW_TYPE.ONLINE && compareType === INTERVIEW_TYPE.ONLINE) {
					before = totalBuffer;
					after = totalBuffer;
				} else if (interviewType === INTERVIEW_TYPE.ONLINE && compareType === INTERVIEW_TYPE.OFFLINE) {
					before = endBuffer;
					after = startBuffer;
				} else if (interviewType === INTERVIEW_TYPE.OFFLINE && compareType === INTERVIEW_TYPE.OFFLINE) {
					before = totalBuffer;
					after = totalBuffer;
				} else if (interviewType === INTERVIEW_TYPE.OFFLINE && compareType === INTERVIEW_TYPE.ONLINE) {
					before = startBuffer;
					after = endBuffer;
				}

				buffers.push({
					eventDayQuery: eventDay,
					startTimeBuffer: formatTimeSub(startTime, before),
					endTimeBuffer: formatTimeAdd(endTime, after),
					interviewTypeQuery: compareType,
				});
			}

			return buffers;
		});

	return [...registeredTimes, ...bufferTimes];
};

export const getRegistrationForCSV = async (include: IncludeOptions | IncludeOptions[], where: WhereAttributeHash) =>
	db.registrations.findAll({ where: where, include: include });

export const getRegistration = async (registrationId: number, memberId?: number, transaction?: Transaction) =>
	db.registrations.findOne({
		where: {
			registrationId: registrationId,
			memberId: memberId,
		},
		include: [
			{
				association: db.registrations.associations.Member,
				attributes: { exclude: ['lineId', 'curRM'] },
			},
			{
				association: db.registrations.associations.Occurrence,
			},
			{
				association: db.registrations.associations.Occasion,
			},
			{
				association: db.registrations.associations.Category,
			},
		],
		transaction,
	});
export const getCampaignRegistrations = async (campaignId: number, transaction?: Transaction) =>
	db.registrations.findAll({
		where: {
			campaignId: campaignId,
		},
		include: [
			{
				association: db.registrations.associations.Member,
				attributes: [
					'memberId',
					'memberCode',
					'lineId',
					'displayName',
					'picUrl',
					'firstName',
					'lastName',
					'firstNameKana',
					'lastNameKana',
					'email',
					'telephone',
					'postalCode',
					'building',
					'address',
					'memberSince',
					'curRM',
					'isCampaign',
					'candidateAt',
					'isRegistered',
					'isFriends',
					'unreadCount',
					'createdAt',
					'updatedAt',
					'customerRegistrationId1',
					'customerRegistrationId2',
				],
			},
			{
				association: db.registrations.associations.memberGifts,
				include: [
					{
						association: db.memberGifts.associations.Gift,
					},
				],
			},
		],
		transaction,
	});

export const getAttended = async (memberId?: number) => {
	const dataRegistrations = await db.registrations.findAll({
		where: {
			memberId,
			campaignId: null,
		},
		include: [
			{
				association: db.registrations.associations.Category,
				paranoid: false,
				attributes: ['isSettingTime', 'startDate', 'endDate', 'categoryId', 'title'],
			},
			{
				association: db.registrations.associations.Occasion,
				paranoid: false,
				attributes: [],
			},
			{
				association: db.registrations.associations.Occurrence,
				paranoid: false,
				attributes: ['startAt', 'endAt', 'startDate'],
			},
			{
				association: db.registrations.associations.Member,
				attributes: [],
			},
		],
		paranoid: false,
		order: [['createdAt', 'DESC']],
		attributes: ['cancelledAt', 'attended', [Sequelize.literal('`Occasion`.`title`'), 'nameProgram']],
	});

	if (dataRegistrations == null) {
		throw new AppError(SYSTEM_ERROR, `registration ${memberId} does not exist`, false);
	}

	const data = dataRegistrations?.map((item) => {
		let action = '';
		let startAt = null;
		let endAt = null;

		if (item?.cancelledAt) {
			action = '当日キャンセル';
		} else if (item?.attended) {
			action = 'イベント参加';
		} else if (
			moment().isSameOrAfter(
				moment(
					`${moment(item?.occurrence?.startDate).format('YYYY-MM-DD')} ${moment(item?.occurrence?.startAt).format(
						'HH:mm',
					)}`,
				),
			)
		) {
			action = '不参加';
		} else {
			action = '予約';
		}

		if (item?.Category?.isSettingTime) {
			startAt = item?.Category?.startDate;
			endAt = item?.Category?.endDate;
		} else {
			startAt = item?.occurrence?.startAt;
			endAt = item?.occurrence?.endAt;
		}

		return {
			id: item?.Category?.categoryId,
			date: item?.occurrence?.startDate,
			startAt,
			endAt,
			nameEvent: item?.Category?.title,
			action,
			nameProgram: _.get(item.toJSON(), 'nameProgram'),
		};
	});

	return data;
};

export const getCampaignsAttended = async (memberId: number) => {
	return db.registrations.findAll({
		where: { categoryId: null, memberId },
		include: [
			{
				model: db.campaigns,
				attributes: [],
				// include: [
				// 	{
				// 		model: db.campaignQuestions
				// 		// attributes: []
				// 	}
				// ]
			},
			{
				model: db.memberGifts,
				attributes: [
					//
					[Sequelize.literal('`memberGifts->Gift`.`title`'), 'name'],
					[Sequelize.literal('`memberGifts->Gift`.`giftId`'), 'id'],
				],
				include: [
					{
						model: db.gifts,
						attributes: [],
					},
				],
			},
		],
		order: [['createdAt', 'DESC']],
		attributes: [
			//
			'note',
			'campaignId',
			'createdAt',
			[Sequelize.literal('`Campaign`.`title`'), 'title'],
		],
	});
};

export const editRegistration = async (
	editParams: CreationAttributes<Registration> & CreationAttributes<Member>,
	transaction?: Transaction,
) => {
	const registration = await db.registrations.findOne({
		where: { registrationId: editParams.registrationId },
		include: [
			{
				association: db.registrations.associations.Member,
				required: false,
			},
			{
				association: db.registrations.associations.Occasion,
				attributes: ['occasionId'],
			},
		],
		transaction,
	});
	if (registration == null) {
		throw new AppError(SYSTEM_ERROR, `registration ${editParams.registrationId} not found`, false);
	}
	const member = registration.Member;
	if (member && member.memberCode == null) {
		// let manualMemberUpdateParams=
		member.set({
			address: editParams.address,
			building: editParams.building,
			firstName: editParams.firstName,
			firstNameKana: editParams.firstNameKana,
			lastName: editParams.lastName,
			lastNameKana: editParams.lastNameKana,
			postalCode: editParams.postalCode,
			email: editParams.email,
			telephone: editParams.telephone,
		});
		if (member.changed()) {
			await member.save({ transaction });
		}
	}
	const registrationUpdateParams = {
		remarks: editParams.remarks,
		message: editParams.message,
	};
	await registration.update(registrationUpdateParams, { transaction });
	const { memberId, occurrenceId, occasionId, categoryId } = registration;
	return { memberId, occurrenceId, occasionId, categoryId } as registrationEmitType;
};

export const updateMemberRegistrations = async (
	{
		updateParams,
		registrationIds,
	}: { updateParams: { remarks: string | null; message: string | null }; registrationIds: number[] },
	transaction?: Transaction,
) =>
	db.registrations.update(
		{ message: updateParams.message, remarks: updateParams.remarks },
		{ where: { registrationId: { [Op.in]: registrationIds } }, transaction },
	);

export const cancelRegistration = async (data: MemberCancelledRegistrationSchema, transaction?: Transaction) => {
	const { memberLine } = data;
	const registrationId = parseInt(data.registrationId);
	const member = await MemberService.findMemberByLineProfile(memberLine);

	const memberId = member.memberId;

	const registration = await db.registrations.findByPk(registrationId, {
		include: [
			{
				model: Occurrence,
				attributes: ['occurrenceId', 'occasionId', 'startAt', 'responseDeadlineDate', 'interviewStep'],
			},
			{
				model: OccurrenceDetail,
			},
		],
		attributes: [
			'registrationId',
			'attended',
			'updatedAt',
			'memberId',
			'occurrenceId',
			'occasionId',
			'categoryId',
			'expected',
			'occurrenceDetailId',
		],
		transaction,
	});
	const occasionId = registration?.occasionId;

	const occasionMember = await OccasionMemberService.getOccasionMemberByConditionWhere({
		memberId,
		occasionId,
	});

	if (!registration?.occurrence || !occasionMember) {
		throw new AppError(BAD_REQUEST, `registration ${registrationId} does not exist ${CUSTOM_SHOW_MESSAGE}`, false);
	}
	const isBeforeDate = diffDayNowWithFormat(registration?.occurrence?.responseDeadlineDate);

	if (occasionMember?.memberInterviewCurrentStep !== registration?.occurrence?.interviewStep || isBeforeDate < 0) {
		throw new AppError(BAD_REQUEST, `キャンセル不可 ${CUSTOM_SHOW_MESSAGE}`);
	}

	await registration.update({ cancelledAt: new Date() }, { transaction });
	await OccasionMemberService.updateOccasionMemberByConditionWhere(
		{
			memberId,
			occasionId,
		},
		{
			interviewStatus: MEMBER_INTERVIEW_STATUS.SCHEDULE_ADJUSTMENT,
		},
	);

	await OccurrenceDetailService.incrementTotalCancelledOccurrenceDetail(registration.occurrenceDetailId, transaction);
	await OccurrenceDetailService.decrementTotalAttendeeOccurrenceDetail(registration.occurrenceDetailId, transaction);

	ReminderService.reservationCancelMessage(registrationId);

	// TODO: send Line message and deploy reminder
	// const memberInfo = member.memberId ? member.toJSON() : (member as registrationMemberInfoType);
	//
	// const nameMember = memberInfo?.customerRegistrationId1;
	// const phoneNumber = memberInfo?.customerRegistrationId2;
	//
	// const registrationSpectators = await SpectatorService.listSpectatorsByWatch('registration', transaction);
	// if (registrationSpectators.length > 0) {
	// 	const spectatorLineIds = registrationSpectators.map((mS) => mS.Member.lineId as string);
	// 	const watchMessageTemplate = await SettingService.getSpectatorNotificationTemplate(
	// 		WATCH_MESSAGE_KEY_REGISTRATION_CANCEL,
	// 	); //REGISTRATION_WATCH_MESSAGE;
	// 	if (watchMessageTemplate && watchMessageTemplate.valueString) {
	// 		let watchMessage = watchMessageTemplate.valueString.replace(
	// 			replacerName,
	// 			`${nameMember ?? ''}`,
	// 			// `${member.lastName} ${member.firstName}`
	// 		);
	// 		watchMessage = watchMessage.replace(replacerBuilding, registrationResult.categoryTitle);
	// 		watchMessage = watchMessage.replace(
	// 			replacerTelephone,
	// 			`${phoneNumber ?? ''}`,
	// 			//  `${member.telephone ?? ''}`
	// 		);
	// 		if (registrationResult.startAt) {
	// 			watchMessage = watchMessage.replace(
	// 				replacerDateTime,
	// 				registrationResult.startAt ? moment(registrationResult.startAt).format('YYYY年MM月DD日HH時mm分') : '',
	// 			);
	// 		}
	//
	// 		await LineService.sendMulticastMessage(spectatorLineIds, watchMessage).catch((err) =>
	// 			writeLog(`failed to send multicast message member watch ${err.message}`, 'info'),
	// 		);
	// 	}
	// }
	// await ReminderService.destroyReminderByRegistrationId(registrationId, transaction);

	return {
		memberId: registration.memberId as number,
		occurrenceId: registration.occurrenceId,
		registrationId: registration.registrationId,
		occurrenceDetailId: registration.occurrenceDetailId,
	};
};

export const deleteRegistration = async (registrationId: number, transaction?: Transaction) => {
	let socketData: registrationEmitType;
	return db.registrations
		.findByPk(registrationId, {
			paranoid: false,
			transaction,
		})
		.then((registration) => {
			if (registration == null) {
				throw new AppError(SYSTEM_ERROR, `registration ${registrationId} does not exist`);
			} else {
				socketData = {
					memberId: registration.memberId as number,
					categoryId: registration.categoryId,
					occasionId: registration.occasionId,
					occurrenceId: registration.occurrenceId,
				};
				return registration.destroy({ transaction });
			}
		})
		.then(() => socketData);
};
export const listMemberRegistrations = async (
	registrationWhere: { memberId?: number; registrationId?: string },
	occurrenceWhere: WhereAttributeHash,
	transaction?: Transaction,
) =>
	db.registrations.findAll({
		where: { ...registrationWhere, campaignId: null },
		include: [
			// {
			// 	association: db.registrations.associations.Occurrence,
			// 	attributes: ['occurrenceId', 'startAt', 'startDate', 'endAt', 'occasionId', 'isSettingTime'],
			// 	where: {
			// 		[Op.or]: [
			// 			{
			// 				isSettingTime: true,
			// 				startDate: { [Op.gte]: moment().startOf('days').toDate() },
			// 			},
			// 			{
			// 				isSettingTime: false,
			// 				...occurrenceWhere,
			// 			},
			// 		],
			// 	},
			// 	order: [[db.occurrences, col('startAt'), 'asc']],
			// },
			// {
			// 	association: db.occurrences.associations.Occasion,
			// 	attributes: [
			// 		//
			// 		'title',
			// 		'occasionId',
			// 		'isMultiEvent',
			// 		'startDate',
			// 		'endDate',
			// 		'isSettingTime',
			// 		'fee',
			// 		'cancelDescription',
			// 		'cancelable',
			// 	],
			// 	include: [
			// 		{
			// 			model: db.occasionCancelConditions,
			// 			attributes: ['day', 'hour', 'minute', 'refundPercentage'],
			// 			separate: true,
			// 		},
			// 	],
			// },
			// {
			// 	association: db.occasions.associations.Category,
			// 	attributes: [
			// 		'title',
			// 		'categoryId',
			// 		'isMultiEvent',
			// 		'startDate',
			// 		'endDate',
			// 		'isSettingTime',
			// 		'type',
			// 		'cancelDescription',
			// 		'cancelable',
			// 	],
			// 	include: [
			// 		{
			// 			model: db.categoryCancelConditions,
			// 			attributes: ['day', 'hour', 'minute', 'refundPercentage'],
			// 			separate: true,
			// 		},
			// 	],
			// },
		],
		transaction,
	});
export const listMemberCampaignRegistrations = async (
	registrationWhere: { memberId?: number; registrationId?: string },
	occurrenceWhere: WhereAttributeHash,
	transaction?: Transaction,
) =>
	db.registrations.findAll({
		where: { ...registrationWhere, categoryId: null },
		include: [
			{
				association: db.registrations.associations.Occurrence,
				attributes: ['occurrenceId', 'startAt', 'startDate', 'endAt', 'occasionId', 'isSettingTime'],
				where: {
					...occurrenceWhere,
				},
				order: [[db.occurrences, col('startAt'), 'asc']],
			},
			{
				association: db.gifts.associations.Campaign,
				attributes: ['title', 'campaignId', 'isMultiEvent'],
			},
		],
		transaction,
	});

export const getRegistration_Member = async (registrationId: number, memberId: number, transaction?: Transaction) =>
	db.registrations.findOne({
		where: { registrationId: registrationId, memberId: memberId },
		include: [
			{
				association: db.registrations.associations.Occurrence,
				order: [[db.occurrences, col('startAt'), 'asc']],
			},
			{
				association: db.occurrences.associations.Occasion,
				include: [
					{
						separate: true,
						association: db.occasions.associations.occasionDetails,
						attributes: { exclude: ['occasionDetailId', 'occasionId'] },
					},
					{
						separate: true,
						association: db.occasions.associations.occasionImages,
						attributes: { exclude: ['occasionImageId', 'occasionId'] },
					},
				],
				order: [[db.occasionDetails, col('showOrder'), 'asc']],
			},
			{
				association: db.occasions.associations.Category,
				include: [
					{
						separate: true,
						association: db.categories.associations.cancelConditions,
						attributes: ['day', 'hour', 'minute', 'refundPercentage'],
					},
					{
						separate: true,
						association: db.categories.associations.categoryAreas,
						attributes: { exclude: ['categoryAreaId', 'categoryId'] },
					},
					{
						separate: true,
						association: db.categories.associations.categoryDetails,
						attributes: { exclude: ['categoryDetailId', 'categoryId'] },
					},
					{
						separate: true,
						association: db.categories.associations.categoryImages,
						attributes: { exclude: ['categoryImageId', 'categoryId'] },
					},
					{
						separate: true,
						association: db.categories.associations.categoryTags,
						attributes: { exclude: ['categoryTagId', 'categoryId'] },
					},
				],
				order: [
					[db.occasionImages, col('showOrder'), 'asc'],
					[db.categoryAreas, col('showOrder'), 'asc'],
					[db.categoryDetails, col('showOrder'), 'asc'],
					[db.categoryImages, col('showOrder'), 'asc'],
					[db.categoryTags, col('showOrder'), 'asc'],
				],
			},
		],
		transaction,
	});
export const getRegistrationMember = async (registrationId: number, memberId: number, transaction?: Transaction) =>
	db.registrations.findOne({
		where: { registrationId: registrationId, memberId: memberId },
		include: [
			{
				association: db.registrations.associations.Occurrence,
				order: [[db.occurrences, col('startAt'), 'asc']],
			},
			{
				association: db.registrations.associations.memberGifts,
				attributes: ['giftId'],
				include: [
					{
						association: db.memberGifts.associations.Gift,
						attributes: ['type', 'title'],
					},
				],
			},
			{
				association: db.gifts.associations.Campaign,
				include: [
					{
						separate: true,
						association: db.campaigns.associations.categoryAreas,
						attributes: { exclude: ['categoryAreaId', 'categoryId', 'campaignId'] },
					},
					{
						separate: true,
						association: db.campaigns.associations.categoryDetails,
						attributes: { exclude: ['categoryDetailId', 'categoryId', 'campaignId'] },
					},
					{
						separate: true,
						association: db.campaigns.associations.categoryImages,
						attributes: { exclude: ['categoryImageId', 'categoryId', 'campaignId'] },
					},
					{
						separate: true,
						association: db.campaigns.associations.categoryTags,
						attributes: { exclude: ['categoryTagId', 'categoryId', 'campaignId'] },
					},
				],
				order: [
					[db.occasionImages, col('showOrder'), 'asc'],
					[db.categoryAreas, col('showOrder'), 'asc'],
					[db.categoryDetails, col('showOrder'), 'asc'],
					[db.categoryImages, col('showOrder'), 'asc'],
					[db.categoryTags, col('showOrder'), 'asc'],
				],
			},
		],
		transaction,
	});

export const generateRegistrationDataForCSV = async ({
	registrationWhere,
	occasionWhere,
	categoryWhere,
	customerRegistrations,
	occurrenceWhere,
}: {
	registrationWhere: WhereAttributeHash;
	occasionWhere: WhereAttributeHash;
	categoryWhere: WhereAttributeHash;
	occurrenceWhere: WhereAttributeHash;
	customerRegistrations: CustomerRegistration[];
}) => {
	return db.registrations.findAll({
		where: registrationWhere,
		attributes: [
			[col('Registration.memberId'), 'memberId'],
			[col('Registration.message'), 'message'],
			[col('Member.memberCode'), 'memberCode'],
			[col('Member.displayName'), 'displayName'],
			[col('Member.firstName'), 'firstName'],
			[col('Member.lastName'), 'lastName'],
			[col('Member.firstNameKana'), 'firstNameKana'],
			[col('Member.lastNameKana'), 'lastNameKana'],
			[col('Member.email'), 'email'],
			[col('Member.telephone'), 'telephone'],
			[col('Member.postalCode'), 'postalCode'],
			[col('Member.building'), 'building'],
			[col('Member.address'), 'address'],
			[col('Member.memberSince'), 'memberSince'],
			[col('Member.isCampaign'), 'isCampaign'],
			[col('Member.candidateAt'), 'candidateAt'],
			[col('Member.isFriends'), 'isFriends'],
			[col('`Occasion->Category`.`title`'), 'categoryTitle'],
			[col('Occasion.title'), 'occasionTitle'],
			[
				Sequelize.literal(
					// eslint-disable-next-line quotes
					"CONCAT(`occurrenceDetail`.`eventDay`, ' ', `occurrenceDetail`.`startTime`)",
				),
				'startAt',
			],
			[col('Member.notes'), 'notes'],
			[col('Member.countVisit'), 'countVisit'],
			[col('Member.lastVisit'), 'lastVisit'],
			[col('Member.currentPoints'), 'currentPoints'],
			[col('Member.createdAt'), 'createdAt'],
			[col('Member.activeUntil'), 'activeUntil'],
			[col('Member.lineId'), 'lineId'],
			...customerRegistrations.map(
				({ customerRegistrationId }) =>
					[
						col(`Member.customerRegistrationId${customerRegistrationId}`),
						`customerRegistrationId${customerRegistrationId}`,
					] as ProjectionAlias,
			),
		],
		include: [
			{
				association: db.registrations.associations.Member,
				required: true,
				attributes: [],
			},
			{
				association: db.registrations.associations.occurrenceDetail,
				required: true,
				attributes: [],
				where: occurrenceWhere,
			},
			{
				association: db.registrations.associations.Occasion,
				required: true,
				where: occasionWhere,
				attributes: [],
				include: [
					{
						association: db.occasions.associations.Category,
						required: true,
						attributes: [],
						where: categoryWhere,
					},
				],
			},
		],
		raw: true,
		nest: true,
	});
};

export const memberRegisterForEvent = async (
	{
		occurrenceId,
		member,
		message = '',
		dataNote = null,
		isManual = false,
		participantName,
		participantCount = 0,
		companionCount = 0,
		timeZone,
	}: {
		occurrenceId: number;
		member: Member;
		message?: string;
		dataNote?: any;
		isManual?: boolean;
		participantName?: string;
		participantCount?: number;
		companionCount?: number;
		timeZone?: string;
	},
	transaction?: Transaction,
) => {
	const memberInfo = member.memberId ? member.toJSON() : (member as registrationMemberInfoType);
	const occurrence = await db.occurrences.findByPk(occurrenceId, {
		include: [
			{
				association: db.occurrences.associations.registrations,
				attributes: ['expected'],
			},
			{
				association: db.occurrences.associations.Occasion,
				attributes: [
					'occasionId',
					'categoryId',
					'title',
					'isMultiEvent',
					'startDate',
					'endDate',
					'isSettingTime',
					'notRegisterEventSameTime',
					'startRegistration',
					'endRegistration',
					'message',
					'fee',
					'groupBooking',
					'isMessage',
				],
				include: [
					{
						model: db.categoryMessageDetails,
					},
				],
			},
			{
				association: db.occurrences.associations.Category,
				attributes: [
					'title',
					'isMultiEvent',
					'startDate',
					'endDate',
					'isSendImage',
					'isSettingTime',
					'notRegisterEventSameTime',
					'startRegistration',
					'endRegistration',
					'isProgram',
					'fee',
					'groupBooking',
					'isMessage',
				],
				include: [
					{
						association: db.categories.associations.categoryImages,
					},
					{
						association: db.categories.associations.categoryDetails,
					},
					{
						association: db.categories.associations.categoryMessages,
					},
					{
						model: db.categoryMessageDetails,
					},
				],
			},
		],
	});

	if (occurrence == null) {
		throw new AppError(SYSTEM_ERROR, `occurrence ${occurrenceId} does not exist`);
	}

	const totalClientAttendee = occurrence.Category?.groupBooking ? participantCount + companionCount : 1;

	const sum = await redisCacheService.increaseOccurrenceRegisterCount(occurrenceId, totalClientAttendee);

	const fee = parseFloat(`${occurrence?.Occasion?.fee || occurrence?.Category?.fee}`);

	let OccurrenceTransaction;

	if (fee > 0) {
		OccurrenceTransaction = await db.transaction.findOne({
			where: {
				memberId: member.memberId!,
				occurrenceId,
				amount: fee,
			},
			raw: true,
			order: [['createdAt', 'DESC']],
		});

		if (
			!OccurrenceTransaction ||
			OccurrenceTransaction.type !== PAYMENT_TYPE.PURCHASE ||
			OccurrenceTransaction.status !== PAYMENT_STATUS.FULFILLED
		) {
			throw new AppError(BAD_REQUEST, 'ご入金後にご登録ください。', true);
		}
	}

	if (
		!isManual &&
		// occurrence.maxAttendee < sum + (occurrence.Category?.groupBooking ? participantCount + companionCount : 1)
		occurrence.maxAttendee < sum
	) {
		throw new AppError(CONFLICT_ERROR, `occurrence ${occurrenceId} is full: ${totalClientAttendee}`);
	}

	const registrations: any = await db.registrations.findAll({
		where: {
			memberId: member.memberId,
			categoryId: {
				[Op.not]: null,
			},
		},
		include: [
			{
				association: db.registrations.associations.Category,
			},
			{
				association: db.registrations.associations.Occasion,
			},
			{
				association: db.registrations.associations.Occurrence,
			},
		],
	});

	if (occurrence?.Category?.isProgram) {
		if (occurrence?.Occasion?.isSettingTime && moment(occurrence?.startDate).isBefore(moment())) {
			throw new AppError(SYSTEM_ERROR, 'startDate isBefore date now');
		}

		const occurrenceStartDate = moment(occurrence?.Occasion?.startDate);
		const occurrenceEndDate = moment(occurrence?.Occasion?.endDate);

		const occurrenceStartRegistration = moment(occurrence?.Occasion?.startRegistration);
		const occurrenceEndRegistration = moment(occurrence?.Occasion?.endRegistration);

		const occurrenceStartAt = moment(occurrence?.startAt);
		const occurrenceEndAt = moment(occurrence?.endAt);

		if (moment().isBefore(occurrenceStartRegistration) || moment().isAfter(occurrenceEndRegistration)) {
			throw new AppError(SYSTEM_ERROR, 'Invalid registration time');
		}

		registrations.forEach((item: any) => {
			if (item.Category?.isProgram) {
				const startDate = moment(item?.Occasion?.startDate);
				const endDate = moment(item?.Occasion?.endDate);

				const startAt = moment(item?.Occurrence?.startAt);
				const endAt = moment(item?.Occurrence?.endAt);

				// if (item?.Occurrence?.occurrenceId === occurrenceId) {
				// 	throw new AppError(BAD_REQUEST, 'Duplicate event registrations are not allowed')
				// }

				if (occurrence?.Occasion?.isSettingTime) {
					if (item?.Occasion?.isSettingTime) {
						if (!(item?.Occasion?.isMultiEvent && occurrence?.Occasion?.isMultiEvent)) {
							if (
								occurrenceStartDate.isBetween(startDate, endDate) ||
								occurrenceEndDate.isBetween(startDate, endDate) ||
								(occurrenceStartDate.isSameOrBefore(startDate) && occurrenceStartDate.isSameOrAfter(endDate)) ||
								(occurrenceStartDate.isSame(startDate) && occurrenceStartDate.isSame(endDate))
							) {
								throw new AppError(BAD_REQUEST, '時間の重複ですので、予約できません');
							}
						}
					} else {
						if (!item?.Occasion?.isMultiEvent) {
							throw new AppError(BAD_REQUEST, '時間の重複ですので、予約できません');
						} else {
							if (item?.Occasion?.notRegisterEventSameTime) {
								if (
									occurrenceStartAt.isBetween(startAt, endAt) ||
									occurrenceEndAt.isBetween(startAt, endAt) ||
									(occurrenceStartAt.isSameOrBefore(startAt) && occurrenceEndAt.isSameOrAfter(endAt)) ||
									(occurrenceStartAt.isSame(startAt) && occurrenceEndAt.isSame(endAt))
								) {
									throw new AppError(BAD_REQUEST, '時間の重複ですので、予約できません');
								}
							}
						}
					}
				} else {
					if (!occurrence?.Occasion?.isMultiEvent) {
						throw new AppError(BAD_REQUEST, '時間の重複ですので、予約できません。');
					} else {
						if (occurrence?.Occasion?.notRegisterEventSameTime || item?.Occasion?.notRegisterEventSameTime) {
							if (
								occurrenceStartAt.isBetween(startAt, endAt) ||
								occurrenceEndAt.isBetween(startAt, endAt) ||
								(occurrenceStartAt.isSameOrBefore(startAt) && occurrenceEndAt.isSameOrAfter(endAt)) ||
								(occurrenceStartAt.isSame(startAt) && occurrenceEndAt.isSame(endAt))
							) {
								throw new AppError(BAD_REQUEST, '同時に複数のイベント予約はできません');
							}
						}
					}
				}
			} else {
				const startDate = moment(item?.Category?.startDate);
				const endDate = moment(item?.Category?.endDate);

				const startAt = moment(item?.Occurrence?.startAt);
				const endAt = moment(item?.Occurrence?.endAt);

				// if (item?.Occurrence?.occurrenceId === occurrenceId) {
				// 	throw new AppError(BAD_REQUEST, 'Duplicate event registrations are not allowed')
				// }

				if (occurrence?.Occasion?.isSettingTime) {
					if (item?.Category?.isSettingTime) {
						if (!(item?.Category?.isMultiEvent && occurrence?.Occasion?.isMultiEvent)) {
							if (
								occurrenceStartDate.isBetween(startDate, endDate) ||
								occurrenceEndDate.isBetween(startDate, endDate) ||
								(occurrenceStartDate.isSameOrBefore(startDate) && occurrenceStartDate.isSameOrAfter(endDate)) ||
								(occurrenceStartDate.isSame(startDate) && occurrenceStartDate.isSame(endDate))
							) {
								throw new AppError(BAD_REQUEST, '時間の重複ですので、予約できません');
							}
						}
					} else {
						if (!item?.Category?.isMultiEvent) {
							throw new AppError(BAD_REQUEST, '時間の重複ですので、予約できません');
						} else {
							if (item?.Category?.notRegisterEventSameTime) {
								if (
									occurrenceStartAt.isBetween(startAt, endAt) ||
									occurrenceEndAt.isBetween(startAt, endAt) ||
									(occurrenceStartAt.isSameOrBefore(startAt) && occurrenceEndAt.isSameOrAfter(endAt)) ||
									(occurrenceStartAt.isSame(startAt) && occurrenceEndAt.isSame(endAt))
								) {
									throw new AppError(BAD_REQUEST, '時間の重複ですので、予約できません');
								}
							}
						}
					}
				} else {
					if (!occurrence?.Occasion?.isMultiEvent) {
						throw new AppError(BAD_REQUEST, '時間の重複ですので、予約できません。');
					} else {
						if (occurrence?.Occasion?.notRegisterEventSameTime || item?.Category?.notRegisterEventSameTime) {
							if (
								occurrenceStartAt.isBetween(startAt, endAt) ||
								occurrenceEndAt.isBetween(startAt, endAt) ||
								(occurrenceStartAt.isSameOrBefore(startAt) && occurrenceEndAt.isSameOrAfter(endAt)) ||
								(occurrenceStartAt.isSame(startAt) && occurrenceEndAt.isSame(endAt))
							) {
								throw new AppError(BAD_REQUEST, '同時に複数のイベント予約はできません');
							}
						}
					}
				}
			}
		});
	} else {
		if (occurrence?.Category?.isSettingTime && moment(occurrence?.startDate).isBefore(moment())) {
			throw new AppError(SYSTEM_ERROR, 'startDate isBefore date now');
		}

		const occurrenceStartDate = moment(occurrence?.Category?.startDate);
		const occurrenceEndDate = moment(occurrence?.Category?.endDate);

		const occurrenceStartRegistration = moment(occurrence?.Category?.startRegistration);
		const occurrenceEndRegistration = moment(occurrence?.Category?.endRegistration);

		const occurrenceStartAt = moment(occurrence?.startAt);
		const occurrenceEndAt = moment(occurrence?.endAt);

		if (moment().isBefore(occurrenceStartRegistration) || moment().isAfter(occurrenceEndRegistration)) {
			throw new AppError(SYSTEM_ERROR, 'Invalid registration time');
		}

		registrations.forEach((item: any) => {
			if (item.Category?.isProgram) {
				const startDate = moment(item?.Occasion?.startDate);
				const endDate = moment(item?.Occasion?.endDate);

				const startAt = moment(item?.Occurrence?.startAt);
				const endAt = moment(item?.Occurrence?.endAt);

				// if (item?.Occurrence?.occurrenceId === occurrenceId) {
				// 	throw new AppError(BAD_REQUEST, 'Duplicate event registrations are not allowed')
				// }

				if (occurrence?.Category?.isSettingTime) {
					if (item?.Occasion?.isSettingTime) {
						if (!(item?.Occasion?.isMultiEvent && occurrence?.Category?.isMultiEvent)) {
							if (
								occurrenceStartDate.isBetween(startDate, endDate) ||
								occurrenceEndDate.isBetween(startDate, endDate) ||
								(occurrenceStartDate.isSameOrBefore(startDate) && occurrenceStartDate.isSameOrAfter(endDate)) ||
								(occurrenceStartDate.isSame(startDate) && occurrenceStartDate.isSame(endDate))
							) {
								throw new AppError(BAD_REQUEST, '時間の重複ですので、予約できません');
							}
						}
					} else {
						if (!item?.Occasion?.isMultiEvent) {
							throw new AppError(BAD_REQUEST, '時間の重複ですので、予約できません');
						} else {
							if (item?.Occasion?.notRegisterEventSameTime) {
								if (
									occurrenceStartAt.isBetween(startAt, endAt) ||
									occurrenceEndAt.isBetween(startAt, endAt) ||
									(occurrenceStartAt.isSameOrBefore(startAt) && occurrenceEndAt.isSameOrAfter(endAt)) ||
									(occurrenceStartAt.isSame(startAt) && occurrenceEndAt.isSame(endAt))
								) {
									throw new AppError(BAD_REQUEST, '時間の重複ですので、予約できません');
								}
							}
						}
					}
				} else {
					if (!occurrence?.Category?.isMultiEvent) {
						throw new AppError(BAD_REQUEST, '時間の重複ですので、予約できません。');
					} else {
						if (occurrence?.Category?.notRegisterEventSameTime || item?.Occasion?.notRegisterEventSameTime) {
							if (
								occurrenceStartAt.isBetween(startAt, endAt) ||
								occurrenceEndAt.isBetween(startAt, endAt) ||
								(occurrenceStartAt.isSameOrBefore(startAt) && occurrenceEndAt.isSameOrAfter(endAt)) ||
								(occurrenceStartAt.isSame(startAt) && occurrenceEndAt.isSame(endAt))
							) {
								throw new AppError(BAD_REQUEST, '同時に複数のイベント予約はできません');
							}
						}
					}
				}
			} else {
				const startDate = moment(item?.Category?.startDate);
				const endDate = moment(item?.Category?.endDate);

				const startAt = moment(item?.Occurrence?.startAt);
				const endAt = moment(item?.Occurrence?.endAt);

				// if (item?.Occurrence?.occurrenceId === occurrenceId) {
				// 	throw new AppError(BAD_REQUEST, 'Duplicate event registrations are not allowed')
				// }

				if (occurrence?.Category?.isSettingTime) {
					if (item?.Category?.isSettingTime) {
						if (!(item?.Category?.isMultiEvent && occurrence?.Category?.isMultiEvent)) {
							if (
								occurrenceStartDate.isBetween(startDate, endDate) ||
								occurrenceEndDate.isBetween(startDate, endDate) ||
								(occurrenceStartDate.isSameOrBefore(startDate) && occurrenceStartDate.isSameOrAfter(endDate)) ||
								(occurrenceStartDate.isSame(startDate) && occurrenceStartDate.isSame(endDate))
							) {
								throw new AppError(BAD_REQUEST, '時間の重複ですので、予約できません');
							}
						}
					} else {
						if (!item?.Category?.isMultiEvent) {
							// bug
							throw new AppError(BAD_REQUEST, '時間の重複ですので、予約できません');
						} else {
							if (item?.Category?.notRegisterEventSameTime) {
								if (
									occurrenceStartAt.isBetween(startAt, endAt) ||
									occurrenceEndAt.isBetween(startAt, endAt) ||
									(occurrenceStartAt.isSameOrBefore(startAt) && occurrenceEndAt.isSameOrAfter(endAt)) ||
									(occurrenceStartAt.isSame(startAt) && occurrenceEndAt.isSame(endAt))
								) {
									throw new AppError(BAD_REQUEST, '時間の重複ですので、予約できません');
								}
							}
						}
					}
				} else {
					if (!occurrence?.Category?.isMultiEvent) {
						throw new AppError(BAD_REQUEST, '時間の重複ですので、予約できません。');
					} else {
						if (occurrence?.Category?.notRegisterEventSameTime || item?.Category?.notRegisterEventSameTime) {
							if (
								occurrenceStartAt.isBetween(startAt, endAt) ||
								occurrenceEndAt.isBetween(startAt, endAt) ||
								(occurrenceStartAt.isSameOrBefore(startAt) && occurrenceEndAt.isSameOrAfter(endAt)) ||
								(occurrenceStartAt.isSame(startAt) && occurrenceEndAt.isSame(endAt))
							) {
								throw new AppError(BAD_REQUEST, '同時に複数のイベント予約はできません');
							}
						}
					}
				}
			}
		});
	}

	const deadlineSettings = await getBookingDeadline();
	if (!occurrence?.Category?.isSettingTime && !occurrence?.Occasion?.isSettingTime) {
		if (moment(occurrence?.startAt).isBefore(moment())) {
			throw new AppError(SYSTEM_ERROR, 'startAt isBefore date now');
		}
		if (!isManual && !checkIfBookingBeforeDeadline(deadlineSettings, occurrence.startAt)) {
			throw new AppError(NOT_ACCEPTABLE, 'registration deadline has passed', false);
		}
	}

	let notes: any;
	if (occurrence?.Category?.isProgram && Array.isArray(occurrence?.Category?.categoryMessages) && dataNote) {
		const arrayMessage: string[] = occurrence?.Occasion?.message
			? JSON.parse(occurrence?.Occasion?.message as any)
			: [];
		const messageKeys = arrayMessage.reduce(
			(prev, curr) => ({
				...prev,
				[curr]: curr,
			}),
			{},
		);
		const categoryMessages = occurrence?.Category?.categoryMessages;

		notes = categoryMessages
			.filter((item) => item.label in messageKeys)
			.map((item) => ({
				label: item.label,
				value: dataNote[`label${item.categoryMessageId}`],
			}));
	} else {
		if (occurrence?.Category?.categoryMessages && occurrence?.Category?.categoryMessages.length > 0 && dataNote) {
			notes = occurrence?.Category?.categoryMessages?.map((item) => ({
				label: item.label,
				value: dataNote[`label${item?.categoryMessageId}`],
			}));
		}
	}

	transaction = await db.sequelize.transaction();

	const registration = await db.registrations.create(
		{
			memberId: memberInfo.memberId ?? null,
			expected:
				occurrence.Category?.groupBooking || occurrence.Occasion?.groupBooking ? participantCount + companionCount : 1,
			categoryId: occurrence.categoryId,
			occasionId: occurrence.occasionId,
			occurrenceId: occurrence.occurrenceId,
			message: message,
			note: occurrence?.Category?.categoryMessages ? notes : null,
			isRegistered: member.isRegistered,
			isFriends: member.isFriends,
			isManual: isManual,
			...(occurrence.Category?.groupBooking || occurrence.Occasion?.groupBooking
				? {
						participantName,
						participantCount,
						companionCount,
				  }
				: {}),
		},
		{ transaction },
	);

	if (OccurrenceTransaction) {
		await db.transaction.update(
			{
				registrationId: registration.registrationId,
			},
			{
				where: {
					id: OccurrenceTransaction.id,
				},
				transaction,
			},
		);
	}

	await transaction.commit();

	const registrationResult = {
		occasionId: occurrence.occasionId,
		categoryId: occurrence.categoryId,
		memberId: member.memberId,
		startAt: occurrence.startAt,
		isSettingTime: occurrence?.Category?.isSettingTime,
		registrationId: registration.registrationId,
		categoryTitle: occurrence?.Category?.title,
		categoryImages: occurrence?.Category?.categoryImages,
		isSendImage: occurrence?.Category?.isSendImage,
		isMessage: occurrence?.Category?.isProgram ? occurrence?.Occasion?.isMessage : occurrence?.Category?.isMessage,
		afterReservationMessage: occurrence?.Category?.isProgram
			? occurrence?.Occasion?.CategoryMessageDetail?.afterReservationMessage
			: occurrence?.Category?.CategoryMessageDetail?.afterReservationMessage,
		reminderMessageOneDay: occurrence?.Category?.isProgram
			? occurrence?.Occasion?.CategoryMessageDetail?.reminderMessageOneDay
			: occurrence?.Category?.CategoryMessageDetail?.reminderMessageOneDay,
		reminderMessageThreeDays: occurrence?.Category?.isProgram
			? occurrence?.Occasion?.CategoryMessageDetail?.reminderMessageThreeDays
			: occurrence?.Category?.CategoryMessageDetail?.reminderMessageThreeDays,
	} as ResMemberRegistrationEvent;

	if (!isManual && timeZone) {
		const messagesToClient = await SettingService.getRegistrationAndReminderMessages();

		const image: any = registrationResult?.categoryImages?.find((item: any) => item.showOrder === 0);

		if (member.lineId && registrationResult?.isSendImage && image?.picUrl) {
			const url = process.env.HOST
				? `${process.env.HOST}/uploads/categories/${image?.picUrl}`
				: `${systemConfig.PATH_FILE_UPLOAD_CATEGORY}/${image?.picUrl}`;

			await LineService.sendImageMessage(member.lineId, url);
		}

		const nameMember = get(memberInfo, 'customerRegistrationId1', '') as string;
		const phoneMember = get(memberInfo, 'customerRegistrationId2', '') as string;

		// cach 1
		if (member.lineId) {
			await LineService.sendLineAfterRegistrationEvent({
				registrationResult,
				messagesToClient,
				nameMember,
				memberLineId: member.lineId,
			});
		}

		await ReminderService.createMessageReminderEvent({
			messagesToClient,
			registrationResult,
			memberId: member.memberId,
			nameMember,
			timeZone,
			replacerName,
			replacerBuilding,
			replacerConfirmationUrl,
			replacerDateTime,
			replacerTelephoneCompany,
		});

		await sendLineToAdminAfterRegistrationEvent({
			registrationResult,
			nameMember,
			phoneMember,
		});

		SocketServerService.emitRegistration({
			memberId: member.memberId,
			categoryId: registrationResult.categoryId,
			occasionId: registrationResult.occasionId,
			occurrenceId,
		});
	}

	return registrationResult;
};

export const memberRegisterForCampaignEvent = async (
	{
		occurrenceId,
		member,
		message = '',
		dataNote = null,
		isManual = false,
		dataFiles,
	}: {
		occurrenceId: number;
		member: Member;
		message?: string;
		dataNote?: any;
		isManual?: boolean;
		dataFiles?: any;
	},
	transaction?: Transaction,
) => {
	const memberInfo = member.memberId ? member.toJSON() : (member as registrationMemberInfoType);

	const occurrence = await db.occurrences.findByPk(occurrenceId, {
		include: [
			{
				association: db.occurrences.associations.registrations,
				attributes: ['expected'],
			},
			{
				required: true,
				association: db.occurrences.associations.Campaign,
				attributes: ['title', 'isMultiEvent', 'startRegistration', 'endRegistration', 'isRegisterMultipleTimes'],
				include: [
					{
						association: db.campaigns.associations.categoryImages,
					},
					{
						association: db.campaigns.associations.categoryDetails,
					},
				],
			},
		],
		transaction,
	});

	if (occurrence == null) {
		throw new AppError(SYSTEM_ERROR, `occurrence ${occurrenceId} does not exist`);
	}
	const registrations: any = await db.registrations.findAll({
		where: {
			memberId: member.memberId,
			campaignId: {
				[Op.not]: null,
			},
		},
		include: [
			{
				association: db.registrations.associations.Campaign,
			},
			{
				association: db.registrations.associations.Occasion,
			},
			{
				association: db.registrations.associations.Occurrence,
			},
		],
	});
	if (moment(occurrence?.endAt).isBefore(moment())) {
		throw new AppError(CONFLICT_ERROR, 'endAt isBefore date now');
	}

	const occurrenceStartRegistration = moment(occurrence?.Campaign?.startRegistration);
	const occurrenceEndRegistration = moment(occurrence?.Campaign?.endRegistration);

	if (moment().isBefore(occurrenceStartRegistration) || moment().isAfter(occurrenceEndRegistration)) {
		throw new AppError(SYSTEM_ERROR, 'Invalid registration time');
	}

	registrations.forEach((item: any) => {
		if (!occurrence?.Campaign?.isRegisterMultipleTimes && item?.occurrenceId == occurrenceId) {
			throw new AppError(BAD_REQUEST, 'Duplicate event registrations are not allowed');
		}
		if (!item?.Campaign?.isMultiEvent) {
			throw new AppError(BAD_REQUEST, '時間の重複ですので、予約できません');
		}
	});

	const sum = occurrence.registrations!.reduce((p, c) => (p += c.expected), 0);
	if (!isManual && occurrence.maxAttendee < sum + 1) {
		throw new AppError(CONFLICT_ERROR, `occurrence ${occurrenceId} is full`);
	}

	const questions = await db.campaignQuestions.findAll({
		where: {
			campaignId: occurrence?.campaignId,
		},
		include: {
			association: db.campaignQuestions.associations.campaignChoices,
		},
		order: [['showOrder', 'asc']],
	});
	const dataCampaignQuestions: any = {};

	!isManual &&
		questions?.forEach((item) => {
			const column = `campaignQuestionId${item?.campaignQuestionId}`;

			const dataColumn = dataNote[column];

			if (item?.required && !dataColumn) {
				throw new AppError(BAD_REQUEST, 'required is not null');
			}

			switch (item.type) {
				case 'checkbox': {
					const selectId = dataColumn;

					let campaignChoiceContents: any;
					if (selectId) {
						const selectCheckbox = item.campaignChoices?.filter((item) => {
							return selectId.includes(item.campaignChoiceId);
						});

						campaignChoiceContents = selectCheckbox?.map((i: any) => i.contents);
					}

					dataCampaignQuestions[column] = campaignChoiceContents?.length ? campaignChoiceContents?.join(', ') : null;

					break;
				}
				case 'image': {
					let files: any;
					if (Array.isArray(dataFiles)) {
						const name = dataColumn;

						if (name) {
							files = dataFiles.find((item) => item?.originalname === name);
						}
					}
					dataCampaignQuestions[column] = files ? files.filename : null;

					break;
				}
				default:
					throw new AppError(SYSTEM_ERROR, 'data error', false);
			}
		});

	const registration = await db.registrations.create(
		{
			memberId: memberInfo.memberId ?? null,
			expected: 1,
			campaignId: occurrence.campaignId,
			occasionId: occurrence.occasionId,
			occurrenceId: occurrence.occurrenceId,
			message: message,
			note: dataCampaignQuestions,
			isRegistered: member.isRegistered,
			isFriends: member.isFriends,
			isManual: isManual,
		},
		{ transaction },
	);

	return {
		occasionId: occurrence.occasionId,
		campaignId: occurrence.campaignId,
		memberId: member.memberId,
		startAt: occurrence.startAt,

		registrationId: registration.registrationId,
		categoryTitle: occurrence?.Campaign?.title,
		categoryImages: occurrence?.Campaign?.categoryImages,
	} as registrationEmitType & {
		startAt: Date;
		registrationId: number;
		categoryTitle: string;
		isSettingTime?: boolean;
		categoryImages?: string[];
	};
};

export const updateIsNotified = async (
	registrationId: number,
	type: Partial<REMINDER_NOTIFY_TYPES>,
	transaction?: Transaction,
) => {
	let dataUpdate: Record<string, Date> = { isNotified1: new Date() };
	if (type === REMINDER_NOTIFY_TYPES.another_day) {
		dataUpdate = { isNotified2: new Date() };
	}
	return db.registrations.update(dataUpdate, {
		where: { registrationId },
		transaction,
	});
};

const checkIfBookingBeforeDeadline = (deadlineSettings: bookingDeadlineType | null, startTime: Date) => {
	if (deadlineSettings == null || deadlineSettings.isEnabled == false) {
		return true;
	}
	const now = moment();
	const deadline = moment(startTime).subtract(deadlineSettings.days, 'days').set({
		hour: deadlineSettings.hours,
		minute: deadlineSettings.minutes,
		second: 0,
	});
	return now.isBefore(deadline);
};

export const updateIsWin = (listId: number[], campaignId: number, transaction?: Transaction) => [
	db.registrations.update(
		{ isWin: true },
		{
			where: {
				registrationId: {
					[Op.in]: listId,
				},
				campaignId,
			},
			transaction,
		},
	),
	db.registrations.findAll({
		where: {
			registrationId: {
				[Op.in]: listId,
			},
			campaignId: campaignId,
			isWin: false,
		},
		include: {
			association: db.registrations.associations.Member,
		},
		transaction,
	}),
];

type TConfirmationRegistrationData = {
	registrationId: number;
	actualCompanionCount: number;
	actualParticipantCount: number;
};
export const confirmationRegistration = async (data: TConfirmationRegistrationData) => {
	const transaction = await db.sequelize.transaction();
	const { registrationId, actualCompanionCount, actualParticipantCount } = data;

	try {
		const registration = await db.registrations.findByPk(registrationId, {
			include: [
				{
					association: db.registrations.associations.Category,
				},
				{
					association: db.registrations.associations.Occurrence,
				},
			],
		});

		if (!registration) {
			throw new AppError(BAD_REQUEST, 'Registration not found', false);
		}

		await registration.update(
			{
				actualCompanionCount,
				actualParticipantCount,
				attended: 1,
			},
			{
				transaction,
			},
		);

		await transaction.commit();
	} catch (error) {
		await transaction.rollback();
		return Promise.reject(error);
	}
};

type TUpdateCountRegistration = {
	registrationId: number;
	participantCount: number;
	companionCount: number;
};
export const updateCountRegistration = async (data: TUpdateCountRegistration) => {
	const transaction = await db.sequelize.transaction();
	const { registrationId, participantCount, companionCount } = data;

	try {
		const registrations = await db.registrations.findByPk(registrationId, {
			include: [
				{
					association: db.registrations.associations.Category,
				},
				{
					association: db.registrations.associations.Occasion,
				},
				{
					association: db.registrations.associations.Occurrence,
				},
			],
		});
		if (!(registrations?.Category?.groupBooking || registrations?.Occasion?.groupBooking)) {
			throw new AppError(BAD_REQUEST, 'data error', false);
		}
		await db.registrations.update(
			{
				participantCount,
				companionCount,
				actualCompanionCount: null,
				actualParticipantCount: null,
				attended: 0,
				expected: participantCount + companionCount,
			},
			{
				where: {
					registrationId,
				},
				transaction,
			},
		);

		SocketServerService.emitRegistration({
			memberId: null,
			occasionId: null,
			occurrenceId: registrations?.occurrence?.occurrenceId,
		});

		await transaction.commit();
	} catch (error) {
		await transaction.rollback();
	}
};

export const memberRegisterForEventV2 = async (data: MemberRegistrationForEventSchema, transaction?: Transaction) => {
	const { occurrenceDetailId, timezone } = data;
	const memberInfo = await MemberService.findMemberByLineProfile(data.memberLine);
	const occurrenceDetail = await OccurrenceDetailService.getOccurrenceDetailById(occurrenceDetailId);
	if (!occurrenceDetail) {
		throw new AppError(BAD_REQUEST, `Occurrence detail ${occurrenceDetailId} does not exist`);
	}

	const occasionId = _.get(occurrenceDetail, 'occurrence.Occasion.occasionId');

	const totalClientAttendee = await redisCacheService.increaseOccurrenceDetailRegisterCountForEvent(
		occurrenceDetailId,
		1,
	);

	let occurrenceDetailMaxAttendee = parseInt(
		(await redisCacheService.getOccurrenceDetailMaxAttendee(occurrenceDetailId)) || '0',
	);

	const occurrenceId = occurrenceDetail?.occurrence?.occurrenceId;
	const memberId = memberInfo.memberId;

	if (!occurrenceDetailMaxAttendee && occurrenceId) {
		const currentOccurrence = await OccurrenceService.getOccurrenceDetailById(occurrenceDetailId);
		if (currentOccurrence) {
			occurrenceDetailMaxAttendee = currentOccurrence.maxAttendee;
			await redisCacheService.setOccurrenceDetailMaxAttendee(occurrenceDetailId, occurrenceDetailMaxAttendee);
		}
	}

	if (totalClientAttendee > occurrenceDetailMaxAttendee) {
		throw new AppError(
			CONFLICT_ERROR,
			`occurrence detail ${occurrenceDetailId} is full: ${totalClientAttendee} ${CUSTOM_SHOW_MESSAGE}`,
		);
	}

	const { eventDay, startTime, endTime } = occurrenceDetail;

	if (RegistrationUtility.isValidateTimeClientRegisterForEvent(eventDay, startTime, endTime)) {
		throw new AppError(BAD_REQUEST, 'Invalid registration time');
	}

	const registrationSameTime = await db.registrations.findOne({
		where: { memberId, cancelledAt: null, interviewStatus: MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED },
		include: [
			{
				model: db.occurrenceDetailModel,
				where: {
					eventDay,
					startTime,
					endTime,
				},
				required: true,
			},
		],
	});

	if (registrationSameTime) {
		throw new AppError(
			BAD_REQUEST,
			`予約可能な枠がありません。管理者までお問い合わせください。 ${CUSTOM_SHOW_MESSAGE}`,
		);
	}

	// Check line user status is schedule adjustment
	const scheduleAdjustmentRecord = await db.occasionMembers.findOne({
		where: {
			memberId,
			occasionId,
			interviewStatus: MEMBER_INTERVIEW_STATUS.SCHEDULE_ADJUSTMENT,
		},
	});

	if (!scheduleAdjustmentRecord) {
		throw new AppError(BAD_REQUEST, 'Member is not schedule adjustment.');
	}

	let registration: Registration | null = null;
	try {
		transaction = await db.sequelize.transaction();

		registration = await db.registrations.create(
			{
				memberId,
				expected: 1,
				occurrenceDetailId: occurrenceDetail.occurrenceDetailId,
				message: '',
				isRegistered: memberInfo.isRegistered,
				isFriends: memberInfo.isFriends,
				isManual: false,
				remarks: null,
				occurrenceId: occurrenceDetail?.occurrence?.occurrenceId,
				occasionId,
			},
			{ transaction },
		);

		await OccurrenceDetailService.incrementTotalAttendeeOccurrenceDetail(occurrenceDetailId, transaction);
		await OccasionMemberService.updateOccasionMemberByConditionWhere(
			{
				memberId,
				occasionId,
			},
			{
				interviewStatus: MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED,
			},
			transaction,
		);

		ReminderService.reservationMessage(occurrenceDetailId, memberInfo);

		await transaction.commit();
	} catch (error) {
		if (transaction) {
			await transaction.rollback();
		}
		writeLog(
			{
				msg: 'Could not create registration or update data',
				error,
				memberId,
				occurrenceDetail,
			},
			'info',
		);
		throw new AppError(BAD_REQUEST, 'Could not create registration or update data');
	}
	SocketServerService.emitRegistrationCreated();

	// TODO: Send line and create line message for cronjob should be try catch avoid side effect to count redis
	// const registrationResult = {
	// 	occasionId: occurrence.occasionId,
	// 	categoryId: occurrence.categoryId,
	// 	memberId: member.memberId,
	// 	startAt: occurrence.startAt,
	// 	isSettingTime: occurrence?.Category?.isSettingTime,
	// 	registrationId: registration.registrationId,
	// 	categoryTitle: occurrence?.Category?.title,
	// 	categoryImages: occurrence?.Category?.categoryImages,
	// 	isSendImage: occurrence?.Category?.isSendImage,
	// 	isMessage: occurrence?.Category?.isProgram ? occurrence?.Occasion?.isMessage : occurrence?.Category?.isMessage,
	// 	afterReservationMessage: occurrence?.Category?.isProgram
	// 		? occurrence?.Occasion?.CategoryMessageDetail?.afterReservationMessage
	// 		: occurrence?.Category?.CategoryMessageDetail?.afterReservationMessage,
	// 	reminderMessageOneDay: occurrence?.Category?.isProgram
	// 		? occurrence?.Occasion?.CategoryMessageDetail?.reminderMessageOneDay
	// 		: occurrence?.Category?.CategoryMessageDetail?.reminderMessageOneDay,
	// 	reminderMessageThreeDays: occurrence?.Category?.isProgram
	// 		? occurrence?.Occasion?.CategoryMessageDetail?.reminderMessageThreeDays
	// 		: occurrence?.Category?.CategoryMessageDetail?.reminderMessageThreeDays,
	// } as ResMemberRegistrationEvent;

	// if (timeZone) {
	// const messagesToClient = await SettingService.getRegistrationAndReminderMessages();
	//
	// const image: any = registrationResult?.categoryImages?.find((item: any) => item.showOrder === 0);
	//
	// if (memberInfo.lineId) {
	// 	const url = process.env.HOST
	// 		? `${process.env.HOST}/uploads/categories/${image?.picUrl}`
	// 		: `${systemConfig.PATH_FILE_UPLOAD_CATEGORY}/${image?.picUrl}`;
	//
	// 	await LineService.sendImageMessage(memberInfo.lineId, url);
	// }

	// const nameMember = get(memberInfo, 'customerRegistrationId1', '') as string;
	// const phoneMember = get(memberInfo, 'customerRegistrationId2', '') as string;

	// if (member.lineId) {
	// 	await LineService.sendLineAfterRegistrationEvent({
	// 		registrationResult,
	// 		messagesToClient,
	// 		nameMember,
	// 		memberLineId: member.lineId,
	// 	});
	// }
	//
	// await ReminderService.createMessageReminderEvent({
	// 	messagesToClient,
	// 	registrationResult,
	// 	memberId: memberInfo.memberId,
	// 	nameMember,
	// 	timeZone,
	// 	replacerName,
	// 	replacerBuilding,
	// 	replacerConfirmationUrl,
	// 	replacerDateTime,
	// 	replacerTelephoneCompany,
	// });
	//
	// await sendLineToAdminAfterRegistrationEvent({
	// 	registrationResult,
	// 	nameMember,
	// 	phoneMember,
	// });

	// SocketServerService.emitRegistration({
	// 	memberId: member.memberId,
	// 	categoryId: registrationResult.categoryId,
	// 	occasionId: registrationResult.occasionId,
	// 	occurrenceId,
	// });
	// }

	return registration;
};

export const memberGetRegistration = async (data: MemberGetRegistration) => {
	const { memberLine, occurrenceId } = data;
	const memberInfo = await MemberService.findMemberByLineProfile(memberLine);
	return db.registrations.findOne({
		where: {
			occurrenceId,
			memberId: memberInfo.memberId,
			cancelledAt: null,
		},
		include: [
			{
				model: db.occurrenceDetailModel,
				attributes: ['eventDay', 'startTime', 'endTime', 'interviewType', 'notes', 'interviewLocation'],
				include: [
					{
						model: Occurrence,
						attributes: ['notes', 'responseDeadlineDate', 'interviewStep'],
						include: [
							{
								model: Occasion,
								attributes: ['title'],
								include: [{ model: db.categories, attributes: ['title'] }],
							},
						],
					},
				],
			},
		],
		order: [['createdAt', 'DESC']],
	});
};

export const adminRegistrationByInterviewStep = async (data: AdminRegistrationByInterviewStepSchema) => {
	const conditionOccurrenceWhere = {
		interviewStep: { [Op.in]: data.interviewSteps },
		occasionId: data.occasionId,
	};
	const pagination = omit(data, 'interviewStatuses');
	const { page, pageSize } = pagination;
	const registrations = await db.registrations.findAll({
		where: {
			cancelledAt: null,
		},
		include: [
			{
				model: db.members,
			},
			{
				model: db.occurrences,
				where: conditionOccurrenceWhere,
			},
			{
				model: db.occurrenceDetailModel,
			},
		],
		limit: parseInt(pageSize),
		offset: (parseInt(page) - 1) * parseInt(pageSize),
		order: [['createdAt', 'DESC']],
	});
	const total = await db.registrations.count({
		where: {
			cancelledAt: null,
		},
		include: [
			{
				model: db.occurrences,
				where: conditionOccurrenceWhere,
			},
		],
	});
	return { list: registrations, page: parseInt(page), pageSize: parseInt(pageSize), total };
};

export const memberGetRegistrationByInterviewStatuses = async (
	data: MemberGetRegistrationByInterviewStatusesSchema,
) => {
	const { memberLine, page, pageSize, interviewStatuses } = data;
	const memberInfo = await MemberService.findMemberByLineProfile(memberLine);
	const interviewStatusesWithoutPassed = interviewStatuses.filter(
		(it) => it !== MEMBER_INTERVIEW_STATUS.INTERVIEW_PASSED,
	);

	let memberStatusesQuery = {
		interviewStatus: { [Op.in]: interviewStatusesWithoutPassed },
	} as any;

	const isPassedInterview = interviewStatuses.includes(MEMBER_INTERVIEW_STATUS.INTERVIEW_PASSED);
	const isAcceptInterview =
		interviewStatuses.includes(MEMBER_INTERVIEW_STATUS.ASSIGNED_CONDITIONALLY_ACCEPTED) ||
		interviewStatuses.includes(MEMBER_INTERVIEW_STATUS.ASSIGNED_ACCEPTED);

	// remove registration passed next round and confirmed
	if (isPassedInterview) {
		memberStatusesQuery = {
			[Op.or]: [
				{ interviewStatus: { [Op.in]: interviewStatusesWithoutPassed } },
				{
					interviewStatus: MEMBER_INTERVIEW_STATUS.INTERVIEW_PASSED,
					attended: 0,
				},
			],
		};
	}

	const memberId = memberInfo.memberId;

	const conditionWhere = {
		memberId,
		cancelledAt: null,
		...memberStatusesQuery,
	};

	let orderRegistration = [
		Sequelize.fn('ABS', Sequelize.literal('DATEDIFF(occurrenceDetail.eventDay, CURDATE())')),
		'daysFromToday',
	] as any;
	if (isAcceptInterview) {
		orderRegistration = null;
	}

	const occasionMembers = await db.occasionMembers.findAll({
		where: {
			memberId,
		},
		attributes: ['occasionId', 'occurrenceIdsRejected'],
		raw: true,
	});

	const occurrenceIdsRejected: number[] = occasionMembers.flatMap((it) =>
		Array.isArray(it.occurrenceIdsRejected) ? it.occurrenceIdsRejected : [],
	);

	const occurrenceConditionWhere = {
		occurrenceId: { [Op.notIn]: occurrenceIdsRejected },
	};

	const list = await db.registrations.findAll({
		where: conditionWhere,
		include: [
			{
				model: db.occurrenceDetailModel,
				attributes: [
					'eventDay',
					'startTime',
					'endTime',
					'interviewType',
					'occurrenceDetailId',
					'notes',
					'interviewLocation',
				],
				required: true,
			},
			{
				model: db.occurrences,
				attributes: ['notes', 'responseDeadlineDate', 'interviewStep', 'occurrenceId'],
				where: occurrenceConditionWhere,
				required: true,
			},
			{
				model: db.occasions,
				attributes: ['title', 'occasionId'],
				include: [
					{
						model: db.categories,
						attributes: ['title', 'categoryId', 'managerId'],
						include: [
							{
								model: db.managers,
							},
						],
					},
				],
				required: true,
			},
		],
		...(orderRegistration && {
			attributes: {
				include: [orderRegistration],
			},
		}),
		group: ['registrationId'],
		order: !isAcceptInterview
			? [
					[Sequelize.literal('daysFromToday'), 'ASC'],
					[Sequelize.literal('occurrenceDetail.startTime'), 'ASC'],
			  ]
			: [['updatedAt', 'DESC']],
		limit: parseInt(pageSize),
		offset: (parseInt(page) - 1) * parseInt(pageSize),
	});
	const total = await db.registrations.count({
		where: conditionWhere,
		include: [
			{
				model: db.occurrenceDetailModel,
				attributes: ['eventDay', 'startTime', 'endTime', 'interviewType', 'occurrenceDetailId'],
				required: true,
			},
			{
				model: db.occurrences,
				attributes: ['notes', 'responseDeadlineDate', 'interviewStep', 'occurrenceId'],
				required: true,
				where: occurrenceConditionWhere,
			},
			{
				model: db.occasions,
				attributes: ['title', 'occasionId'],
				include: [{ model: db.categories, attributes: ['title'] }],
				required: true,
			},
		],
		distinct: true,
	});
	return { list, page: parseInt(page), pageSize: parseInt(pageSize), total };
};

const validateResponseDeadline = async (deadline: string, occasionId: number, nextInterviewStep: any) => {
	const isTimeValid = await db.occurrenceDetailModel.count({
		where: {
			eventDay: { [Op.gte]: deadline },
		},
		include: [
			{
				model: db.occurrences,
				where: {
					occasionId,
					interviewStep: nextInterviewStep,
				},
				required: true,
			},
		],
	});

	if (!isTimeValid) {
		throw new AppError(BAD_REQUEST, `Occurrence is full ${CUSTOM_SHOW_MESSAGE} response_dead_line_invalid`);
	}
};

const validatePassInterviewConditions = async (
	data: AdminUpdateInterviewStatusSchema,
	occasionMember: OccasionMember,
) => {
	const { memberIds, responseDeadlineDatePassInterview, occasionId } = data;
	const nextInterviewStep = RegistrationUtility.getNextInterviewStep(occasionMember.memberInterviewCurrentStep);

	if (!nextInterviewStep) return;

	// Check if adding more members exceeds max attendee limit
	const currentMemberAdded = await db.occasionMembers.count({
		where: {
			occasionId,
			[Op.or]: [
				{
					memberInterviewCurrentStep: occasionMember?.memberInterviewCurrentStep,
					interviewStatus: MEMBER_INTERVIEW_STATUS.INTERVIEW_PASSED,
				},
				{
					memberInterviewCurrentStep: nextInterviewStep,
					interviewStatus: MEMBER_INTERVIEW_STATUS.SCHEDULE_ADJUSTMENT,
				},
			],
		},
	});

	const attendeeCheck = await OccurrenceService.checkMaxAttendees(
		occasionId,
		nextInterviewStep,
		memberIds.length + currentMemberAdded,
	);

	if (attendeeCheck.status) {
		throw new AppError(BAD_REQUEST, `Occurrence is full ${CUSTOM_SHOW_MESSAGE} ${attendeeCheck.reason}`);
	}

	// Validate response deadline date if provided
	if (responseDeadlineDatePassInterview) {
		await validateResponseDeadline(responseDeadlineDatePassInterview, occasionId, nextInterviewStep);
	}
};

const validateMembersBelongsToOccasion = async (memberIds: number[], occasionId: number) => {
	const occasionsMember = await db.occasionMembers.findAll({
		where: {
			memberId: {
				[Op.in]: memberIds,
			},
			occasionId,
		},
	});

	if (occasionsMember.length === 0) {
		throw new AppError(BAD_REQUEST, 'Member is not registered for this occasion', false);
	}

	return occasionsMember;
};

export const adminUpdateInterviewStatus = async (data: AdminUpdateInterviewStatusSchema) => {
	const { memberIds, interviewStatus, responseDeadlineDatePassInterview, occasionId } = data;

	const occasionsMember = await validateMembersBelongsToOccasion(memberIds, occasionId);

	if (occasionsMember.length !== memberIds.length) {
		throw new AppError(BAD_REQUEST, 'Member is not registered for this occasion', false);
	}

	const firstOccasionMember = occasionsMember[0];

	if (interviewStatus === MEMBER_INTERVIEW_STATUS.INTERVIEW_PASSED) {
		await validatePassInterviewConditions(data, firstOccasionMember);
	}

	const currentOccasionMemberStatus = firstOccasionMember.interviewStatus;

	const hasDifferentStatus = occasionsMember.some(
		(member) => member.interviewStatus !== MEMBER_INTERVIEW_STATUS.WAITING_RESULTS,
	);

	if (hasDifferentStatus) {
		throw new AppError(BAD_REQUEST, 'All interview statuses must be the waiting results.', false);
	}

	let transaction = null;
	try {
		transaction = await db.sequelize.transaction();

		await db.registrations.update(
			{ interviewStatus, responseDeadlineDatePassInterview },
			{
				where: {
					memberId: {
						[Op.in]: memberIds,
					},
					occasionId,
					interviewStatus: currentOccasionMemberStatus,
				},
				transaction,
			},
		);

		await OccasionMemberService.updateOccasionMemberByConditionWhere(
			{
				memberId: {
					[Op.in]: memberIds,
				},
				occasionId,
			},
			{
				interviewStatus,
			},
		);

		await transaction.commit();
	} catch (err: any) {
		if (transaction != null) {
			await transaction.rollback();
		}
		writeLog({ msg: 'adminUpdateInterviewStatus', err: err?.message, data }, 'error');
		throw err;
	}
	ReminderService.messageSentToStudentAfterInterviewRequest(memberIds, occasionId, responseDeadlineDatePassInterview);

	return true;
};

export const getRegistrationV2 = async (registrationId: number) =>
	db.registrations.findOne({
		where: {
			registrationId,
		},
		include: [
			{
				model: db.members,
			},
			{
				model: db.occurrences,
			},
			{
				model: db.occasions,
				include: [{ model: db.categories }],
			},
		],
	});

export const confirmInterviewNextStep = async (registrationId: number) => {
	const currentRegistration = await db.registrations.findByPk(registrationId);
	if (!currentRegistration) {
		throw new AppError(BAD_REQUEST, `registration does not exists ${CUSTOM_SHOW_MESSAGE}`, false);
	}
	const { occasionId, memberId } = currentRegistration;
	const currentOccasionMember = await OccasionMemberService.getOccasionMemberByConditionWhere({
		occasionId,
		memberId,
	});
	if (!currentOccasionMember) {
		throw new AppError(BAD_REQUEST, `occasion member does not exists ${CUSTOM_SHOW_MESSAGE}`, false);
	}
	const nextInterviewStep = RegistrationUtility.getNextInterviewStep(currentOccasionMember?.memberInterviewCurrentStep);
	const currentDay = moment().format(DATE_FORMAT);
	const currentTime = moment().format(TIME_FORMAT);
	const eventInterview = await db.occurrences.findOne({
		where: {
			...(nextInterviewStep && { interviewStep: nextInterviewStep }),
			occasionId,
		},
		include: [
			{
				model: db.categories,
				where: { isDisplayed: true },
			},
			{
				model: db.occasions,
				where: { isDisplayed: true },
			},
			{
				model: db.occurrenceDetailModel,
				where: {
					[Op.or]: [
						{ eventDay: { [Op.gt]: currentDay } },
						{ eventDay: { [Op.eq]: currentDay }, startTime: { [Op.gt]: currentTime } },
					],
				},
			},
		],
	});
	if (!eventInterview) {
		throw new AppError(BAD_REQUEST, `${CUSTOM_SHOW_MESSAGE}`, false);
	}
	await OccasionMemberService.updateOccasionMemberByConditionWhere(
		{
			occasionId,
			memberId,
		},
		{
			interviewStatus: MEMBER_INTERVIEW_STATUS.SCHEDULE_ADJUSTMENT,
			...(nextInterviewStep && { memberInterviewCurrentStep: nextInterviewStep }),
		},
	);
	await db.registrations.update(
		{ attended: 1 },
		{
			where: {
				registrationId,
			},
		},
	);
	SocketServerService.emitOccasionMemberChangeStatus();
	return eventInterview;
};

export const memberChangeMemberActionStatus = async (
	data: MemberChangeMemberActionStatusSchema,
	transaction?: Transaction,
) => {
	await db.registrations.update(
		{
			memberActionStatus: data.memberActionStatus,
		},
		{
			where: {
				registrationId: data.registrationId,
			},
			transaction,
		},
	);
	return true;
};

export const memberConfirmOfficeAttend = async (data: MemberConfirmOfficeAttendSchema, transaction?: Transaction) => {
	const currentRegistration = await db.registrations.findOne({
		where: {
			registrationId: data.registrationId,
		},
	});
	if (!currentRegistration) {
		throw new AppError(BAD_REQUEST, 'registration does not exists ${CUSTOM_SHOW_MESSAGE}`)', false);
	}
	await db.registrations.update(
		{
			memberActionStatus: MEMBER_ACTIONS_STATUS.MEMBER_ACCEPTED,
		},
		{
			where: {
				registrationId: data.registrationId,
			},
			transaction,
		},
	);
	await db.registrations.update(
		{
			memberActionStatus: MEMBER_ACTIONS_STATUS.MEMBER_NOT_ACCEPTED,
		},
		{
			where: {
				[Op.or]: [
					{ memberActionStatus: { [Op.ne]: MEMBER_ACTIONS_STATUS.MEMBER_ACCEPTED } },
					{ memberActionStatus: null },
				],
				memberId: currentRegistration.memberId,
			},
			transaction,
		},
	);
	await db.registrations.update(
		{
			interviewStatus: MEMBER_INTERVIEW_STATUS.DECLINED,
		},
		{
			where: {
				interviewStatus: {
					[Op.notIn]: [
						MEMBER_INTERVIEW_STATUS.DECLINED,
						MEMBER_INTERVIEW_STATUS.NOT_ACCEPTED,
						MEMBER_INTERVIEW_STATUS.ASSIGNED_ACCEPTED,
						MEMBER_INTERVIEW_STATUS.ASSIGNED_CONDITIONALLY_ACCEPTED,
					],
				},
				memberId: currentRegistration.memberId,
			},
			transaction,
		},
	);
	await OccasionMemberService.updateOccasionMemberByConditionWhere(
		{
			memberId: currentRegistration.memberId,
			interviewStatus: {
				[Op.notIn]: [
					MEMBER_INTERVIEW_STATUS.DECLINED,
					MEMBER_INTERVIEW_STATUS.NOT_ACCEPTED,
					MEMBER_INTERVIEW_STATUS.ASSIGNED_ACCEPTED,
					MEMBER_INTERVIEW_STATUS.ASSIGNED_CONDITIONALLY_ACCEPTED,
					MEMBER_INTERVIEW_STATUS.INTERVIEW_PROPOSAL,
				],
			},
		},
		{
			interviewStatus: MEMBER_INTERVIEW_STATUS.DECLINED,
		},
		transaction,
	);
	return { memberId: currentRegistration.memberId, currentRegistration };
};

export const cronJobDeclineInterviewExpired = async () => {
	try {
		const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
		let yesterday = moment().subtract(1, 'days').startOf('day').format('YYYY-MM-DD');
		if (systemConfig.TEST_JOB_DECLINED_INTERVIEW) {
			yesterday = moment().add(1, 'days').format('YYYY-MM-DD');
		}
		const endOfYesterday = moment(yesterday).endOf('day').format('HH:mm:ss');
		console.log(
			'start run cronjob systemDeclineInterviewExpired',
			CRON_JOB_CHANGE_INTERVIEW_STATUS,
			yesterday,
			endOfYesterday,
			new Date(),
			timezone,
		);
		const registrationsExpiredForInterviewPassed = await db.registrations.findAll({
			where: {
				cancelledAt: null,
				interviewStatus: MEMBER_INTERVIEW_STATUS.INTERVIEW_PASSED,
				responseDeadlineDatePassInterview: { [Op.lte]: yesterday },
				attended: 0, // Not confirm passed
				expiredAt: null,
				memberId: { [Op.not]: null },
			},
			attributes: ['occurrenceDetailId', 'memberId', 'occasionId', 'occasionId', 'registrationId'],
		});

		console.log('registrationsExpired', registrationsExpiredForInterviewPassed.length);
		console.log('registrationsExpired', registrationsExpiredForInterviewPassed);

		const listMemberIdExpired = [] as number[];
		for (const registration of registrationsExpiredForInterviewPassed) {
			const memberId = registration.memberId;
			const occasionId = registration.occasionId;
			const occurrenceId = registration.occasionId;
			const occurrenceDetailId = registration.occurrenceDetailId;
			const registrationId = registration.registrationId;

			let transaction = null;
			try {
				transaction = await db.sequelize.transaction();
				if (memberId && occasionId && occurrenceId) {
					listMemberIdExpired.push(memberId);
					const occasionMember = await db.occasionMembers.findOne({
						where: {
							memberId,
							occasionId,
						},
					});
					if (!occasionMember) {
						return;
					}
					const oldOccurrenceIdsCancelled = occasionMember.occurrenceIdsRejected || [];
					const newOccurrenceIdsRejected = [...oldOccurrenceIdsCancelled, occurrenceId];

					await db.occasionMembers.update(
						{
							occurrenceIdsRejected: [...new Set(newOccurrenceIdsRejected)],
							interviewStatus: MEMBER_INTERVIEW_STATUS.AUTO_DECLINED,
							expiredAt: new Date(),
						},
						{
							where: {
								memberId,
								occasionId,
								interviewStatus: {
									[Op.notIn]: [MEMBER_INTERVIEW_STATUS.DECLINED, MEMBER_INTERVIEW_STATUS.AUTO_DECLINED],
								},
							},
							transaction,
						},
					);
					await db.registrations.update(
						{
							interviewStatus: MEMBER_INTERVIEW_STATUS.AUTO_DECLINED,
							expiredAt: new Date(),
						},
						{
							where: {
								registrationId,
								interviewStatus: {
									[Op.notIn]: [MEMBER_INTERVIEW_STATUS.DECLINED, MEMBER_INTERVIEW_STATUS.AUTO_DECLINED],
								},
							},
							transaction,
						},
					);
					await transaction.commit();
					if (occurrenceDetailId) {
						await redisCacheService.decreaseOccurrenceDetailRegisterCountForEvent(occurrenceDetailId, 1);
					}
				}
			} catch (err: any) {
				console.log('err', err);
				if (transaction !== null) {
					await transaction.rollback();
					writeLog({ msg: 'declineInterviewExpired_update_data', error: err, message: err?.message }, 'error');
				}
			}
		}
		const listMemberInterviewAdjustment = await db.occasionMembers.findAll({
			where: {
				interviewStatus: MEMBER_INTERVIEW_STATUS.SCHEDULE_ADJUSTMENT,
			},
		});
		if (listMemberInterviewAdjustment.length > 0) {
			for (const memberInterviewAdjustment of listMemberInterviewAdjustment) {
				const occurrence = await db.occurrences.findOne({
					where: {
						interviewStep: memberInterviewAdjustment.memberInterviewCurrentStep,
						occasionId: memberInterviewAdjustment.occasionId,
						responseDeadlineDate: { [Op.lte]: yesterday },
					},
				});
				if (occurrence) {
					const newOccurrenceIdsRejected = [
						...memberInterviewAdjustment.occurrenceIdsRejected,
						occurrence.occurrenceId,
					];
					await db.occasionMembers.update(
						{
							occurrenceIdsRejected: newOccurrenceIdsRejected,
							interviewStatus: MEMBER_INTERVIEW_STATUS.AUTO_DECLINED,
							expiredAt: new Date(),
						},
						{
							where: {
								occasionMemberId: memberInterviewAdjustment.occasionMemberId,
							},
						},
					);
				}
			}
		}
	} catch (err: any) {
		writeLog({ msg: 'declineInterviewExpired', error: err, message: err?.message }, 'info');
	}
};

export const updateRegistrationsAfterRemoveMemberClass = async (memberIds: number[], transaction?: Transaction) => {
	const listRegistrationDeleted = await db.registrations.findAll({
		where: { memberId: { [Op.in]: memberIds }, cancelledAt: null, deletedAt: null },
	});
	await db.registrations.update(
		{ memberId: null, deletedAt: new Date() },
		{
			where: { memberId: { [Op.in]: memberIds } },
			transaction,
		},
	);
	const listOccurrenceDetailIds = listRegistrationDeleted.map((it) => it.occurrenceDetailId);
	for (const occurrenceDetailId of listOccurrenceDetailIds) {
		if (occurrenceDetailId) {
			await redisCacheService.decreaseOccurrenceDetailRegisterCountForEvent(occurrenceDetailId, 1);
			await OccurrenceDetailService.decrementTotalAttendeeOccurrenceDetail(occurrenceDetailId, transaction);
		}
	}
	return true;
};

export const cronJobChangeInterviewToWaitingResult = async (isManually = false) => {
	const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
	const today = getDateNowWithTimezone().format(DATE_FORMAT_TYPE.ISO_DATE);
	const timeNow = getDateNowWithTimezone().format(TIME_FORMAT);
	console.log(
		`start run cronjob runChangeStatusInterviewToWaitingResult isManually ${isManually}`,
		CRON_JOB_CHANGE_INTERVIEW_STATUS,
		today,
		timeNow,
		new Date(),
		timezone,
	);
	const registrationsInterviewed = await db.registrations.findAll({
		where: {
			cancelledAt: null,
			interviewStatus: MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED,
			memberId: { [Op.not]: null },
		},
		include: [
			{
				model: db.occurrenceDetailModel,
				where: {
					eventDay: today,
					endTime: {
						[Op.lt]: timeNow,
					},
				},
			},
		],
	});
	console.log('registrationsInterviewed', registrationsInterviewed.length);
	console.log('registrationsInterviewed', registrationsInterviewed);
	if (registrationsInterviewed.length) {
		const registrationIds = registrationsInterviewed.map((it) => it.registrationId);
		const occasionConditions = [] as any;
		for (const registration of registrationsInterviewed) {
			occasionConditions.push({
				[Op.and]: {
					memberId: registration.memberId,
					occasionId: registration.occasionId,
				},
			});
		}
		let transaction = null;
		try {
			transaction = await db.sequelize.transaction();
			await db.registrations.update(
				{ interviewStatus: MEMBER_INTERVIEW_STATUS.WAITING_RESULTS },
				{ where: { registrationId: { [Op.in]: registrationIds as number[] } }, transaction },
			);
			await OccasionMemberService.updateOccasionMemberByConditionWhere(
				{
					[Op.or]: occasionConditions,
				},
				{
					interviewStatus: MEMBER_INTERVIEW_STATUS.WAITING_RESULTS,
				},
				transaction,
			);
			await transaction.commit();
		} catch (err: any) {
			writeLog(
				{
					msg: 'scheduledTaskService.service.runChangeStatusInterviewToWaitingResult',
					errMsg: err?.message,
					err,
				},
				'error',
			);
		}
		SocketServerService.emitOccasionMemberChangeStatus();
	}
	return true;
};

export const generateRegistrationDataForCSVV2 = async ({
	registrationWhere,
	occasionWhere,
	categoryWhere,
	customerRegistrations,
	occurrenceWhere,
}: {
	registrationWhere: WhereAttributeHash;
	occasionWhere: WhereAttributeHash;
	categoryWhere: WhereAttributeHash;
	occurrenceWhere: WhereAttributeHash;
	customerRegistrations: CustomerRegistration[];
}) => {
	return db.registrations.findAll({
		where: registrationWhere,
		attributes: [
			[col('Registration.memberId'), 'memberId'],
			[col('Registration.message'), 'message'],
			[col('Member.memberCode'), 'memberCode'],
			[col('Member.displayName'), 'displayName'],
			[col('Member.firstName'), 'firstName'],
			[col('Member.lastName'), 'lastName'],
			[col('Member.firstNameKana'), 'firstNameKana'],
			[col('Member.lastNameKana'), 'lastNameKana'],
			[col('Member.email'), 'email'],
			[col('Member.telephone'), 'telephone'],
			[col('Member.postalCode'), 'postalCode'],
			[col('Member.building'), 'building'],
			[col('Member.address'), 'address'],
			[col('Member.memberSince'), 'memberSince'],
			[col('Member.isCampaign'), 'isCampaign'],
			[col('Member.candidateAt'), 'candidateAt'],
			[col('Member.isFriends'), 'isFriends'],
			[col('`Occasion->Category`.`title`'), 'categoryTitle'],
			[col('`Occasion->classCategory`.`name`'), 'classCategoryName'],
			[col('`Occasion.isDisplayed`'), 'isDisplayedOccasion'],
			[col('Occasion.title'), 'occasionTitle'],
			[
				Sequelize.literal(
					// eslint-disable-next-line quotes
					"CONCAT(`occurrenceDetail`.`eventDay`, ' ', `occurrenceDetail`.`startTime`)",
				),
				'startAt',
			],
			[col('occurrenceDetail.interviewType'), 'interviewType'],
			[col('occurrenceDetail.interviewLocation'), 'interviewLocation'],
			[col('Registration.interviewStatus'), 'interviewStatus'],
			[col('occurrence.interviewStep'), 'interviewStep'],
			[col('occurrence.responseDeadlineDate'), 'responseDeadlineDate'],
			[col('Registration.cancelledAt'), 'cancelledAt'],
			[col('Member.notes'), 'notes'],
			[col('Member.countVisit'), 'countVisit'],
			[col('Member.lastVisit'), 'lastVisit'],
			[col('Member.currentPoints'), 'currentPoints'],
			[col('Member.createdAt'), 'createdAt'],
			[col('Member.activeUntil'), 'activeUntil'],
			[col('Member.lineId'), 'lineId'],
			...customerRegistrations.map(
				({ customerRegistrationId }) =>
					[
						col(`Member.customerRegistrationId${customerRegistrationId}`),
						`customerRegistrationId${customerRegistrationId}`,
					] as ProjectionAlias,
			),
		],
		include: [
			{
				association: db.registrations.associations.Member,
				required: true,
				attributes: [],
			},
			{
				model: db.occurrences,
				required: true,
				attributes: [],
			},
			{
				association: db.registrations.associations.occurrenceDetail,
				required: true,
				attributes: [],
				where: {},
			},
			{
				association: db.registrations.associations.Occasion,
				required: true,
				where: occasionWhere,
				attributes: [],
				include: [
					{
						association: db.occasions.associations.Category,
						required: true,
						attributes: [],
						where: categoryWhere,
					},
					{
						model: db.classCategoryModel,
					},
				],
			},
		],
		raw: true,
		nest: true,
	});
};

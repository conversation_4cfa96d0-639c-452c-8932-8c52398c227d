import { z } from 'zod';

const passwordValidation = z
	.string()
	.min(8, 'Password must be at least 8 characters long')
	.max(20, 'Password must be at most 20 characters long')
	.refine(
		(password) => {
			const latinOnlyRegex = /^[A-Za-z0-9]+$/;
			return latinOnlyRegex.test(password);
		},
		{
			message:
				'Password must contain only Latin letters (A-Z, a-z) and numbers (0-9). No spaces, special characters, or non-Latin characters allowed.',
		},
	);

export const createManagerSchema = z.object({
	username: z.string().min(1),
	password: passwordValidation,
	email: z.string().email(),
	officeName: z.string().optional().nullable().default(null),
	website: z.string().optional().nullable().default(null),
	emailContact: z.string().optional().nullable().default(null),
	managerName: z.string().optional().nullable().default(null),
	authLevel: z.number().optional().default(5),
	phoneNumber: z.string().optional(),
	interviewLocation: z.string().optional(),
});

export const updateManagerSchema = z.object({
	username: z.string().min(1),
	password: passwordValidation.optional(),
	email: z.string().email().optional(),
	officeName: z.string().optional(),
	website: z.string().optional().nullable().default(null),
	emailContact: z.string().optional(),
	managerName: z.string().optional(),
	authLevel: z.number().optional(),
	phoneNumber: z.string().optional(),
	interviewLocation: z.string().optional(),
	managerId: z.number(),
});

export const deleteManagerSchema = z.object({
	username: z.string().min(1),
});

export const getManagerListSchema = z.object({
	page: z.string().min(1),
	pageSize: z.string().min(1),
});

export type CreateManagerSchema = z.infer<typeof createManagerSchema>;
export type UpdateManagerSchema = z.infer<typeof updateManagerSchema>;
export type deleteManagerSchema = z.infer<typeof deleteManagerSchema>;
export type GetManagerListSchema = z.infer<typeof getManagerListSchema>;

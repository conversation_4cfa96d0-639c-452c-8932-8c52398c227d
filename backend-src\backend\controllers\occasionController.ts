import { NextFunction, Request, Response } from 'express';
import { CreationAttributes, Op, Sequelize, Transaction } from 'sequelize';
import {
	BAD_REQUEST,
	INTERVIEW_STEP,
	MEMBER_INTERVIEW_STATUS,
	RESPONSE_SUCCESS,
	SYSTEM_ERROR,
	systemConfig,
} from '../config';
import { db } from '../models';
import { OccasionDetail } from '../models/occasionDetailModel';
import { Occurrence } from '../models/occurrenceModel';
import { OccasionMemberService, OccasionService, OccurrenceDetailService, SocketServerService } from '../services';
import { AppError, FileOps } from '../utilities';
import {
	adminGetInterviewStatisticSchema,
	createOccasionSchema,
	getOccasionListSchema,
	updateOccasionDisplaySchema,
	updateOccasionSchema,
} from '~schemas/occasions';
import { isEmpty, omit } from 'lodash';
import path = require('path');
import { redisCacheService } from '~services/redisCacheService';

interface IOccurrenceDetail {
	occurrenceId: number;
	occurrenceDetailId: number;
	eventDay: string;
}

interface IOccurrence {
	occurrenceId: number;
	occurrenceDetails: IOccurrenceDetail[];
}

interface ICategory {
	title: string;
}

interface IOccasion {
	occasionId: number;
	title: string;
	occurrences: IOccurrence[];
	Category: ICategory;
}

export const listOccasionBare = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const include =
			req.query.includePic == 'true' ? { association: db.occasions.associations.occasionImages } : undefined;
		const isParanoid = req.query.includeDestroyed == 'true' ? false : true;
		const isCampaign = req.query.isCampaign == 'true' ? true : false;
		await OccasionService.listOccasionsBare(include, isParanoid, isCampaign).then((occasionList) =>
			res.send(occasionList),
		);
	} catch (e) {
		next(e);
	}
};

export const detailOccasion_Master = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const occasionId = parseInt(req.params.occasionId);

		if (!occasionId || isNaN(occasionId)) {
			throw new AppError(SYSTEM_ERROR, 'invalid occasionId', false);
		}
		const from = req.query.from as unknown as Date;
		const to = req.query.to as unknown as Date;
		const isParanoid = req.query.includeDestroyed != 'true';
		const data = await OccasionService.getOccasionDetailsV2({ occasionId, isParanoid, from, to });
		return res.send(data);
	} catch (e) {
		next(e);
	}
};

export const browseOccasionCategoryMaster = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const validatedQuery = getOccasionListSchema.parse(req.query);
		const { categoryId, sort, sortKey } = validatedQuery;
		const condition: PaginationParamsNew = {
			pageSize: parseInt(validatedQuery.pageSize),
			page: parseInt(validatedQuery.page),
			sort,
			sortKey,
		};
		const occasions = await OccasionService.browseOccasionsCategoryMaster(parseInt(categoryId), condition);
		return res.send({ ...condition, ...occasions });
	} catch (e) {
		next(e);
	}
};

export const browseOccasionMaster = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const campaignId = parseInt(req.query.campaignId as string);
		if (!campaignId || isNaN(campaignId)) {
			throw new AppError(SYSTEM_ERROR, 'invalid parameters', false);
		}
		const condition: paginationParams = {
			pp: parseInt(req.query.pp as string) || 20,
			p: parseInt(req.query.p as string) || 1,
			sort: (req.query.sort as string as 'asc' | 'desc' | null) || 'asc',
			sortKey: (req.query.sortKey as string) || 'showOrder',
		};
		await OccasionService.browseOccasionsMaster(campaignId, condition).then((occasions) =>
			res.send({ ...condition, ...occasions }),
		);
		//res.send({ pp: condition.pp, p: condition.p, sort: condition.sort, sortKey: condition.sortKey, ...occasions }));
	} catch (e) {
		next(e);
	}
};

export const createCampaignOccasion = async (req: Request, res: Response, next: NextFunction) => {
	let transaction: Transaction | null = null;
	const reqFiles = req.files as { occasionImages?: Express.Multer.File[] };
	const images = reqFiles?.occasionImages ?? [];

	try {
		const params = req.body;

		if (!params.title || isNaN(params.campaignId) || !params.campaignId) {
			throw new AppError(SYSTEM_ERROR, 'invalid parameters', false);
		}
		const details = params.occasionDetails
			? (JSON.parse(params.occasionDetails as string) as CreationAttributes<OccasionDetail>[])
			: [];
		const imageDetails = params.occasionImagesData
			? (JSON.parse(params.occasionImagesData as string) as imageUpdateType[])
			: [];
		params.canOverlap = params.canOverlap && params.canOverlap == 'true';

		params.campaignId = parseInt(params.campaignId);
		transaction = await db.sequelize.transaction();
		const occasion = await OccasionService.createCampaignOccasion(params, transaction);

		await Promise.all([
			Array.isArray(details) && details.length > 0
				? OccasionService.updateOccasionDetails(occasion.occasionId, details, transaction)
				: Promise.resolve(),
			Array.isArray(images) && images.length > 0 && Array.isArray(imageDetails) && imageDetails.length > 0
				? OccasionService.updateOccasionImages(occasion.occasionId, images, imageDetails, transaction)
				: Promise.resolve(),
		])
			.then(() => transaction?.commit())
			.then(() => {
				SocketServerService.emitOccasion({
					occasionId: occasion.occasionId,
					campaignId: occasion.campaignId as number | undefined,
				});
				res.sendStatus(RESPONSE_SUCCESS);
			});
	} catch (e) {
		if (Array.isArray(images)) {
			await Promise.all(
				images.map((image) => FileOps.deleteFile(path.join(systemConfig.PATH_FILE_UPLOAD_OCCASION, image.filename))),
			);
		}
		if (transaction != null) {
			await transaction.rollback();
		}
		next(e);
	}
};

export const createOccasion = async (req: Request, res: Response, next: NextFunction) => {
	let transaction: Transaction | null = null;

	try {
		const validateData = createOccasionSchema.parse(req.body);

		transaction = await db.sequelize.transaction();
		const occasion = await OccasionService.createOccasion(validateData, transaction);
		await transaction.commit();

		SocketServerService.emitOccasion({
			occasionId: occasion.occasionId,
			categoryId: occasion.categoryId as number | undefined,
		});
		res.sendStatus(RESPONSE_SUCCESS);
	} catch (e) {
		if (transaction != null) {
			await transaction.rollback();
		}
		next(e);
	}
};

export const updateCampaignOccasion = async (req: Request, res: Response, next: NextFunction) => {
	const reqFiles = req.files as { occasionImages?: Express.Multer.File[] };
	const images = reqFiles?.occasionImages ?? [];
	let transaction: Transaction | null = null;
	try {
		const occasionId = parseInt(req.params.occasionId);
		const params = req.body;
		if (!occasionId) {
			throw new AppError(SYSTEM_ERROR, 'invalid occasionId', false);
		}
		const details = params.occasionDetails
			? (JSON.parse(params.occasionDetails as string) as CreationAttributes<OccasionDetail>[])
			: [];
		const imageDetails = params.occasionImagesData
			? (JSON.parse(params.occasionImagesData as string) as imageUpdateType[])
			: [];
		let occurrences = params.occurrences ?? [];
		if (typeof occurrences == 'string') {
			occurrences = JSON.parse(occurrences) as CreationAttributes<Occurrence>[];
		}
		params.canOverlap = params.canOverlap && params.canOverlap == 'true';
		params.campaignId = params.campaignId ? parseInt(req.body.campaignId) : null;

		transaction = await db.sequelize.transaction();
		const occasion = await db.occasions.findByPk(occasionId, {
			attributes: ['occasionId', 'campaignId'],
			transaction,
		});
		if (occasion == null) {
			throw new AppError(SYSTEM_ERROR, 'occasion does not exist', false);
		}
		const campaignId = occasion.campaignId as number | undefined;
		await Promise.all([
			OccasionService.updateCampaignOccasion(occasionId, params, transaction),
			Array.isArray(details)
				? OccasionService.updateOccasionDetails(occasionId, details, transaction)
				: Promise.resolve(),
			Array.isArray(details)
				? OccasionService.updateOccasionImages(occasionId, images, imageDetails, transaction)
				: Promise.resolve(),
		])
			.then(() => transaction?.commit())
			.then(() => {
				SocketServerService.emitOccasion({ occasionId, campaignId });
				res.sendStatus(RESPONSE_SUCCESS);
			});
	} catch (e) {
		if (transaction != null) {
			await transaction.rollback();
		}
		next(e);
	}
};
export const updateOccasion = async (req: Request, res: Response, next: NextFunction) => {
	let transaction: Transaction | null = null;

	try {
		const validateData = updateOccasionSchema.parse({ ...req.body, ...req.params });

		transaction = await db.sequelize.transaction();
		await OccasionService.updateOccasion(validateData, transaction);
		await transaction.commit();

		SocketServerService.emitOccasionUpdated(parseInt(validateData.occasionId));
		res.sendStatus(RESPONSE_SUCCESS);
	} catch (e) {
		if (transaction != null) {
			await transaction.rollback();
		}
		next(e);
	}
};

export const updateOccasionOrder = async (req: Request, res: Response, next: NextFunction) => {
	let transaction: Transaction | null = null;
	try {
		const params = req.body.occasions as { occasionId: number; showOrder: number }[];
		if (!Array.isArray(params) || params.length == 0) {
			throw new AppError(SYSTEM_ERROR, 'invalid parameters', false);
		}
		transaction = await db.sequelize.transaction();
		const occasion = await db.occasions.findOne({
			where: { occasionId: params[0].occasionId },
			attributes: ['occasionId', 'categoryId'],
			transaction,
		});
		if (occasion == null) {
			throw new AppError(SYSTEM_ERROR, 'occasion does not exist', false);
		}
		const categoryId = occasion.categoryId as number | undefined;
		await OccasionService.updateOccasionOrder(params, transaction)
			.then(() => transaction?.commit())
			.then(() => {
				SocketServerService.emitOccasion({ categoryId });
				res.sendStatus(RESPONSE_SUCCESS);
			});
	} catch (e) {
		if (transaction != null) {
			await transaction.rollback();
		}
		next(e);
	}
};

export const deleteOccasion = async (req: Request, res: Response, next: NextFunction) => {
	let transaction: Transaction | null = null;
	const occasionId = parseInt(req.params.occasionId);
	try {
		if (!occasionId) {
			throw new AppError(SYSTEM_ERROR, 'invalid occasionId', false);
		}
		transaction = await db.sequelize.transaction();
		const occasion = await db.occasions.findByPk(occasionId, {
			attributes: ['occasionId', 'categoryId'],
			transaction,
		});
		if (occasion == null) {
			throw new AppError(SYSTEM_ERROR, 'occasion does not exist', false);
		}
		const categoryId = occasion.categoryId as number | undefined;
		const campaignId = occasion.campaignId as number | undefined;
		await OccasionService.deleteOccasion(occasionId, transaction)
			.then(() => transaction?.commit())
			.then(() => {
				SocketServerService.emitOccasion({ occasionId, categoryId, campaignId });
				res.sendStatus(RESPONSE_SUCCESS);
			});
	} catch (e) {
		if (transaction != null) {
			await transaction.rollback();
		}
		next(e);
	}
};

export const updateOccasionDisplay = async (req: Request, res: Response, next: NextFunction) => {
	let transaction: Transaction | null = null;

	try {
		const validateData = updateOccasionDisplaySchema.parse({ ...req.params, ...req.body });

		transaction = await db.sequelize.transaction();
		await OccasionService.updateOccasionDisplay(validateData, transaction);
		await transaction.commit();

		res.sendStatus(RESPONSE_SUCCESS);
	} catch (e) {
		if (transaction != null) {
			await transaction.rollback();
		}
		next(e);
	}
};

export const adminGetInterviewStatistic = async (req: Request, res: Response, next: NextFunction) => {
	const validateData = adminGetInterviewStatisticSchema.parse({ ...req.query });

	const duplicates = await db.occasionMembers.findAll({
		where: {
			occasionId: validateData.occasionId,
		},
		attributes: ['memberId', 'occasionId'],
		group: ['memberId', 'occasionId'],
		having: Sequelize.literal('COUNT(*) > 1'),
	});

	if (duplicates.length > 0) {
		for (const duplicate of duplicates) {
			const { memberId, occasionId } = duplicate;

			const latestRecord = await db.occasionMembers.findOne({
				where: { memberId, occasionId },
				order: [['updatedAt', 'DESC']],
			});

			if (!latestRecord) {
				continue;
			}

			await db.occasionMembers.destroy({
				where: {
					memberId,
					occasionId,
					occasionMemberId: { [Op.ne]: latestRecord?.occasionMemberId },
				},
			});
		}
	}

	const firstInterview = await OccasionMemberService.adminGetMemberByInterview({
		interviewSteps: [INTERVIEW_STEP.FIRST_INTERVIEW],
		interviewStatuses: [
			MEMBER_INTERVIEW_STATUS.SCHEDULE_ADJUSTMENT,
			MEMBER_INTERVIEW_STATUS.WAITING_RESULTS,
			MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED,
			MEMBER_INTERVIEW_STATUS.INTERVIEW_PASSED,
		],
		...{ ...validateData, pageSize: '9999' },
	});
	const secondInterview = await OccasionMemberService.adminGetMemberByInterview({
		interviewSteps: [INTERVIEW_STEP.SECOND_INTERVIEW],
		interviewStatuses: [
			MEMBER_INTERVIEW_STATUS.SCHEDULE_ADJUSTMENT,
			MEMBER_INTERVIEW_STATUS.WAITING_RESULTS,
			MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED,
			MEMBER_INTERVIEW_STATUS.INTERVIEW_PASSED,
		],
		...{ ...validateData, pageSize: '9999' },
	});
	const thirdInterview = await OccasionMemberService.adminGetMemberByInterview({
		interviewSteps: [INTERVIEW_STEP.THIRD_INTERVIEW],
		interviewStatuses: [
			MEMBER_INTERVIEW_STATUS.SCHEDULE_ADJUSTMENT,
			MEMBER_INTERVIEW_STATUS.WAITING_RESULTS,
			MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED,
			MEMBER_INTERVIEW_STATUS.INTERVIEW_PASSED,
		],
		...{ ...validateData, pageSize: '9999' },
	});
	const fourthInterview = await OccasionMemberService.adminGetMemberByInterview({
		interviewSteps: [INTERVIEW_STEP.FOURTH_INTERVIEW],
		interviewStatuses: [
			MEMBER_INTERVIEW_STATUS.SCHEDULE_ADJUSTMENT,
			MEMBER_INTERVIEW_STATUS.WAITING_RESULTS,
			MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED,
			MEMBER_INTERVIEW_STATUS.INTERVIEW_PASSED,
		],
		...{ ...validateData, pageSize: '9999' },
	});

	const pagination = omit(validateData, 'occasionId');

	const interviewsCandidates = await OccasionMemberService.adminGetMemberByConditionWhere(
		{
			interviewStatus: {
				[Op.in]: [MEMBER_INTERVIEW_STATUS.INTERVIEW_PROPOSAL],
			},
			occasionId: validateData.occasionId,
		},
		{ ...pagination, pageSize: '9999' },
	);

	const interviewsPassed = await OccasionMemberService.adminGetMemberByConditionWhere(
		{
			interviewStatus: {
				[Op.in]: [MEMBER_INTERVIEW_STATUS.ASSIGNED_ACCEPTED, MEMBER_INTERVIEW_STATUS.ASSIGNED_CONDITIONALLY_ACCEPTED],
			},
			occasionId: validateData.occasionId,
		},
		{ ...pagination, pageSize: '9999' },
	);

	const interviewsFailed = await OccasionMemberService.adminGetMemberByConditionWhere(
		{
			interviewStatus: {
				[Op.in]: [
					MEMBER_INTERVIEW_STATUS.NOT_ACCEPTED,
					MEMBER_INTERVIEW_STATUS.DECLINED,
					MEMBER_INTERVIEW_STATUS.AUTO_DECLINED,
				],
			},
			occasionId: validateData.occasionId,
		},
		{ ...pagination, pageSize: '9999' },
	);

	return res.send({
		firstInterview: firstInterview.list.length,
		secondInterview: secondInterview.list.length,
		thirdInterview: thirdInterview.list.length,
		fourthInterview: fourthInterview.list.length,
		interviewsCandidates: interviewsCandidates.list.length,
		interviewsPassed: interviewsPassed.list.length,
		interviewsFailed: interviewsFailed.list.length,
	});
};

// Fix issues 117278: user create event time and delete and then create new time
export const cleanEventTimeManyOccurrenceOccasion = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const { isGetData, occasionId, interviewStep } = req.body;
		const allOccasions = await db.occasions.findAll({
			where: occasionId ? { occasionId } : {},
			include: [
				{
					model: db.occurrences,
					where: {
						interviewStep,
					},
					attributes: ['occurrenceId', 'interviewStep'],
					required: true,
					include: [
						{
							model: db.occurrenceDetailModel,
							attributes: ['occurrenceId', 'occurrenceDetailId', 'eventDay', 'deletedAt', 'createdAt'],
						},
					],
				},
				{
					model: db.categories,
					attributes: ['title'],
				},
			],
			attributes: ['occasionId', 'title'],
			order: [[{ model: db.occurrences, as: 'occurrences' }, 'occurrenceId', 'DESC']],
		});

		if (!allOccasions.length)
			return res.sendStatus(200).send({
				notFound: true,
			});

		const listOccasionEmptyTime = [] as any;
		const listOccasionNotEmptyTime = [] as any;
		const dataError = [] as any;

		for (const currentOccasion of allOccasions) {
			const occasionJson = currentOccasion.toJSON();
			const occurrencesOccasion = currentOccasion.occurrences;

			// Get occasion error that user removed all time
			const allEmpty = occurrencesOccasion?.every((occ: any) => occ.occurrenceDetails.length === 0);
			if (allEmpty) {
				listOccasionEmptyTime.push(currentOccasion);
				continue;
			}

			// Get occasion error
			if (occurrencesOccasion && occurrencesOccasion.length > 1) {
				// Get occurrence error that user deleted all time
				const emptyTime = occurrencesOccasion.filter((it) => isEmpty(it.occurrenceDetails));

				let isPushed = false;
				if (!isEmpty(emptyTime)) {
					dataError.push(occasionJson);
					isPushed = true;
					listOccasionEmptyTime.push({ ...occasionJson, occurrences: emptyTime });
				}

				// Get occurrence error that user created new time
				const notEmpty = occurrencesOccasion.filter((it) => !isEmpty(it.occurrenceDetails));
				if (!isEmpty(notEmpty)) {
					if (!isPushed) {
						dataError.push(occasionJson);
					}
					listOccasionNotEmptyTime.push({ ...occasionJson, occurrences: notEmpty });
				}
			}
		}

		const occurrenceIdsRemoved = [] as any;
		const occasionDetailsUpdated = [] as any;
		const registrationNeedUpdateOccurrence = [] as any;

		listOccasionEmptyTime.forEach((occasion: any) => {
			const occurrenceIds = occasion.occurrences.map((occurrence: any) => occurrence.occurrenceId);
			occurrenceIdsRemoved.push(...occurrenceIds);
		});

		// Get all registration by interview step
		const listRegistration = await db.registrations.findAll({
			include: [
				{
					model: db.occurrences,
					where: {
						interviewStep,
					},
					attributes: ['occurrenceId'],
					required: true,
				},
			],
		});

		const listOccurrenceIdInRegistration = listRegistration.map((it) => it.occurrenceId);

		listOccasionNotEmptyTime.forEach((occasion: any) => {
			// Get all occurrence that user registered
			const currentOccurrencesInRegistration = occasion.occurrences.filter((occurrence: any) =>
				listOccurrenceIdInRegistration.includes(occurrence.occurrenceId),
			);

			const currentOccurrenceIdsInRegistration = currentOccurrencesInRegistration.map((it: any) => it.occurrenceId);

			occasion.occurrences.forEach((occurrence: any, index: any) => {
				const occurrenceDetailIds = occurrence.occurrenceDetails.map((detail: any) => detail.occurrenceDetailId);

				if (currentOccurrenceIdsInRegistration.length === 1) {
					const primaryOccurrenceId = currentOccurrenceIdsInRegistration[0];
					if (!currentOccurrenceIdsInRegistration.includes(occurrence.occurrenceId)) {
						occasionDetailsUpdated.push({
							occurrenceId: primaryOccurrenceId,
							occurrenceDetailIds,
						});
						occurrenceIdsRemoved.push(occurrence.occurrenceId);
					}
				} else if (currentOccurrenceIdsInRegistration.length > 1) {
					const primaryOccurrenceId = currentOccurrenceIdsInRegistration[0];
					const remainOccurrenceRegistration = currentOccurrenceIdsInRegistration.slice(1);

					if (occurrence.occurrenceId !== primaryOccurrenceId) {
						const listRegistrationNeedUpdate = listRegistration.filter((it: any) =>
							remainOccurrenceRegistration.includes(it.occurrenceId),
						);
						registrationNeedUpdateOccurrence.push({
							occurrenceId: primaryOccurrenceId,
							registrationIds: listRegistrationNeedUpdate.map((it) => it.registrationId),
						});
						occasionDetailsUpdated.push({
							occurrenceId: occurrence.occurrenceId,
							occurrenceDetailIds,
						});
						occurrenceIdsRemoved.push(occurrence.occurrenceId);
					}
				} else if (index > 0) {
					const primaryOccurrenceId = occasion.occurrences[0].occurrenceId;
					// Only process additional occurrences when not registered
					occurrenceIdsRemoved.push(occurrence.occurrenceId);
					occasionDetailsUpdated.push({
						occurrenceId: primaryOccurrenceId,
						occurrenceDetailIds,
					});
				}
			});
		});

		if (!isGetData) {
			let transaction = null;

			try {
				transaction = await db.sequelize.transaction();
				for (const occasionDetailUpdated of occasionDetailsUpdated) {
					await db.occurrenceDetailModel.update(
						{
							occurrenceId: occasionDetailUpdated.occurrenceId,
						},
						{
							where: {
								occurrenceDetailId: { [Op.in]: occasionDetailUpdated.occurrenceDetailIds },
							},
							transaction,
						},
					);
				}
				for (const registrationNeedUpdate of registrationNeedUpdateOccurrence) {
					await db.registrations.update(
						{
							occurrenceId: registrationNeedUpdate.occurrenceId,
						},
						{
							where: {
								registrationId: { [Op.in]: registrationNeedUpdate.registrationIds },
							},
							transaction,
						},
					);
				}
				await db.occurrences.destroy({
					where: {
						occurrenceId: {
							[Op.in]: occurrenceIdsRemoved,
						},
					},
					transaction,
				});
				await transaction.commit();
			} catch (err) {
				if (transaction) await transaction.rollback();
				throw new AppError(BAD_REQUEST, 'Update data invalid');
			}
		}

		res.status(200).json({
			dataError,
			occurrenceIdsRemoved,
			occasionDetailsUpdated,
			totalDataError: dataError.length,
			listOccasionNotEmptyTime,
			registrationNeedUpdateOccurrence,
		});
	} catch (e) {
		next(e);
	}
};

export const scanDataOccasionMemberErrorController = async (req: Request, res: Response, next: NextFunction) => {
	let transaction: Transaction | null = null;
	try {
		const { isUpdated, occurrenceDetailId } = req.body;
		const dataOccurrenceDetailError = await db.occurrenceDetailModel.findAll({
			where: {
				occurrenceId: { [Op.ne]: null },
			},
			attributes: [
				'occurrenceDetailId',
				'totalAttendees',
				'maxAttendee',
				[
					Sequelize.literal(`(
				    SELECT COUNT(*)
				    FROM registrations r
				    WHERE r.occurrenceDetailId = occurrenceDetail.occurrenceDetailId
				    AND r.deletedAt IS NULL
				    AND r.cancelledAt IS NULL
				  )`),
					'registrationCount',
				],
			],
			having: {
				[Op.or]: [Sequelize.literal('registrationCount != totalAttendees')],
			},
			raw: true,
		});

		if (isUpdated) {
			if (occurrenceDetailId) {
				transaction = await db.sequelize.transaction();
				const dataError: any = dataOccurrenceDetailError.find((it) => it.occurrenceDetailId === occurrenceDetailId);
				if (dataError) {
					await db.occurrenceDetailModel.update(
						{
							totalAttendees: dataError.registrationCount as number,
						},
						{
							where: {
								occurrenceDetailId,
							},
						},
					);
				}
				await transaction.commit();
				return res.status(200).json({ succeed: true, dataError });
			} else {
				for (const occurrenceDetail of dataOccurrenceDetailError as any[]) {
					transaction = await db.sequelize.transaction();
					await db.occurrenceDetailModel.update(
						{
							totalAttendees: occurrenceDetail.registrationCount as number,
						},
						{
							where: {
								occurrenceDetailId,
							},
						},
					);
					await transaction.commit();
				}
				return res.status(200).json({ succeed: true, dataOccurrenceDetailError });
			}
		}

		const occurrenceDetailIds = dataOccurrenceDetailError.map((it) => it.occurrenceDetailId);

		const categories = await db.categories.findAll({
			attributes: ['categoryId', 'title'],
			include: [
				{
					model: db.occasions,
					attributes: ['occasionId', 'title'],
					include: [
						{
							model: db.occurrences,
							attributes: ['occurrenceId'],
							include: [
								{
									model: db.occurrenceDetailModel,
									attributes: ['eventDay', 'startTime', 'endTime', 'occurrenceDetailId'],
									where: {
										occurrenceDetailId: { [Op.in]: occurrenceDetailIds },
									},
									required: true,
									include: [
										{
											model: db.registrations,
											attributes: ['registrationId'],
											where: {
												deletedAt: null,
											},
											required: false,
											include: [
												{
													model: db.members,
													attributes: ['memberId', 'fullName', 'rollNumber'],
													required: false,
												},
											],
										},
									],
								},
							],
							required: true,
						},
					],
					required: true,
				},
			],
		});

		res.json({
			occurrenceDetailErrorLength: dataOccurrenceDetailError.length,
			categoriesErr: categories.length,
			dataOccurrenceDetailError,
			categories,
		});
	} catch (error) {
		if (transaction !== null) {
			await transaction.rollback();
		}
		res.status(500).json({ message: 'Internal Server Error' });
	}
};

export const scanDataAndRemoveDataEventTimeErrorController = async (
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	let transaction: Transaction | null = null;
	const { isUpdate } = req.body;
	try {
		const occurrenceDetails = await db.occurrenceDetailModel.findAll({
			include: [
				{
					model: db.occurrences,
					where: {
						deletedAt: { [Op.ne]: null },
					},
					paranoid: false,
					attributes: ['occurrenceId'],
				},
			],
			attributes: ['occurrenceDetailId'],
		});

		const listOccurrenceDetailIds = occurrenceDetails.map((it) => it.occurrenceDetailId).filter(Boolean);
		if (isUpdate && listOccurrenceDetailIds.length > 0) {
			transaction = await db.sequelize.transaction();
			await db.occurrenceDetailModel.destroy({
				where: {
					occurrenceDetailId: { [Op.in]: listOccurrenceDetailIds },
				},
			});
			await transaction.commit();
		}

		res.json({
			totalData: listOccurrenceDetailIds.length,
			listOccurrenceDetailIds,
			occurrenceDetails,
		});
	} catch (e) {
		if (transaction !== null) {
			await transaction.rollback();
		}
		next(e);
	}
};

export const updateRegistrationController = async (req: Request, res: Response, next: NextFunction) => {
	let transaction: Transaction | null = null;
	let totalRedis = 0;
	const { isGetData, occurrenceDetailId, registrationId } = req.body;

	try {
		if (isGetData && registrationId) {
			const registration = await db.registrations.findOne({
				where: {
					registrationId,
				},
				include: [
					{
						model: db.occurrenceDetailModel,
					},
				],
			});
			return res.status(200).json({ registration });
		}

		// Update change occurrenceDetailId of registration
		if (occurrenceDetailId && registrationId) {
			transaction = await db.sequelize.transaction();
			await db.registrations.update(
				{
					occurrenceDetailId,
				},
				{
					where: {
						registrationId,
					},
					transaction,
				},
			);
			await OccurrenceDetailService.incrementTotalAttendeeOccurrenceDetail(occurrenceDetailId, transaction);
			await transaction.commit();
			totalRedis = await redisCacheService.increaseOccurrenceDetailRegisterCountForEvent(occurrenceDetailId, 1);
		}

		res.status(200).json({ success: true });
	} catch (e) {
		if (occurrenceDetailId && totalRedis !== 0) {
			await redisCacheService.decreaseOccurrenceDetailRegisterCountForEvent(occurrenceDetailId, 1);
		}
		if (transaction !== null) {
			await transaction.rollback();
		}
		next(e);
	}
};

export const restoreOccurrenceDetailController = async (req: Request, res: Response, next: NextFunction) => {
	let transaction: Transaction | null = null;
	let totalRedis = 0;
	const { isGetData, occurrenceDetailId, occurrenceId } = req.body;

	try {
		if (isGetData && occurrenceDetailId) {
			const occurrenceDetailRollback = await db.occurrenceDetailModel.findOne({
				where: { occurrenceDetailId },
				include: [
					{
						model: db.occurrences,
						paranoid: false,
					},
				],
				paranoid: false,
			});
			return res.status(200).json({ occurrenceDetailRollback });
		}

		// Restore data occurrence detail
		if (occurrenceDetailId && occurrenceId) {
			transaction = await db.sequelize.transaction();

			const occurrenceDetailRollback = await db.occurrenceDetailModel.findOne({
				where: { occurrenceDetailId },
				paranoid: false,
				transaction,
			});

			if (occurrenceDetailRollback?.deletedAt) {
				await occurrenceDetailRollback.restore({ transaction });
			}

			await db.occurrenceDetailModel.update(
				{ occurrenceId },
				{
					where: { occurrenceDetailId },
					paranoid: false,
					transaction,
				},
			);

			await redisCacheService.setOccurrenceDetailRegisterCountForEvent(occurrenceDetailId, 0);
			totalRedis = await redisCacheService.increaseOccurrenceDetailRegisterCountForEvent(occurrenceDetailId, 1);

			await transaction.commit();
		}

		res.status(200).json({ success: true });
	} catch (e) {
		if (occurrenceDetailId && totalRedis !== 0) {
			await redisCacheService.decreaseOccurrenceDetailRegisterCountForEvent(occurrenceDetailId, 1);
		}

		if (transaction !== null) {
			await transaction.rollback();
		}
		next(e);
	}
};

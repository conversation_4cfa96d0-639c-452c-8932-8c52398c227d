import Redis from 'ioredis';
import redisClient from '~utilities/initRedis';
import { RedisKeys } from '../config/redis.config';
import { systemConfig } from '~config';
import { InterviewStepSchema } from '~schemas/occurrenceDetail';

class RedisCacheService {
	private client: Redis;

	constructor(redisClient: Redis) {
		this.client = redisClient;
	}

	private generateKey(parts: string[]): string {
		return parts.join(':');
	}

	async setOccurrenceRegisterCount(occurrenceId: number, count: number): Promise<void> {
		const key = this.generateKey([
			RedisKeys.EVENT_OCCURRENCE,
			occurrenceId.toString(),
			RedisKeys.EVENT_CLIENT_REGISTRATION_COUNT,
		]);
		if (systemConfig.CONSOLE_ONLY) {
			console.log('setOccurrenceRegisterCount', { key, count });
		}
		await this.client.set(key, count.toString());
	}

	async increaseOccurrenceRegisterCount(occurrenceId: number, count: number): Promise<number> {
		const key = this.generateKey([
			RedisKeys.EVENT_OCCURRENCE,
			occurrenceId.toString(),
			RedisKeys.EVENT_CLIENT_REGISTRATION_COUNT,
		]);
		if (systemConfig.CONSOLE_ONLY) {
			console.log('increaseOccurrenceRegisterCount', { key, count });
		}
		return this.client.incrby(key, count.toString());
	}

	async decreaseOccurrenceRegisterCount(occurrenceId: number, count: number): Promise<void> {
		const key = this.generateKey([
			RedisKeys.EVENT_OCCURRENCE,
			occurrenceId.toString(),
			RedisKeys.EVENT_CLIENT_REGISTRATION_COUNT,
		]);
		if (systemConfig.CONSOLE_ONLY) {
			console.log('decreaseOccurrenceRegisterCount', { key, count });
		}
		await this.client.decrby(key, count);
	}

	async setOccurrenceDetailMaxAttendee(occurrenceDetailId: number, count: number): Promise<void> {
		const key = this.generateKey([
			RedisKeys.EVENT_OCCURRENCE_DETAIL,
			occurrenceDetailId.toString(),
			RedisKeys.EVENT_OCCURRENCE_DETAIL_MAX_ATTENDEE,
		]);
		if (systemConfig.CONSOLE_ONLY) {
			console.log('setOccurrenceRegisterCountForEvent', { key, count });
		}
		await this.client.set(key, count.toString());
	}

	async getOccurrenceDetailMaxAttendee(occurrenceDetailId: number): Promise<string | null> {
		const key = this.generateKey([
			RedisKeys.EVENT_OCCURRENCE_DETAIL,
			occurrenceDetailId.toString(),
			RedisKeys.EVENT_OCCURRENCE_DETAIL_MAX_ATTENDEE,
		]);
		return this.client.get(key);
	}

	async increaseOccurrenceDetailRegisterCountForEvent(occurrenceDetailId: number, count: number): Promise<number> {
		const key = this.generateKey([
			RedisKeys.EVENT_OCCURRENCE_DETAIL,
			occurrenceDetailId.toString(),
			RedisKeys.EVENT_CLIENT_REGISTRATION_COUNT,
		]);
		if (systemConfig.CONSOLE_ONLY) {
			console.log('increaseOccurrenceRegisterCountForEvent', { key, count });
		}
		return this.client.incrby(key, count.toString());
	}

	async decreaseOccurrenceDetailRegisterCountForEvent(occurrenceDetailId: number, count: number): Promise<number> {
		const key = this.generateKey([
			RedisKeys.EVENT_OCCURRENCE_DETAIL,
			occurrenceDetailId.toString(),
			RedisKeys.EVENT_CLIENT_REGISTRATION_COUNT,
		]);
		if (systemConfig.CONSOLE_ONLY) {
			console.log('decreaseOccurrenceRegisterCountForEvent', { key, count });
		}
		return this.client.decrby(key, count);
	}

	async setOccurrenceDetailsMaxAttendee(occurrenceDetailIds: number[], count: number): Promise<void> {
		for (const occurrenceDetailId of occurrenceDetailIds) {
			await this.setOccurrenceDetailMaxAttendee(occurrenceDetailId, count);
		}
	}

	async setOccurrenceDetailRegisterCountForEvent(occurrenceDetailId: number, count: number): Promise<string> {
		const key = this.generateKey([
			RedisKeys.EVENT_OCCURRENCE_DETAIL,
			occurrenceDetailId.toString(),
			RedisKeys.EVENT_CLIENT_REGISTRATION_COUNT,
		]);
		if (systemConfig.CONSOLE_ONLY) {
			console.log('increaseOccurrenceRegisterCountForEvent', { key, count });
		}
		return this.client.set(key, count.toString());
	}

	async createKeyLockOccurrenceCreation(occasionId: number, interviewStep: InterviewStepSchema): Promise<'OK' | null> {
		const lockKey = `occurrence-lock:${occasionId}:${interviewStep}`;
		return this.client.set(lockKey, 'locked', 'EX', 1, 'NX');
	}

	async setInformationNotification(data: Record<string, any>): Promise<string> {
		const key = this.generateKey([RedisKeys.NOTIFICATION_SETTING]);
		return this.client.set(key, JSON.stringify(data));
	}

	async getInformationNotification(): Promise<Record<string, any> | null> {
		const key = this.generateKey([RedisKeys.NOTIFICATION_SETTING]);
		const data = await this.client.get(key);
		if (!data) return null;
		return JSON.parse(data);
	}
}

export const redisCacheService = new RedisCacheService(redisClient);

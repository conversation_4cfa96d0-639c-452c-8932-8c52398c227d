import { Model, Sequelize, DataTypes, InferAttributes, InferCreationAttributes, CreationOptional } from 'sequelize';
import { DATABASE_TABLE_NAME } from '~config';

export class ClassCategory extends Model<InferAttributes<ClassCategory>, InferCreationAttributes<ClassCategory>> {
	//ATTRIBUTES
	declare classCategoryId: CreationOptional<number>;
	declare name: string;
	//TIMESTAMPS
	declare createdAt: CreationOptional<Date>;
	declare updatedAt: CreationOptional<Date>;
	static initClass = (sequelize: Sequelize) =>
		ClassCategory.init(
			{
				classCategoryId: {
					type: DataTypes.INTEGER({ unsigned: true }),
					allowNull: false,
					primaryKey: true,
					autoIncrement: true,
				},
				name: { type: DataTypes.STRING, allowNull: false, unique: true },
				createdAt: DataTypes.DATE,
				updatedAt: DataTypes.DATE,
			},
			{
				tableName: DATABASE_TABLE_NAME.CLASS_CATEGORY,
				timestamps: true,
				paranoid: false,
				sequelize: sequelize,
				charset: 'utf8mb4',
				collate: 'utf8mb4_general_ci',
				name: {
					singular: 'classCategory',
					plural: DATABASE_TABLE_NAME.CLASS_CATEGORY,
				},
			},
		);
}

import { QueryInterface, DataTypes, literal } from 'sequelize';
import { tableExists, tableNotExists } from '~utilities';
import { DATABASE_TABLE_NAME } from '~config';

module.exports = {
	up: async (queryInterface: QueryInterface) => {
		if (await tableNotExists(queryInterface, DATABASE_TABLE_NAME.INTERVIEW_SETTINGS)) {
			await queryInterface.createTable(DATABASE_TABLE_NAME.INTERVIEW_SETTINGS, {
				interviewSettingId: {
					type: DataTypes.INTEGER,
					primaryKey: true,
					autoIncrement: true,
				},
				startDate: {
					type: DataTypes.DATEONLY,
					allowNull: false,
				},
				endDate: {
					type: DataTypes.DATEONLY,
					allowNull: false,
				},
				settingType: {
					type: DataTypes.INTEGER,
					allowNull: false,
				},
				classCategoryIds: {
					type: DataTypes.JSON,
					allowNull: false,
				},
				createdAt: {
					allowNull: false,
					type: DataTypes.DATE,
					defaultValue: literal('CURRENT_TIMESTAMP'),
				},
				updatedAt: {
					allowNull: false,
					type: DataTypes.DATE,
					defaultValue: literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
				},
			});
		}
	},

	down: async (queryInterface: QueryInterface) => {
		if (await tableExists(queryInterface, DATABASE_TABLE_NAME.INTERVIEW_SETTINGS)) {
			await queryInterface.dropTable(DATABASE_TABLE_NAME.INTERVIEW_SETTINGS);
		}
	},
};

import { QueryInterface, DataTypes } from 'sequelize';
import { tableExists, tableNotExists } from '~utilities';
import { DATABASE_TABLE_NAME } from '~config';

module.exports = {
	up: async (queryInterface: QueryInterface) => {
		if (await tableNotExists(queryInterface, DATABASE_TABLE_NAME.INTERVIEW_SETTINGS)) {
			await queryInterface.createTable(DATABASE_TABLE_NAME.INTERVIEW_SETTINGS, {
				interviewSettingId: {
					type: DataTypes.INTEGER,
					primaryKey: true,
					autoIncrement: true,
				},
				start_date: {
					type: DataTypes.DATE,
					allowNull: false,
				},
				end_date: {
					type: DataTypes.DATE,
					allowNull: false,
				},
				setting_type: {
					type: DataTypes.INTEGER,
					allowNull: false,
				},
				classCategoryId: {
					type: DataTypes.ARRAY(DataTypes.INTEGER),
					references: {
						model: DATABASE_TABLE_NAME.CLASS_CATEGORY,
						key: 'classCategoryId',
					},
				},
				createdAt: {
					type: DataTypes.DATE,
					allowNull: false,
					defaultValue: new Date(),
				},
				updatedAt: {
					type: DataTypes.DATE,
					allowNull: false,
					defaultValue: new Date(),
				},
			});

			await queryInterface.bulkInsert(DATABASE_TABLE_NAME.INTERVIEW_SETTINGS, [
				{
					isEnabled: false,
					imageFilename: null,
					createdAt: new Date(),
					updatedAt: new Date(),
				},
			]);
		}
	},

	down: async (queryInterface: QueryInterface) => {
		if (await tableExists(queryInterface, DATABASE_TABLE_NAME.INTERVIEW_SETTINGS)) {
			await queryInterface.dropTable(DATABASE_TABLE_NAME.INTERVIEW_SETTINGS);
		}
	},
};

import { DATABASE_TABLE_NAME, STUDENT_TYPE } from '../config';
import { SequelizeUtility } from '../utilities';

import type { Migration } from 'sequelize-cli';

module.exports = {
	async up(queryInterface, Sequelize) {
		if (await SequelizeUtility.columnNotExists(queryInterface, DATABASE_TABLE_NAME.MEMBERS, 'studentType')) {
			await queryInterface.addColumn(DATABASE_TABLE_NAME.MEMBERS, 'studentType', {
				type: Sequelize.ENUM(...Object.values(STUDENT_TYPE)),
				allowNull: true,
			});
		}
	},

	async down(queryInterface) {
		if (await SequelizeUtility.columnExists(queryInterface, DATABASE_TABLE_NAME.MEMBERS, 'studentType')) {
			await queryInterface.removeColumn(DATABASE_TABLE_NAME.MEMBERS, 'studentType');
		}
	},
} satisfies Migration;

import { QueryInterface, DataTypes } from 'sequelize';
import { DATABASE_TABLE_NAME, NOTIFICATION_SETTING } from '~config';

export = {
	up: async (queryInterface: QueryInterface) => {
		await queryInterface.createTable(DATABASE_TABLE_NAME.NOTIFICATION_SETTING, {
			settingNotificationId: {
				type: DataTypes.INTEGER.UNSIGNED,
				autoIncrement: true,
				primaryKey: true,
				allowNull: false,
			},
			title: {
				type: DataTypes.STRING,
				allowNull: false,
			},
			content: {
				type: DataTypes.TEXT,
				allowNull: false,
			},
			isMemberNotificationEnabled: {
				type: DataTypes.BOOLEAN,
				defaultValue: false,
			},
			isAdminNotificationEnabled: {
				type: DataTypes.BOOLEAN,
				defaultValue: false,
			},
			isPublish: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: false,
			},
			settingType: {
				type: DataTypes.ENUM(...Object.values(NOTIFICATION_SETTING)),
				allowNull: true,
				defaultValue: NOTIFICATION_SETTING.notificationSetting,
			},
			createdAt: {
				type: DataTypes.DATE,
				allowNull: false,
				defaultValue: new Date(),
			},
			updatedAt: {
				type: DataTypes.DATE,
				allowNull: false,
				defaultValue: new Date(),
			},
		});
	},

	down: async (queryInterface: QueryInterface) => {
		await queryInterface.dropTable(DATABASE_TABLE_NAME.NOTIFICATION_SETTING);
	},
};

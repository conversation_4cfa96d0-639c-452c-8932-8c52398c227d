import {
	Association,
	CreationOptional,
	DataTypes,
	ForeignKey,
	InferAttributes,
	InferCreationAttributes,
	Model,
	NonAttribute,
	Sequelize,
} from 'sequelize';
import { Occurrence } from './occurrenceModel';
import { Member } from './memberModel';
import { <PERSON>mind<PERSON> } from './reminderModel';
import { Occasion } from './occasionModel';
import { Category } from './categoryModel';
import { Json } from 'sequelize/types/utils';
import { Campaign } from './campaignModel';
import { MemberGift } from './memberGiftModel';
import { OccurrenceDetail } from '~models/occurrenceDetailModel';
import { DATABASE_TABLE_NAME, MEMBER_ACTIONS_STATUS, MEMBER_INTERVIEW_STATUS } from '~config';

export class Registration extends Model<
	InferAttributes<
		Registration,
		{
			omit:
				| 'occurrence'
				| 'Occasion'
				| 'Category'
				| 'Member'
				| 'reminders'
				| 'Campaign'
				| 'memberGifts'
				| 'occurrenceDetail';
		}
	>,
	InferCreationAttributes<
		Registration,
		{
			omit:
				| 'occurrence'
				| 'Occasion'
				| 'Category'
				| 'Member'
				| 'reminders'
				| 'Campaign'
				| 'memberGifts'
				| 'occurrenceDetail';
		}
	>
> {
	declare registrationId: CreationOptional<number>;
	declare memberId: ForeignKey<Member['memberId'] | null>;
	declare occurrenceId: ForeignKey<Occurrence['occurrenceId']>;
	declare occurrenceDetailId: ForeignKey<OccurrenceDetail['occurrenceDetailId']>;
	declare categoryId: ForeignKey<Category['categoryId'] | null>;
	declare occasionId: ForeignKey<Occasion['occasionId'] | null>;
	declare campaignId: ForeignKey<Campaign['campaignId'] | null>;
	declare expected: number;
	declare attended: CreationOptional<number>;
	declare isNotified1: Date | null;
	declare isNotified2: Date | null;
	declare cancelledAt: Date | null;
	declare message: string | null;
	declare isRegistered: boolean;
	declare isFriends: boolean;
	declare isManual: boolean;
	declare remarks: string | null;
	declare note?: Json;
	declare isNotificationSent?: boolean;
	declare participantName: CreationOptional<string>;
	declare participantCount: CreationOptional<number>;
	declare companionCount: CreationOptional<number>;
	declare actualParticipantCount: CreationOptional<number | null>;
	declare actualCompanionCount: CreationOptional<number | null>;
	declare interviewStatus: CreationOptional<MEMBER_INTERVIEW_STATUS>;
	declare memberActionStatus: CreationOptional<MEMBER_ACTIONS_STATUS> | null;
	declare responseDeadlineDatePassInterview: CreationOptional<string> | null;
	declare isWin?: boolean;
	declare expiredAt: Date | null;

	//TIMESTAMPS
	declare createdAt: CreationOptional<Date>;
	declare updatedAt: CreationOptional<Date>;
	declare deletedAt: CreationOptional<Date> | null;
	//ASSOCIATION
	declare Category?: NonAttribute<Category>;
	declare Occasion?: NonAttribute<Occasion>;
	// declare Occurrence?: NonAttribute<Occurrence>;
	declare Member?: NonAttribute<Member>;
	declare reminders?: NonAttribute<Reminder[]>;
	declare Campaign?: NonAttribute<Campaign>;
	declare occurrenceDetail?: NonAttribute<OccurrenceDetail>;
	declare memberGifts?: NonAttribute<MemberGift[]>;
	declare occurrence?: NonAttribute<Occurrence>;

	declare static associations: {
		Category: Association<Category, Registration>;
		Occasion: Association<Occasion, Registration>;
		Occurrence: Association<Occurrence, Registration>;
		Member: Association<Member, Registration>;
		reminders: Association<Reminder, Registration>;
		Campaign: Association<Campaign, Registration>;
		memberGifts: Association<Registration, MemberGift>;
		occurrenceDetail: Association<Registration, OccurrenceDetail>;
	};
	static initClass = (sequelize: Sequelize) =>
		Registration.init(
			{
				registrationId: {
					type: DataTypes.INTEGER({ unsigned: true }),
					allowNull: false,
					primaryKey: true,
					autoIncrement: true,
				},
				expected: { type: DataTypes.TINYINT, allowNull: false },
				attended: {
					type: DataTypes.TINYINT({
						length: 1,
					}),
					allowNull: true,
					defaultValue: 0,
				},
				isNotified1: { type: DataTypes.DATE, allowNull: true, defaultValue: null },
				isNotified2: { type: DataTypes.DATE, allowNull: true, defaultValue: null },
				cancelledAt: { type: DataTypes.DATE, allowNull: true, defaultValue: null },
				message: { type: DataTypes.STRING(200), allowNull: true, defaultValue: null },
				isRegistered: { type: DataTypes.BOOLEAN, allowNull: false },
				isFriends: { type: DataTypes.BOOLEAN, allowNull: false },
				isManual: { type: DataTypes.BOOLEAN, allowNull: false },
				remarks: { type: DataTypes.STRING, allowNull: true, defaultValue: null },
				note: { type: DataTypes.JSON, allowNull: true },
				participantName: { type: DataTypes.STRING, allowNull: true, defaultValue: null },
				participantCount: { type: DataTypes.INTEGER.UNSIGNED, allowNull: true, defaultValue: null },
				companionCount: { type: DataTypes.INTEGER.UNSIGNED, allowNull: true, defaultValue: null },
				actualParticipantCount: { type: DataTypes.INTEGER.UNSIGNED, allowNull: true, defaultValue: null },
				actualCompanionCount: { type: DataTypes.INTEGER.UNSIGNED, allowNull: true, defaultValue: null },
				interviewStatus: {
					type: DataTypes.ENUM(...Object.values(MEMBER_INTERVIEW_STATUS)),
					allowNull: false,
					defaultValue: MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED,
				},
				memberActionStatus: {
					type: DataTypes.ENUM(...Object.values(MEMBER_ACTIONS_STATUS)),
					allowNull: true,
				},
				responseDeadlineDatePassInterview: { type: DataTypes.DATEONLY, allowNull: true },

				isWin: { type: DataTypes.BOOLEAN, allowNull: true, defaultValue: false },
				isNotificationSent: { type: DataTypes.BOOLEAN, defaultValue: false, allowNull: false },
				expiredAt: { type: DataTypes.DATE, allowNull: true, defaultValue: null },

				createdAt: DataTypes.DATE,
				updatedAt: DataTypes.DATE,
				deletedAt: DataTypes.DATE,
			},
			{
				sequelize: sequelize,
				timestamps: true,
				paranoid: true,
				charset: 'utf8mb4',
				collate: 'utf8mb4_general_ci',
				tableName: DATABASE_TABLE_NAME.REGISTRATIONS,
				modelName: 'Registration',
				name: {
					singular: 'Registration',
					plural: DATABASE_TABLE_NAME.REGISTRATIONS,
				},
			},
		);
}

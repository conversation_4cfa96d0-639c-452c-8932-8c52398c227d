import { Request, Response, NextFunction } from 'express';
import { NotificationSetting } from '~models/notificationSettingModel';
import { NOTIFICATION_SETTING, PUBLIC_NOTIFICATION_SETTING, SYSTEM_ERROR } from '~config';
import { redisCacheService } from '~services/redisCacheService';
import { AppError } from '~utilities';
import { MemberLineSchema } from '~schemas/member';

type DataPropertyNotiPopUp = {
	settingNotificationId?: number;
	setting?: number;
	title: string;
	content: string;
	isMemberNotificationEnabled: boolean;
	publicSettingStatus: PUBLIC_NOTIFICATION_SETTING;
	isAdminNotificationEnabled: boolean;
};

export const createSettingNotification = async (req: Request, res: Response, next: NextFunction) => {
	const { title, content, isMemberNotificationEnabled, publicSettingStatus, isAdminNotificationEnabled } =
		req.body as DataPropertyNotiPopUp;
	try {
		await NotificationSetting.create({
			title,
			content,
			isAdminNotificationEnabled,
			isMemberNotificationEnabled,
			publicSettingStatus,
			settingType: NOTIFICATION_SETTING.notificationSetting,
		});

		const latestPopup = await NotificationSetting.findOne({
			where: {
				settingType: NOTIFICATION_SETTING.notificationSetting,
			},
			order: [['settingNotificationId', 'DESC']],
		});

		if (latestPopup) {
			await redisCacheService.setInformationNotification(latestPopup.dataValues);
		}

		res.status(200).json(latestPopup);
	} catch (error) {
		next(error);
	}
};

export const updateSettingNotification = async (req: Request, res: Response, next: NextFunction) => {
	const {
		title,
		content,
		isMemberNotificationEnabled,
		publicSettingStatus,
		isAdminNotificationEnabled,
		settingNotificationId,
	} = req.body as DataPropertyNotiPopUp;

	try {
		const popup = await NotificationSetting.findOne({
			where: {
				settingNotificationId: settingNotificationId,
				settingType: NOTIFICATION_SETTING.notificationSetting,
			},
		});

		if (!popup) {
			throw new AppError(SYSTEM_ERROR, 'NotificationSetting not found', false);
		}

		await popup.update({
			title,
			content,
			isMemberNotificationEnabled,
			isAdminNotificationEnabled,
			publicSettingStatus,
		});

		const latestPopup = await NotificationSetting.findOne({
			where: {
				settingType: NOTIFICATION_SETTING.notificationSetting,
				settingNotificationId: settingNotificationId,
			},
		});

		if (latestPopup) {
			await redisCacheService.setInformationNotification(latestPopup.dataValues);
		}

		res.status(200).json(popup);
	} catch (error) {
		next(error);
	}
};

export const getSettingNotification = async (req: Request, res: Response, next: NextFunction) => {
	try {
		let data: any = {};
		const dataRedis = await redisCacheService.getInformationNotification();
		if (dataRedis) {
			data = dataRedis;
		} else {
			const dataQuery = await NotificationSetting.findOne({
				where: {
					settingType: NOTIFICATION_SETTING.notificationSetting,
				},
			});
			data = dataQuery;
			if (dataQuery != null) {
				await redisCacheService.setInformationNotification(dataQuery.dataValues);
			}
		}
		let idUser = null;
		if (res?.locals?.memberLine) {
			const memberLine = res.locals.memberLine as MemberLineSchema;
			idUser = memberLine.userId;
		} else if (req?.session?.user?.id) {
			idUser = req?.session?.user?.id;
		}
		res.status(200).json({ ...data, idUser });
	} catch (error) {
		next(error);
	}
};

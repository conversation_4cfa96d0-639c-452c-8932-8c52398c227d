import { CUSTOMER_REGISTRATION_NAME, DATABASE_TABLE_NAME } from '../config';
import { SequelizeUtility } from '../utilities';

import type { Migration } from 'sequelize-cli';

module.exports = {
	async up(queryInterface, Sequelize) {
		const NEW_COLUMNS = [
			CUSTOMER_REGISTRATION_NAME.REPRESENTATIVE_NAME,
			CUSTOMER_REGISTRATION_NAME.UNIVERSITY_STUDENT_EMAIL,
			CUSTOMER_REGISTRATION_NAME.ENTRY_CITY,
			CUSTOMER_REGISTRATION_NAME.UNIVERSITY_NAME,
		];
		for (const newColumn of NEW_COLUMNS) {
			if (await SequelizeUtility.columnNotExists(queryInterface, DATABASE_TABLE_NAME.MEMBERS, newColumn)) {
				await queryInterface.addColumn(DATABASE_TABLE_NAME.MEMBERS, newColumn, {
					type: newColumn === CUSTOMER_REGISTRATION_NAME.ENTRY_CITY ? Sequelize.JSON : Sequelize.STRING,
					allowNull: true,
					defaultValue: null,
				});
			}
		}
	},

	async down(queryInterface) {
		const NEW_COLUMNS = [
			CUSTOMER_REGISTRATION_NAME.REPRESENTATIVE_NAME,
			CUSTOMER_REGISTRATION_NAME.UNIVERSITY_STUDENT_EMAIL,
			CUSTOMER_REGISTRATION_NAME.ENTRY_CITY,
			CUSTOMER_REGISTRATION_NAME.UNIVERSITY_NAME,
		];
		for (const newColumn of NEW_COLUMNS) {
			if (await SequelizeUtility.columnExists(queryInterface, DATABASE_TABLE_NAME.MEMBERS, newColumn)) {
				await queryInterface.removeColumn(DATABASE_TABLE_NAME.MEMBERS, newColumn);
			}
		}
	},
} satisfies Migration;

import { Router } from 'express';
import { checkSession, checkLineProfile, checkSecretKey } from '../middlewares/authMiddleware';
import { errorLogger, errorResponder } from '../middlewares/errorMiddleware';
import {
	Authentication<PERSON>ontroller,
	ManagerController,
	SystemSettingController,
	MaintenanceModeController,
} from '../controllers';
import { router as MasterRouter } from './masterRouter';
import { router as LiffRouter } from './liffRouter';
import { router as LineRouter } from './lineRouter';
import { router as AppRouter } from './appRouter';
import { router as WebhookRouter } from './webhookRouter';
import { router as initDataRouter } from './initData.router';

const router = Router();

// router.use('/line', LineWebhook);
router.post('/login', AuthenticationController.Login);
router.get('/logout', AuthenticationController.Logout);
router.get('/sess', checkSession, ManagerController.checkAuthenticatedUser);
router.get('/auth', checkSession, ManagerController.checkAuthenticatedUser);
router.get('/favicon', SystemSettingController.getFavicon);
router.get('/logo', SystemSettingController.getLogo);
router.get('/store/pic', SystemSettingController.getStorePic);
router.get('/settings', SystemSettingController.getPublicSettings);
router.get('/maintenance/status', MaintenanceModeController.getMaintenanceStatus);

router.use('/m', checkSession, MasterRouter);
router.use('/line', LineRouter);
router.use('/app', AppRouter);
router.use('/liff', checkLineProfile, LiffRouter);
router.use('/webhook', WebhookRouter);
router.use('/initData', checkSecretKey, initDataRouter);
router.use(errorLogger, errorResponder);
export { router };

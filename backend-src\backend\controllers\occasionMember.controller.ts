import { NextFunction, Request, Response } from 'express';
import { BAD_REQUEST, MEMBER_INTERVIEW_STATUS, RESPONSE_SUCCESS, systemConfig } from '../config';
import { OccasionMemberService, SocketServerService } from '../services';
import {
	addMemberToOccasionSchema,
	adminChangeMemberStatusToScheduleAdjustmentSchema,
	adminGetMemberByInterviewStatusSchema,
	adminUpdateOccasionMemberNotesSchema,
} from '~schemas/occasionMember';
import { Op, Sequelize } from 'sequelize';
import { db } from '~models';
import { isTrue } from '~utilities/common.utils';

export const addMemberToOccasion = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const validateData = addMemberToOccasionSchema.parse(req.body);
		await OccasionMemberService.addMemberToOccasion(validateData);
		SocketServerService.emitAddMemberToOccasion();
		return res.sendStatus(RESPONSE_SUCCESS);
	} catch (e) {
		next(e);
	}
};

export const adminGetMemberByInterviewStatus = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const validateData = adminGetMemberByInterviewStatusSchema.parse(req.query);
		const data = await OccasionMemberService.adminGetMemberByInterview(validateData);
		return res.send(data);
	} catch (e) {
		next(e);
	}
};

export const adminChangeMemberStatusToScheduleAdjustment = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const validateData = adminChangeMemberStatusToScheduleAdjustmentSchema.parse(req.body);
		const data = await OccasionMemberService.adminChangeMemberStatusToScheduleAdjustment(validateData);
		SocketServerService.emitMemberInterviewScheduledAdded();
		SocketServerService.emitOccasionMemberChangeStatus();
		return res.send(data);
	} catch (e) {
		next(e);
	}
};

export const adminUpdateOccasionMemberNotes = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const validateData = adminUpdateOccasionMemberNotesSchema.parse(req.body);
		const { occasionMemberId, notes } = validateData;
		await OccasionMemberService.updateOccasionMemberByConditionWhere(
			{
				occasionMemberId,
			},
			{
				notes,
			},
		);
		return res.sendStatus(RESPONSE_SUCCESS);
	} catch (e) {
		next(e);
	}
};

export const removeDuplicateDataOccasionMember = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const duplicates = await db.occasionMembers.findAll({
			attributes: ['memberId', 'occasionId'],
			group: ['memberId', 'occasionId'],
			having: Sequelize.literal('COUNT(*) > 1'),
		});

		for (const duplicate of duplicates) {
			const { memberId, occasionId } = duplicate;

			const latestRecord = await db.occasionMembers.findOne({
				where: { memberId, occasionId },
				order: [['updatedAt', 'DESC']],
			});

			await db.occasionMembers.destroy({
				where: {
					memberId,
					occasionId,
					occasionMemberId: { [Op.ne]: latestRecord?.occasionMemberId },
				},
			});
		}

		res.status(200).json(RESPONSE_SUCCESS);
	} catch (e) {
		next(e);
	}
};

export const updateManuallyMemberToWaitingInterviewStatus = async (req: Request, res: Response, next: NextFunction) => {
	try {
		if (!isTrue(systemConfig.TEST_CHANGE_WAITING_STATUS)) {
			return res.sendStatus(RESPONSE_SUCCESS);
		}
		const { occasionIds } = req.body;

		if (!occasionIds || !Array.isArray(occasionIds) || occasionIds.length === 0) {
			return res.status(BAD_REQUEST).json({ error: 'occasionIds is required and must be a non-empty array' });
		}

		await db.registrations.update(
			{ interviewStatus: MEMBER_INTERVIEW_STATUS.WAITING_RESULTS },
			{
				where: {
					interviewStatus: MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED,
					attended: 0,
					occasionId: {
						[Op.in]: occasionIds,
					},
				},
			},
		);

		await db.occasionMembers.update(
			{ interviewStatus: MEMBER_INTERVIEW_STATUS.WAITING_RESULTS },
			{
				where: {
					interviewStatus: MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED,
					occasionId: {
						[Op.in]: occasionIds,
					},
				},
			},
		);

		return res.sendStatus(RESPONSE_SUCCESS);
	} catch (e) {
		next(e);
	}
};

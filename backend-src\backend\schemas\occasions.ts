import { z } from 'zod';
import { SORT_ORDER_DATABASE } from '~config';

export const getOccasionListSchema = z.object({
	page: z.string().min(1),
	pageSize: z.string().min(1),
	categoryId: z.string().min(1),
	sort: z.nativeEnum(SORT_ORDER_DATABASE).default(SORT_ORDER_DATABASE.DESC),
	sortKey: z.string().optional().default('occasionId'),
});

export const createOccasionSchema = z.object({
	categoryId: z.number().min(1),
	classCategoryId: z.number().min(1),
	title: z.string().min(1),
	description: z.string().optional().default(''),
	isMultiEvent: z.boolean().optional().default(true),
});

export const updateOccasionDisplaySchema = z.object({
	occasionId: z.number().min(1),
	isDisplayed: z.boolean(),
});

export const updateOccasionSchema = z.object({
	occasionId: z.string().min(1),
	classCategoryId: z.number().min(1),
	title: z.string().min(1),
});

export const adminGetInterviewStatisticSchema = z.object({
	occasionId: z.string().min(1),
	page: z.string().optional().default('1'),
	pageSize: z.string().optional().default('4'),
});

export type GetOccasionListSchema = z.infer<typeof getOccasionListSchema>;
export type CreateOccasionSchema = z.infer<typeof createOccasionSchema>;
export type UpdateOccasionDisplaySchema = z.infer<typeof updateOccasionDisplaySchema>;
export type UpdateOccasionSchema = z.infer<typeof updateOccasionSchema>;
export type AdminGetInterviewStatisticSchema = z.infer<typeof adminGetInterviewStatisticSchema>;

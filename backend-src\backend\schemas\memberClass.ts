import { z } from 'zod';

export const createMemberClassSchema = z.object({
	className: z.string().min(1),
	classCategoryId: z.number(),
	minimumFirstRoundInterviews: z.number().int(),
});

export const updateMemberClassSchema = z.object({
	className: z.string().min(1),
	classCategoryId: z.number(),
	minimumFirstRoundInterviews: z.number().int(),
});

export const getMemberClassListSchema = z.object({
	page: z.string().optional().default('1'),
	pageSize: z.string().optional().default('20'),
	isFetchAll: z.boolean().optional().default(false),
	isGeneralUniversity: z.boolean().optional().default(false),
	isFetchAllCategory: z.boolean().optional().default(false),
});

export const queryParamMemberClassSchema = z.object({
	memberClassId: z.string().refine((val) => !isNaN(parseInt(val)), {
		message: 'Value must be a valid integer string',
	}),
});

export type CreateMemberClassSchema = z.infer<typeof createMemberClassSchema>;
export type UpdateMemberClassSchema = z.infer<typeof updateMemberClassSchema>;
export type GetMemberClassListSchema = z.infer<typeof getMemberClassListSchema>;

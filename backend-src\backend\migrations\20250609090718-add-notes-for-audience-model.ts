import { DATABASE_TABLE_NAME } from '../config';
import { SequelizeUtility } from '../utilities';

import type { Migration } from 'sequelize-cli';

module.exports = {
	async up(queryInterface, Sequelize) {
		if (await SequelizeUtility.columnNotExists(queryInterface, DATABASE_TABLE_NAME.AUDIENCE, 'notes')) {
			await queryInterface.addColumn(DATABASE_TABLE_NAME.AUDIENCE, 'notes', {
				type: Sequelize.STRING,
				allowNull: true,
			});
		}
	},

	async down(queryInterface) {
		if (await SequelizeUtility.columnExists(queryInterface, DATABASE_TABLE_NAME.AUDIENCE, 'notes')) {
			await queryInterface.removeColumn(DATABASE_TABLE_NAME.AUDIENCE, 'notes');
		}
	},
} satisfies Migration;

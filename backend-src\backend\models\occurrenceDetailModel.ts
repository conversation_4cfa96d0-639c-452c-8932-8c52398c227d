import {
	CreationOptional,
	DataTypes,
	ForeignKey,
	InferAttributes,
	InferCreationAttributes,
	Model,
	NonAttribute,
	Sequelize,
} from 'sequelize';
import { DATABASE_TABLE_NAME, INTERVIEW_TYPE } from '~config';
import { Occurrence } from '~models/occurrenceModel';

export class OccurrenceDetail extends Model<
	InferAttributes<OccurrenceDetail>,
	InferCreationAttributes<OccurrenceDetail>
> {
	declare occurrenceDetailId: CreationOptional<number>;
	declare interviewType: INTERVIEW_TYPE;
	declare interviewLocation: string | null;
	declare eventDay: string;
	declare startTime: string;
	declare endTime: string;
	declare maxAttendee: number;
	declare totalAttendees: CreationOptional<number>;
	declare totalCancelled: CreationOptional<number>;
	declare notes: CreationOptional<string> | null;

	//TIMESTAMPS
	declare createdAt: CreationOptional<Date>;
	declare updatedAt: CreationOptional<Date>;
	declare deletedAt: CreationOptional<Date>;

	//ASSOCIATIONS
	declare occurrenceId: ForeignKey<Occurrence['occurrenceId']> | null;
	declare occurrence?: NonAttribute<Occurrence>;

	static initClass = (sequelize: Sequelize) =>
		OccurrenceDetail.init(
			{
				occurrenceDetailId: {
					type: DataTypes.INTEGER({ unsigned: true }),
					allowNull: false,
					primaryKey: true,
					autoIncrement: true,
				},
				interviewLocation: { type: DataTypes.STRING, allowNull: true },
				interviewType: {
					type: DataTypes.ENUM(...Object.values(INTERVIEW_TYPE)),
					allowNull: false,
					defaultValue: INTERVIEW_TYPE.ONLINE,
				},
				eventDay: { type: DataTypes.DATEONLY, allowNull: true, defaultValue: null },
				startTime: {
					type: DataTypes.TIME,
					allowNull: false,
				},
				endTime: {
					type: DataTypes.TIME,
					allowNull: true,
					validate: {
						isAfterStart() {
							if (this.startTime && this.endTime && this.endTime <= this.startTime) {
								throw new Error('endTime must be later startTime');
							}
						},
					},
				},
				maxAttendee: { type: DataTypes.INTEGER, allowNull: false, defaultValue: 0 },
				totalAttendees: { type: DataTypes.INTEGER, allowNull: false, defaultValue: 0 },
				totalCancelled: { type: DataTypes.INTEGER, allowNull: false, defaultValue: 0 },
				notes: { type: DataTypes.STRING(5000), allowNull: true },

				createdAt: DataTypes.DATE,
				updatedAt: DataTypes.DATE,
				deletedAt: DataTypes.DATE,
			},
			{
				sequelize: sequelize,
				timestamps: true,
				paranoid: true,
				charset: 'utf8mb4',
				collate: 'utf8mb4_general_ci',
				tableName: DATABASE_TABLE_NAME.OCCURRENCE_DETAIL,
				modelName: 'occurrenceDetail',
				name: {
					singular: 'occurrenceDetail',
					plural: DATABASE_TABLE_NAME.OCCURRENCE_DETAIL,
				},
			},
		);
}

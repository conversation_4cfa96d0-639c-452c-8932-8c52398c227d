import { NextFunction, Request, Response } from 'express';
import { OccurrenceDetailService, SocketServerService } from '../services';
import {
	checkExistsSameTimeSchema,
	getAllOccurrenceDetailsByOccurrenceId,
	getOccurrenceDetailSchema,
	removeOccurrenceDetailSchema,
	updateOccurrenceDetailSchema,
} from '~schemas/occurrenceDetail';
import { RESPONSE_SUCCESS } from '~config';
import { MemberLineSchema } from '~schemas/member';

export const getOccurrenceDetail = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const validateData = getOccurrenceDetailSchema.parse(req.params);
		const { occurrenceDetailId } = validateData;
		const data = await OccurrenceDetailService.getOccurrenceDetail(parseInt(occurrenceDetailId));
		return res.status(200).json(data);
	} catch (e) {
		next(e);
	}
};

export const checkExistSameTime = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const validateData = checkExistsSameTimeSchema.parse(req.body);
		const data = await OccurrenceDetailService.checkExistSameTime(validateData);
		return res.status(200).json(data);
	} catch (e) {
		next(e);
	}
};

export const memberGetAllOccurrenceDetailByOccurrenceId = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const memberLine = res.locals.memberLine as MemberLineSchema;
		const validateData = getAllOccurrenceDetailsByOccurrenceId.parse({ ...req.query, memberLine });
		const data = await OccurrenceDetailService.getAllOccurrenceDetailByOccurrenceId(validateData);
		return res.status(200).json(data);
	} catch (e) {
		next(e);
	}
};

export const removeOccurrenceDetail = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const validateData = removeOccurrenceDetailSchema.parse(req.params);
		const data = await OccurrenceDetailService.removeOccurrenceDetail(validateData);
		SocketServerService.emitOccurrenceDetailsRemoved(validateData);
		return res.status(200).json(data);
	} catch (e) {
		next(e);
	}
};

export const updateOccurrenceDetail = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const validateData = updateOccurrenceDetailSchema.parse(req.body);
		const { occurrenceDetail } = validateData;
		await OccurrenceDetailService.updateOccurrenceDetail({ occurrenceDetail });
		return res.status(200).json(RESPONSE_SUCCESS);
	} catch (e) {
		next(e);
	}
};

import { QueryInterface } from 'sequelize';
import { DATABASE_TABLE_NAME } from '~config';

export const up = async (queryInterface: QueryInterface) => {
	await queryInterface.sequelize.query(`
      ALTER TABLE ${DATABASE_TABLE_NAME.NOTIFICATION_SETTING}
      RENAME COLUMN isPublish TO publicSettingStatus;
    `);
};

export const down = async (queryInterface: QueryInterface) => {
	await queryInterface.sequelize.query(`
      ALTER TABLE ${DATABASE_TABLE_NAME.NOTIFICATION_SETTING}
      RENAME COLUMN publicSettingStatus TO isPublish;
    `);
};

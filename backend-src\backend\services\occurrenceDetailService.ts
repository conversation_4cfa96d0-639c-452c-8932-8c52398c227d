import { Op, Sequelize, Transaction, WhereAttributeHash } from 'sequelize';
import { BAD_REQUEST, CUSTOM_SHOW_MESSAGE, INTERVIEW_TYPE, MEMBER_INTERVIEW_STATUS, TIME_FORMAT } from '../config';
import { db } from '../models';
import { AppError, DATE_FORMAT_TYPE, getDateNowWithTimezone } from '../utilities';
import { formatTimeAdd } from '../utilities/commonDateTime';
import {
	CheckExistsSameTimeSchema,
	CreateOccurrenceDetailSchema,
	GetAllOccurrenceDetailsByOccurrenceId,
	RemoveOccurrenceDetailSchema,
	UpdateOccurrenceDetailSchema,
} from '~schemas/occurrenceDetail';
import { Occurrence } from '~models/occurrenceModel';
import { Occasion } from '~models/occasionModel';
import { get } from 'lodash';
import { redisCacheService } from '~services/redisCacheService';
import { RegistrationService, OccurrenceService } from '../services';
import { OccasionDetail } from '~models/occasionDetailModel';
import moment from 'moment';
import { maxTimeInterviewOverlap } from '../config/constants';

export type TypeAllBufferTime = {
	eventDayQuery: string;
	interviewTypeQuery?: string;
	endTimeBuffer: string;
	startTimeBuffer: string;
	startBufferOnOff?: number;
};

export const getConditionRuleGetOccurrenceDetails = async (memberId: number) => {
	const allBufferTimes = await RegistrationService.getBufferTimes(memberId);

	const today = getDateNowWithTimezone().format(DATE_FORMAT_TYPE.ISO_DATE);
	const timeNow = getDateNowWithTimezone().format(TIME_FORMAT);
	let queryOccurrenceDetails = {
		[Op.or]: [
			{
				eventDay: { [Op.gt]: today },
			},
			{
				eventDay: { [Op.eq]: today },
				startTime: { [Op.gte]: timeNow },
			},
		],
	} as any;
	if (allBufferTimes.length > 0) {
		queryOccurrenceDetails = {
			[Op.or]: [
				{
					eventDay: { [Op.gt]: today },
					[Op.not]: {
						[Op.or]: [...allBufferTimes],
					},
				},
				{
					eventDay: { [Op.eq]: today },
					startTime: { [Op.gte]: timeNow },
					[Op.not]: {
						[Op.or]: [...allBufferTimes],
					},
				},
			],
		};
	}
	return queryOccurrenceDetails;
};

export const getConditionRuleGetOccurrenceDetailsV2 = async (memberId: number) => {
	const allBufferTimes = await RegistrationService.getBufferTimesV2(memberId);
	const today = getDateNowWithTimezone().format(DATE_FORMAT_TYPE.ISO_DATE);
	const timeNow = getDateNowWithTimezone().format(TIME_FORMAT);
	let queryOccurrenceDetails = {
		[Op.or]: [
			{
				eventDay: { [Op.gt]: today },
			},
			{
				eventDay: { [Op.eq]: today },
				startTime: { [Op.gte]: timeNow },
			},
		],
	} as any;

	return { queryOccurrenceDetails, allBufferTimes };
};

export const occurrenceDetailsHaveConflicTimeAndNoConflicTime = ({
	allBufferTimes,
	occurrenceDetails,
}: {
	allBufferTimes: TypeAllBufferTime[];
	occurrenceDetails: OccasionDetail[];
}) => {
	const toMinutes = (time: string) => {
		const [h, m] = time.split(':').map(Number);
		return h * 60 + m;
	};

	return occurrenceDetails.map((detail) => {
		const { eventDay, startTime, endTime, interviewType } = detail.get({ plain: true }) as any;
		const detailStart = toMinutes(startTime);
		const detailEnd = toMinutes(endTime);

		const conflictExists = allBufferTimes.some((buffer) => {
			if (buffer.eventDayQuery !== eventDay) return false;

			// chỉ so sánh buffer cùng loại (ONLINE hoặc OFFLINE)
			if (buffer.interviewTypeQuery !== interviewType) return false;

			const bufStart = toMinutes(buffer.startTimeBuffer);
			const bufEnd = toMinutes(buffer.endTimeBuffer);

			return detailStart < bufEnd && detailEnd > bufStart;
		});

		return {
			...detail.get({ plain: true }),
			checkConflict: conflictExists,
		};
	});
};

export const getOccurrenceDetail = async (occurrenceDetailId: number) => {
	const occurrenceDetail = await db.occurrenceDetailModel.findOne({
		where: { occurrenceDetailId },
		include: [
			{
				model: db.occurrences,
				attributes: ['interviewStep', 'responseDeadlineDate', 'isDisplayed'],
				required: true,
				include: [
					{
						association: db.occurrences.associations.registrations,
						where: {
							cancelledAt: null,
							occurrenceDetailId: occurrenceDetailId,
						},
						attributes: {
							exclude: ['isRegistered', 'isFriends', 'isNotified1', 'isNotified2', 'updatedAt', 'deletedAt'],
						},
						include: [{ model: db.members }],
						required: false,
					},
					{
						model: db.occasions,
						include: [{ model: db.categories, attributes: ['title'] }],
					},
				],
				order: [[db.registrations, 'registrationId', 'asc']],
			},
		],
	});
	if (!occurrenceDetail) {
		throw new AppError(BAD_REQUEST, `occurrence detail ${occurrenceDetailId} does not exist`, false);
	}

	return occurrenceDetail;
};

export const checkExistSameTime = async (data: CheckExistsSameTimeSchema) => {
	const { occasionId, occurrenceDetails } = data;
	try {

		const getKey = (eventDay: string, startTime: string, endTime: string) =>
			`${eventDay}_${startTime}_${endTime}`;

		const listKeyTimeInterview: Record<string, number> = {};
		const errorsData: number[] = [];

		for (const [index, { eventDay, startTime, endTime }] of occurrenceDetails.entries()) {
			const key = getKey(eventDay, startTime, endTime);
			listKeyTimeInterview[key] = (listKeyTimeInterview[key] || 0) + 1;

			if (listKeyTimeInterview[key] > maxTimeInterviewOverlap) {
				errorsData.push(index);
			}
		}

		if (errorsData.length > 0) return { errorsData };

		const dbRecords = await db.occurrenceDetailModel.findAll({
			include: [
				{
					model: db.occurrences,
					where: { occasionId },
					attributes: [],
				},
			],
			attributes: ['eventDay', 'startTime', 'endTime'],
		});

		const dbKeyCounts: Record<string, number> = {};
		for (const { eventDay, startTime, endTime } of dbRecords) {
			const key = getKey(
				eventDay,
				moment(startTime, 'HH:mm:ss').format('HH:mm'),
				moment(endTime, 'HH:mm:ss').format('HH:mm'),
			);
			dbKeyCounts[key] = (dbKeyCounts[key] || 0) + 1;
		}

		for (const [index, { eventDay, startTime, endTime }] of occurrenceDetails.entries()) {
			const key = getKey(eventDay, startTime, endTime);
			const totalCount = (dbKeyCounts[key] || 0) + (listKeyTimeInterview[key] || 0);

			if (totalCount > maxTimeInterviewOverlap) {
				errorsData.push(index);
			}
		}

		return { errorsData };
	} catch (err: any) {
		throw new AppError(BAD_REQUEST, err);
	}
};

export const updateOccurrenceDetail = async (data: UpdateOccurrenceDetailSchema, transaction?: Transaction) => {
	const { occurrenceDetail } = data;
	const currentOccurrenceDetail = await db.occurrenceDetailModel.findOne({
		where: { occurrenceDetailId: occurrenceDetail.occurrenceDetailId },
		include: [
			{
				model: db.occurrences,
				include: [{ model: db.occasions }],
			},
		],
	});
	const occasionId = currentOccurrenceDetail?.occurrence?.Occasion?.occasionId;
	const occurrenceId = currentOccurrenceDetail?.occurrence?.occurrenceId;
	const interviewStep = currentOccurrenceDetail?.occurrence?.interviewStep;
	const currentMemberAdded = await db.occasionMembers.count({
		where: {
			occasionId,
			memberInterviewCurrentStep: interviewStep,
			interviewStatus: MEMBER_INTERVIEW_STATUS.SCHEDULE_ADJUSTMENT,
		},
	});
	const allOccurrenceDetail = await db.occurrenceDetailModel.findAll({
		include: [
			{
				model: db.occurrences,
				where: { occurrenceId },
			},
		],
	});
	let totalSlot = 0;
	const newMaxAttendee = occurrenceDetail?.maxAttendee || 0;
	if (currentOccurrenceDetail && currentOccurrenceDetail.totalAttendees > newMaxAttendee) {
		throw new AppError(
			BAD_REQUEST,
			`The new max attendees must be greater than the old max attendees ${CUSTOM_SHOW_MESSAGE}`,
		);
	}
	if (allOccurrenceDetail.length) {
		totalSlot =
			allOccurrenceDetail.reduce((acc, item) => {
				if (item.occurrenceDetailId === occurrenceDetail.occurrenceDetailId) {
					return acc + newMaxAttendee - item.totalAttendees;
				}
				const remain = item.maxAttendee - item.totalAttendees;
				return acc + remain;
			}, 0) || 0;
	}

	if (totalSlot < currentMemberAdded) {
		throw new AppError(
			BAD_REQUEST,
			`The new max attendees must be greater than the old max attendees ${CUSTOM_SHOW_MESSAGE}`,
		);
	}
	await db.occurrenceDetailModel.update(occurrenceDetail, {
		where: { occurrenceDetailId: occurrenceDetail.occurrenceDetailId },
		transaction,
	});
	await redisCacheService.setOccurrenceDetailsMaxAttendee(
		[occurrenceDetail.occurrenceDetailId] as number[],
		newMaxAttendee,
	);
	return true;
};

export const getAllOccurrenceDetailByOccurrenceId = async (data: GetAllOccurrenceDetailsByOccurrenceId) => {
	const { occurrenceId, memberLine } = data;
	const currentMember = await db.members.findOne({
		where: {
			lineId: memberLine.userId,
		},
		attributes: ['memberId', 'memberClassId'],
	});

	if (!currentMember) {
		throw new AppError(BAD_REQUEST, 'Member is not found.', false);
	}
	const { memberId } = currentMember;
	const occurrence = await db.occurrences.findByPk(occurrenceId, {
		include: [
			{
				model: Occasion,
				attributes: ['title'],
				include: [{ model: db.categories, attributes: ['title'] }],
			},
		],
		attributes: ['interviewStep', 'responseDeadlineDate'],
	});

	const { queryOccurrenceDetails, allBufferTimes } = (await getConditionRuleGetOccurrenceDetailsV2(memberId)) as any;

	let occurrenceDetails: any = await db.occurrenceDetailModel.findAll({
		where: {
			occurrenceId,
			...queryOccurrenceDetails,
		},
		include: [
			{
				model: Occurrence,
				include: [
					{
						model: Occasion,
						attributes: ['title'],
						include: [{ model: db.categories, attributes: ['title'] }],
					},
				],
				attributes: ['interviewStep', 'responseDeadlineDate', 'notes'],
			},
		],
		order: [
			['eventDay', 'ASC'],
			['startTime', 'ASC'],
		],
	});

	if (allBufferTimes.length > 0) {
		occurrenceDetails = occurrenceDetailsHaveConflicTimeAndNoConflicTime({ allBufferTimes, occurrenceDetails });
	}

	return {
		occurrence,
		occurrenceDetails,
	};
};

export const findAllOccurrenceDetailByWhereCondition = async (occurrenceDetailWhere: WhereAttributeHash) => {
	return db.occurrenceDetailModel.findAll({ where: occurrenceDetailWhere });
};

export const getOccurrenceDetailById = async (occurrenceDetailId: number) => {
	return db.occurrenceDetailModel.findOne({
		where: {
			occurrenceDetailId,
		},
		include: [
			{
				model: Occurrence,
				required: true,
				include: [
					{
						model: Occasion,
						required: true,
					},
				],
			},
		],
	});
};

export const incrementTotalAttendeeOccurrenceDetail = async (
	occurrenceDetailId: number,
	transaction?: Transaction | null,
) => {
	return db.occurrenceDetailModel.increment(
		{ totalAttendees: 1 },
		{
			where: { occurrenceDetailId },
			transaction,
		},
	);
};

export const incrementTotalCancelledOccurrenceDetail = async (
	occurrenceDetailId: number,
	transaction?: Transaction | null,
) => {
	return db.occurrenceDetailModel.increment(
		{ totalCancelled: 1 },
		{
			where: { occurrenceDetailId },
			transaction,
		},
	);
};

export const decrementTotalAttendeeOccurrenceDetail = async (
	occurrenceDetailId: number,
	transaction?: Transaction | null,
) => {
	return db.occurrenceDetailModel.increment(
		{ totalAttendees: -1 },
		{
			where: { occurrenceDetailId },
			transaction,
		},
	);
};

export const removeOccurrenceDetail = async (data: RemoveOccurrenceDetailSchema, transaction?: Transaction) => {
	const { occurrenceDetailId } = data;
	const currentOccurrenceDetail = await db.occurrenceDetailModel.findOne({
		where: {
			occurrenceDetailId,
		},
		include: [
			{
				model: db.occurrences,
				include: [{ model: db.occasions }],
				attributes: ['occurrenceId', 'interviewStep', 'occasionId'],
			},
		],
	});
	if (!currentOccurrenceDetail) {
		throw new AppError(BAD_REQUEST, `occurrence detail ${data.occurrenceDetailId} does not exist`, false);
	}
	const occasionId = currentOccurrenceDetail?.occurrence?.Occasion?.occasionId;
	const occurrenceId = currentOccurrenceDetail?.occurrence?.occurrenceId;
	const interviewStep = currentOccurrenceDetail?.occurrence?.interviewStep;
	const currentMemberAdded = await db.occasionMembers.count({
		where: {
			occasionId,
			memberInterviewCurrentStep: interviewStep,
			interviewStatus: MEMBER_INTERVIEW_STATUS.SCHEDULE_ADJUSTMENT,
		},
	});
	const allOccurrenceDetail = await db.occurrenceDetailModel.findAll({
		include: [
			{
				model: db.occurrences,
				where: { occurrenceId },
			},
		],
	});
	let totalSlot = 0;
	if (allOccurrenceDetail.length) {
		totalSlot =
			allOccurrenceDetail.reduce((acc, item) => {
				const remain = item.maxAttendee - item.totalAttendees;
				return acc + remain;
			}, 0) || 0;
	}

	if (totalSlot - currentOccurrenceDetail.maxAttendee < currentMemberAdded) {
		throw new AppError(
			BAD_REQUEST,
			`The new max attendees must be greater than the old max attendees ${CUSTOM_SHOW_MESSAGE}`,
		);
	}
	const registrations = await db.registrations.findAll({
		where: {
			occurrenceDetailId,
		},
	});
	if (!registrations.length) {
		const occasionId = get(currentOccurrenceDetail, 'occurrence.occasionId') as number;
		const memberIds = registrations.map((registration) => registration.memberId) as number[];
		await db.occasionMembers.update(
			{
				interviewStatus: MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED,
			},
			{
				where: {
					memberId: { [Op.in]: memberIds },
					occasionId,
				},
			},
		);
	}
	await db.occurrenceDetailModel.destroy({
		where: { occurrenceDetailId: data.occurrenceDetailId },
		transaction,
	});
	await OccurrenceService.deleteOccurrenceIfNoOccurrenceDetail(occurrenceId as number);
	return true;
};

export const createOccurrenceDetail = async (data: CreateOccurrenceDetailSchema[], transaction?: Transaction) => {
	return db.occurrenceDetailModel.bulkCreate(data, {
		transaction,
	});
};

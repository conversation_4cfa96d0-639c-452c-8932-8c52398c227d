import { db } from '../models';
import { CreateOrUpdateBufferEventTimeSettingSchema } from '~schemas/bufferEventTimeSetting';

export const createOrUpdateNewBufferEventTimeSetting = async (newData: CreateOrUpdateBufferEventTimeSettingSchema) => {
	let record = await db.bufferEventTimeSettingModel.findOne({ where: { key: 'singleton' } });
	let { startTime, endTime } = newData;
	if (!startTime) {
		startTime = 0;
	}
	if (!endTime) {
		endTime = 0;
	}

	if (!record) {
		record = await db.bufferEventTimeSettingModel.create({ startTime, endTime });
	} else {
		await db.bufferEventTimeSettingModel.update({ startTime, endTime }, { where: { key: 'singleton' } });
	}

	return record;
};

export const getBufferEventTimeSetting = async () => db.bufferEventTimeSettingModel.findOne({});

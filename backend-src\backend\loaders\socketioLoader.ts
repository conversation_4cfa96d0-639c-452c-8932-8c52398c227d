import socketio = require('socket.io');
import { Server as HttpServer } from 'http';
import { Server as SocketServer } from 'socket.io';
import { systemConfig } from '../config';

const iooptions = {
	serveClient: systemConfig.ENV_TEST ? true : false,
	path: '/socket.io',
	cors: {
		origin: (process.env.CORS_LIST
			? [...new Set([...process.env.CORS_LIST.split(','), systemConfig.SITE_URI])]
			: systemConfig.NODE_ENV === 'production'
			? [systemConfig.SITE_URI, 'https://status-check.testweb-demo.com']
			: [systemConfig.SITE_URI, 'http://localhost:3000', process.env.NGROK_URI]
		).filter((origin): origin is string => typeof origin === 'string' && !!origin),
	},
};

// let socketIO: any;
const socketIO: SocketServer = new socketio.Server(iooptions);
function attachSocketServer(httpServer: HttpServer) {
	socketIO.attach(httpServer);
}

export { socketIO, attachSocketServer };

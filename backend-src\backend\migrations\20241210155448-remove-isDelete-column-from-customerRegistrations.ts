import { DATABASE_TABLE_NAME } from '~config';
import { SequelizeUtility } from '../utilities';

import type { Migration } from 'sequelize-cli';

module.exports = {
	async up(queryInterface) {
		if (await SequelizeUtility.columnExists(queryInterface, DATABASE_TABLE_NAME.CUSTOMER_REGISTRATIONS, 'isDelete')) {
			await queryInterface.removeColumn(DATABASE_TABLE_NAME.CUSTOMER_REGISTRATIONS, 'isDelete');
		}
	},

	async down(queryInterface, Sequelize) {
		if (
			await SequelizeUtility.columnNotExists(queryInterface, DATABASE_TABLE_NAME.CUSTOMER_REGISTRATIONS, 'isDelete')
		) {
			await queryInterface.addColumn(DATABASE_TABLE_NAME.CUSTOMER_REGISTRATIONS, 'isDelete', {
				type: Sequelize.BOOLEAN,
				allowNull: true,
				defaultValue: false,
			});
		}
	},
} satisfies Migration;

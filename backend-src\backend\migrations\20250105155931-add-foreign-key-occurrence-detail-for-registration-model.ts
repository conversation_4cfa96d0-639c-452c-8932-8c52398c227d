import { DATABASE_TABLE_NAME } from '../config';
import { SequelizeUtility } from '../utilities';

import type { Migration } from 'sequelize-cli';

module.exports = {
	async up(queryInterface, Sequelize) {
		const NEW_COLUMNS = ['occurrenceDetailId'];
		for (const newColumn of NEW_COLUMNS) {
			if (await SequelizeUtility.columnNotExists(queryInterface, DATABASE_TABLE_NAME.REGISTRATIONS, newColumn)) {
				await queryInterface.addColumn(DATABASE_TABLE_NAME.REGISTRATIONS, newColumn, {
					type: Sequelize.INTEGER({ unsigned: true }),
					references: {
						model: DATABASE_TABLE_NAME.OCCURRENCE_DETAIL,
						key: 'occurrenceDetailId',
					},
					onDelete: 'CASCADE',
				});
			}
		}
	},

	async down(queryInterface) {
		const NEW_COLUMNS = ['occurrenceDetailId'];
		for (const newColumn of NEW_COLUMNS) {
			if (await SequelizeUtility.columnExists(queryInterface, DATABASE_TABLE_NAME.REGISTRATIONS, newColumn)) {
				await queryInterface.removeColumn(DATABASE_TABLE_NAME.REGISTRATIONS, newColumn);
			}
		}
	},
} satisfies Migration;

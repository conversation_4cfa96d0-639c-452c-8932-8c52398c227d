import { Router } from 'express';
import {
	Manager<PERSON><PERSON>roller,
	OccasionMemberController,
	RegistrationController,
	OccasionController,
	CustomerRegistrationController,
	MemberController,
	MaintenanceModeController,
} from '../controllers';

const router = Router();

router.post('/manager/create', ManagerController.createNewManager);
router.put('/manager/:username', ManagerController.updateManager);
router.delete('/manager/:username', ManagerController.deleteManager);
router.post('/change-interview-to-waiting-result', RegistrationController.cronJobChangeInterviewToWaitingResult);
router.get('/remove-duplicates-occasion-member', OccasionMemberController.removeDuplicateDataOccasionMember);
router.post('/clean-data-event-time', OccasionController.cleanEventTimeManyOccurrenceOccasion);
router.post('/update-name-customer-registration', CustomerRegistrationController.updateNameCustomerRegistration);
router.post('/scan-data-and-fix-error-registration', OccasionController.scanDataOccasionMemberErrorController);
router.post(
	'/scan-and-fix-remove-data-event-time-error',
	OccasionController.scanDataAndRemoveDataEventTimeErrorController,
);
router.post('/mg-update-registration-by-id', OccasionController.updateRegistrationController);
router.post('/mg-restore-occurrence-detail-by-id', OccasionController.restoreOccurrenceDetailController);
router.post('/update-image-data-by-member-id', MemberController.memberMigrationDataImageProfileController);

// Maintenance mode routes
router.get('/maintenance/status', MaintenanceModeController.getMaintenanceStatus);
router.post('/maintenance/on', MaintenanceModeController.enableMaintenanceMode);
router.post('/maintenance/off', MaintenanceModeController.disableMaintenanceMode);
router.post(
	'/interview/change-to-waiting-interview',
	OccasionMemberController.updateManuallyMemberToWaitingInterviewStatus,
);

export { router };

import { z } from 'zod';
import {
	INTERVIEW_STEP,
	INTERVIEW_TYPE,
	MESSAGE_ZOD_VALIDATE_DATE,
	MESSAGE_ZOD_VALIDATE_TIME,
	REGEX_DATE_VALIDATE,
	REGEX_TIME_VALIDATE,
} from '~config';
import { memberLineSchema } from '~schemas/member';

export const interviewStepSchema = z.enum([
	INTERVIEW_STEP.FIRST_INTERVIEW,
	INTERVIEW_STEP.SECOND_INTERVIEW,
	INTERVIEW_STEP.THIRD_INTERVIEW,
	INTERVIEW_STEP.FOURTH_INTERVIEW,
]);

export const occurrenceDetails = z.object({
	interviewType: z.enum([INTERVIEW_TYPE.ONLINE, INTERVIEW_TYPE.OFFLINE]),
	eventDay: z.string().regex(REGEX_DATE_VALIDATE, MESSAGE_ZOD_VALIDATE_DATE),
	startTime: z.string().regex(REGEX_TIME_VALIDATE, MESSAGE_ZOD_VALIDATE_TIME),
	endTime: z.string().regex(REGEX_TIME_VALIDATE, MESSAGE_ZOD_VALIDATE_TIME),
	interviewLocation: z.string().optional().nullable(),
	occurrenceDetailId: z.number().optional(),
	maxAttendee: z.number().optional(),
	totalAttendee: z.number().optional(),
	notes: z.string().optional().nullable(),
});

export const getOccurrenceDetailSchema = z.object({
	occurrenceDetailId: z.string().min(1),
});

export const checkExistsSameTimeSchema = z.object({
	occasionId: z.string().min(1),
	occurrenceDetails: z.array(occurrenceDetails),
	interviewStep: interviewStepSchema,
});

export const updateOccurrenceDetailSchema = z.object({
	occurrenceDetail: occurrenceDetails,
});

export const getAllOccurrenceDetailsByOccurrenceId = z.object({
	occurrenceId: z.string().min(1),
	memberLine: memberLineSchema,
});

export const removeOccurrenceDetailSchema = z.object({
	occurrenceDetailId: z.string().min(1),
});

export const createOccurrenceDetailSchema = z.object({
	interviewType: z.enum([INTERVIEW_TYPE.ONLINE, INTERVIEW_TYPE.OFFLINE]),
	eventDay: z.string().regex(REGEX_DATE_VALIDATE, MESSAGE_ZOD_VALIDATE_DATE),
	startTime: z.string().regex(REGEX_TIME_VALIDATE, MESSAGE_ZOD_VALIDATE_TIME),
	endTime: z.string().regex(REGEX_TIME_VALIDATE, MESSAGE_ZOD_VALIDATE_TIME),
	interviewLocation: z.string().optional().default(''),
	maxAttendee: z.number(),
	notes: z.string().optional().nullable(),
	occurrenceId: z.number().optional(),
});

export type GetOccurrenceDetailSchema = z.infer<typeof getOccurrenceDetailSchema>;
export type CheckExistsSameTimeSchema = z.infer<typeof checkExistsSameTimeSchema>;
export type UpdateOccurrenceDetailSchema = z.infer<typeof updateOccurrenceDetailSchema>;
export type GetAllOccurrenceDetailsByOccurrenceId = z.infer<typeof getAllOccurrenceDetailsByOccurrenceId>;
export type RemoveOccurrenceDetailSchema = z.infer<typeof removeOccurrenceDetailSchema>;
export type CreateOccurrenceDetailSchema = z.infer<typeof createOccurrenceDetailSchema>;
export type InterviewStepSchema = z.infer<typeof interviewStepSchema>;

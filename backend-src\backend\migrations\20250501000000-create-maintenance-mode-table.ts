import { QueryInterface, DataTypes } from 'sequelize';
import { tableExists, tableNotExists } from '~utilities';
import { DATABASE_TABLE_NAME } from '~config';

module.exports = {
	up: async (queryInterface: QueryInterface) => {
		if (await tableNotExists(queryInterface, DATABASE_TABLE_NAME.MAINTENANCE_MODE)) {
			await queryInterface.createTable(DATABASE_TABLE_NAME.MAINTENANCE_MODE, {
				id: {
					type: DataTypes.INTEGER,
					primaryKey: true,
					autoIncrement: true,
				},
				isEnabled: {
					type: DataTypes.BOOLEAN,
					allowNull: false,
					defaultValue: false,
				},
				imageFilename: {
					type: DataTypes.STRING(500),
					allowNull: true,
					defaultValue: null,
				},
				createdAt: {
					type: DataTypes.DATE,
					allowNull: false,
				},
				updatedAt: {
					type: DataTypes.DATE,
					allowNull: false,
				},
			});

			await queryInterface.bulkInsert(DATABASE_TABLE_NAME.MAINTENANCE_MODE, [
				{
					isEnabled: false,
					imageFilename: null,
					createdAt: new Date(),
					updatedAt: new Date(),
				},
			]);
		}
	},

	down: async (queryInterface: QueryInterface) => {
		if (await tableExists(queryInterface, DATABASE_TABLE_NAME.MAINTENANCE_MODE)) {
			await queryInterface.dropTable(DATABASE_TABLE_NAME.MAINTENANCE_MODE);
		}
	},
};

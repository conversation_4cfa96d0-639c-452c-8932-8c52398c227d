import { CreationAttributes, Op, Transaction, WhereAttributeHash } from 'sequelize';
import { db } from '../models';
import { Spectator } from '../models/spectatorModel';
import { ADMIN_KEY_SEND_NOTIFICATION } from '~config';
import { get } from 'lodash';
import { TEMPLATE_REPLACERS } from '~utilities/common.utils';
import { LineService, SettingService } from '../services';

export const listSpectatorsByWatch = async (
	watch: (typeof ADMIN_KEY_SEND_NOTIFICATION)[keyof typeof ADMIN_KEY_SEND_NOTIFICATION],
	transaction?: Transaction,
) => {
	let spectatorWhere: WhereAttributeHash | null = null;
	switch (watch) {
		case 'campaign':
			spectatorWhere = { isSpectatingCampaign: true };
			break;
		case 'member':
			spectatorWhere = { isSpectatingMember: true };
			break;
		case 'registration':
			spectatorWhere = { isSpectatingRegistration: true };
			break;
		case ADMIN_KEY_SEND_NOTIFICATION.JOINED_OFFICE:
			spectatorWhere = { isMemberJoinedOffice: true };
			break;
		case ADMIN_KEY_SEND_NOTIFICATION.KEEP_OFFICE:
			spectatorWhere = { isMemberKeepOffice: true };
			break;
		default:
			break;
	}
	if (spectatorWhere != null) {
		return await db.spectators.findAll({
			where: spectatorWhere,
			attributes: ['memberId'],
			include: {
				association: db.spectators.associations.Member,
				attributes: ['lineId'],
			},
			transaction,
		});
	} else {
		return [];
	}
};

export const listSpectatorCandidates = async (transaction?: Transaction) => {
	const existingSpectators = await db.spectators.findAll({
		attributes: ['spectatorId', 'memberId'],
		transaction,
	});
	let spectators = [];
	if (existingSpectators.length == 0) {
		spectators = await db.members.findAll({
			where: { lineId: { [Op.not]: null }, isFriends: true },
			attributes: ['memberId', 'displayName', 'picUrl'],
			transaction,
		});
	} else {
		spectators = await db.members.findAll({
			where: {
				memberId: { [Op.notIn]: existingSpectators.map((s) => s.memberId) },
				lineId: { [Op.not]: null },
				isFriends: true,
			},
			attributes: ['memberId', 'displayName', 'picUrl'],
			transaction,
		});
	}
	return spectators;
};

export const listSpectators = async (transaction?: Transaction) =>
	db.spectators.findAll({
		attributes: { exclude: ['createdAt', 'updatedAt'] },
		include: {
			association: db.spectators.associations.Member,
			attributes: [
				'memberId',
				'memberCode',
				'displayName',
				'picUrl',
				'firstName',
				'lastName',
				'firstNameKana',
				'lastNameKana',
				'email',
				'telephone',
				'memberInfo',
			],
		},
		transaction,
	});

export const bulkEditSpectators = async (members: CreationAttributes<Spectator>[], transaction?: Transaction) => {
	const membersDB = await db.members.findAll({
		attributes: ['memberId'],
		where: {
			memberId: { [Op.in]: members.map((m) => m.memberId as number) },
			isFriends: true,
		},
		transaction,
	});
	if (membersDB.length > 0) {
		members = members.filter((m) => membersDB.some((mDB) => m.memberId == mDB.memberId));
		return await db.spectators.bulkCreate(members, {
			fields: [
				'memberId',
				'isSpectatingMember',
				'isSpectatingCampaign',
				'isSpectatingRegistration',
				'isMemberKeepOffice',
				'isMemberJoinedOffice',
			],
			updateOnDuplicate: [
				'isSpectatingMember',
				'isSpectatingCampaign',
				'isSpectatingRegistration',
				'isMemberKeepOffice',
				'isMemberJoinedOffice',
			],
			transaction,
		});
	} else {
		return [];
	}
};

export const deleteSpectator = async (spectatorId: number, transaction?: Transaction) =>
	db.spectators.destroy({ where: { spectatorId }, transaction });

export const sendMessageToLineUserBySettings = async ({
	memberId,
	settingKey,
	occasionId,
}: {
	memberId: number;
	settingKey: (typeof ADMIN_KEY_SEND_NOTIFICATION)[keyof typeof ADMIN_KEY_SEND_NOTIFICATION];
	occasionId?: number;
}) => {
	const memberSpectators = await listSpectatorsByWatch(settingKey);
	if (memberSpectators.length > 0) {
		const member = await db.members.findOne({
			where: {
				memberId,
			},
		});
		if (!member) return true;
		const spectatorLineIds = memberSpectators.map((mS) => mS.Member.lineId as string);
		const watchMessageTemplate = await SettingService.getSpectatorNotificationTemplate(settingKey);
		if (watchMessageTemplate && watchMessageTemplate.valueString) {
			let watchMessage = watchMessageTemplate.valueString.replace(
				TEMPLATE_REPLACERS.REPLACE_NAME,
				`${get(member, 'fullName', '')}`,
			);
			watchMessage = watchMessage.replace(
				TEMPLATE_REPLACERS.REPLACE_TELEPHONE,
				`${get(member, 'dataValues.customerRegistrationId2', '')}`,
			);
			if (occasionId) {
				const currentOccasion = await db.occasions.findOne({
					where: {
						occasionId,
					},
					include: [
						{
							model: db.categories,
							attributes: ['title'],
						},
					],
				});
				if (currentOccasion) {
					watchMessage = watchMessage.replace(
						TEMPLATE_REPLACERS.REPLACE_OFFICE_NAME,
						`${get(currentOccasion.toJSON(), 'Category.title', '')}`,
					);
				}
			}
			await LineService.sendMulticastMessage(spectatorLineIds, watchMessage);
		}
	}
	return true;
};

import { DATABASE_TABLE_NAME, INTERVIEW_STEP } from '../config';
import { SequelizeUtility } from '../utilities';

import type { Migration } from 'sequelize-cli';

module.exports = {
	async up(queryInterface, Sequelize) {
		const NEW_COLUMNS = [
			{
				name: 'notes',
				type: { type: Sequelize.STRING(5000), allowNull: true },
			},
			{
				name: 'responseDeadlineDate',
				type: { type: Sequelize.DATEONLY, allowNull: true },
			},
			{
				name: 'interviewStep',
				type: {
					type: Sequelize.ENUM(...Object.values(INTERVIEW_STEP)),
					allowNull: false,
					defaultValue: INTERVIEW_STEP.FIRST_INTERVIEW,
				},
			},
		];
		for (const newColumn of NEW_COLUMNS) {
			if (await SequelizeUtility.columnNotExists(queryInterface, DATABASE_TABLE_NAME.OCCURRENCE, newColumn.name)) {
				await queryInterface.addColumn(DATABASE_TABLE_NAME.OCCURRENCE, newColumn.name, newColumn.type);
			}
		}
	},

	async down(queryInterface) {
		const NEW_COLUMNS = ['notes', 'interviewStep', 'responseDeadlineDate'];
		for (const newColumn of NEW_COLUMNS) {
			if (await SequelizeUtility.columnExists(queryInterface, DATABASE_TABLE_NAME.OCCURRENCE, newColumn)) {
				await queryInterface.removeColumn(DATABASE_TABLE_NAME.OCCURRENCE, newColumn);
			}
		}
	},
} satisfies Migration;

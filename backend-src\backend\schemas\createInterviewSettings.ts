import { z } from 'zod';
import { SETTING_TYPES } from '~config';
// export const dateSchema = z
// 	.string()
// 	.regex(/^\d{4}-\d{2}-\d{2}$/, {
// 		message: 'Date must be in format YYYY-MM-DD',
// 	})
// 	.transform((val) => new Date(val));

export const createInterviewSettingsSchema = z
	.object({
		startDate: z.coerce.date(),
		endDate: z.coerce.date(),
		settingType: z.nativeEnum(SETTING_TYPES),
		classCategoryIds: z.array(z.number()).min(1),
		interviewSettingTimes: z
			.array(
				z.object({
					date: z.coerce.date(),
					startTime: z.string().optional(),
					endTime: z.string().optional(),
					classCategoryId: z.number().optional(),
					isBlockedCategory: z.boolean().optional(),
				}),
			)
			.min(1),
	})
	.refine((data) => data.startDate < data.endDate, {
		message: 'startDate must be before endDate',
		path: ['startDate'],
	})
	.refine(
		(data) =>
			data.interviewSettingTimes.every((d) => {
				if (!d.startTime || !d.endTime) return true;
				const [h1, m1] = d.startTime.split(':').map(Number);
				const [h2, m2] = d.endTime.split(':').map(Number);
				return h1 * 60 + m1 < h2 * 60 + m2;
			}),
		{
			message: 'startTime must be before endTime',
			path: ['startTime'],
		},
	);

export type CreateInterviewSettingsSchema = z.infer<typeof createInterviewSettingsSchema>;

import { Sequelize, Model, DataTypes, InferAttributes, InferCreationAttributes, CreationOptional } from 'sequelize';
import { DATABASE_TABLE_NAME } from '~config';

export class InterviewSettings extends Model<
	InferAttributes<InterviewSettings>,
	InferCreationAttributes<InterviewSettings>
> {
	//ATTRIBUTES
	declare interviewSettingId: CreationOptional<number>;
	declare startDate: Date;
	declare endDate: Date;
	declare settingType: number;
	declare classCategoryIds: number[];

	// //TIMESTAMPS
	declare createdAt?: CreationOptional<Date>;
	declare updatedAt?: CreationOptional<Date>;

	static initClass = (sequelize: Sequelize) =>
		InterviewSettings.init(
			{
				interviewSettingId: {
					type: DataTypes.INTEGER,
					autoIncrement: true,
					primaryKey: true,
				},
				startDate: {
					type: DataTypes.DATEONLY,
					allowNull: false,
				},
				endDate: {
					type: DataTypes.DATEONLY,
					allowNull: false,
				},
				settingType: {
					type: DataTypes.INTEGER,
					allowNull: false,
				},
				classCategoryIds: {
					type: DataTypes.JSON,
					allowNull: false,
				},
			},
			{
				sequelize: sequelize,
				tableName: DATABASE_TABLE_NAME.INTERVIEW_SETTINGS,
				timestamps: true,
				charset: 'utf8mb4',
				collate: 'utf8mb4_general_ci',
				name: {
					singular: 'interviewSetting',
					plural: DATABASE_TABLE_NAME.INTERVIEW_SETTINGS,
				},
			},
		);
}

import { z } from 'zod';

export const getAllMemberSchema = z.object({
	occasionId: z.string().optional(),
	textSearch: z.string().optional(),
	isRegistered: z.boolean().optional().default(true),
});

export const memberLineSchema = z.object({
	userId: z.string(),
	displayName: z.string().optional(),
	pictureUrl: z.string().optional(),
	statusMessage: z.string().optional(),
});

export type GetAllMemberSchema = z.infer<typeof getAllMemberSchema>;
export type MemberLineSchema = z.infer<typeof memberLineSchema>;

export const getInterviewHistoriesByMemberSchema = z.object({
	memberId: z.number(),
});
export type GetInterviewHistoriesByMemberSchema = z.infer<typeof getInterviewHistoriesByMemberSchema>;

import { NextFunction, Request, Response } from 'express';
import {
	AUTH_LEVELS,
	BAD_REQUEST,
	CATEGORY_TYPE,
	CUSTOM_SHOW_MESSAGE,
	MESSAGE_SESSION_DOES_NOT_EXISTS,
	RESPONSE_SUCCESS,
	SESSION_ERROR,
	systemConfig,
} from '../config';
import { AppError, createHash } from '../utilities';
import { CategoryService, ManagerService, SessionService } from '../services';
import { Manager } from '../models/managerModel';
import { createManagerSchema, deleteManagerSchema, getManagerListSchema, updateManagerSchema } from '~schemas/manager';
import { db } from '~models';
import { isTrue } from '~utilities/common.utils';

export const checkDuplicateEmailContact = async (emailContact: string, managerId?: number) => {
	const errorsData = [] as number[];
	if (emailContact) {
		const seen = new Set();
		const arrEmailsContact = emailContact.split(';');
		for (const [index, emailContactStr] of arrEmailsContact.entries()) {
			if (errorsData.includes(index)) continue;
			if (seen.has(emailContactStr)) {
				errorsData.push(index);
				break;
			}
			seen.add(emailContactStr);
			const existsEmailContact = await ManagerService.existingManagerEmailContact(emailContactStr, managerId);
			if (existsEmailContact) {
				errorsData.push(index);
				break;
			}
		}
	}
	return errorsData;
};

export const checkAuthenticatedUser = async (req: Request, res: Response, next: NextFunction) => {
	try {
		if (isTrue(req.headers['skip-authen']) && isTrue(systemConfig.SKIP_AUTHEN)) {
			next();
			return;
		}
		if (req.session.user) {
			const manager = await ManagerService.getManager(req.session.user.id);
			if (manager == null) {
				throw new AppError(SESSION_ERROR, `manager ${req.session.user.id} not found`);
			}
			const category = await db.categories.findOne({
				where: {
					managerId: manager.managerId,
				},
			});
			return res.send({
				auth: manager.authLevel == AUTH_LEVELS.master ? 'master' : 'manager',
				username: manager.username,
				categoryId: category?.categoryId || '',
			});
		} else {
			return res.status(SESSION_ERROR).send(MESSAGE_SESSION_DOES_NOT_EXISTS);
		}
	} catch (e) {
		next(e);
	}
};

const removeSessionById = async (managerId: number) => {
	const allSession = await SessionService.getAllSession();
	let sessionId = null;
	allSession.forEach((session) => {
		const sessionData = JSON.parse(session.data);
		if (sessionData.user.id === managerId) {
			sessionId = session.sid;
		}
	});
	if (sessionId) {
		return SessionService.removeSessionById(sessionId);
	}
	return;
};

export const createNewManager = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const validatedBody = createManagerSchema.parse(req.body);
		const { username, email, password, authLevel, emailContact, ...rest } = validatedBody;
		const manager = await ManagerService.findManagerByUsername(username);

		if (manager) {
			if (manager?.recoveryMail === email.trim()) {
				throw new AppError(BAD_REQUEST, `manager ${manager.recoveryMail} already exists ${CUSTOM_SHOW_MESSAGE}`);
			}
			throw new AppError(BAD_REQUEST, `manager ${manager.username} already exists ${CUSTOM_SHOW_MESSAGE}`);
		}
		if (emailContact) {
			const errorsData = await checkDuplicateEmailContact(emailContact);
			if (errorsData.length > 0) {
				return res.status(RESPONSE_SUCCESS).send({ errorsData });
			}
		}
		if (manager == null) {
			const hashPassword = await createHash(password);
			const dataCreated = {
				username,
				pwhash: hashPassword,
				recoveryMail: email,
				authLevel,
				isActivated: true,
				emailContact,
				...rest,
			} as unknown as Manager;
			const newManager = await ManagerService.createNewManager(dataCreated);
			if (rest.officeName) {
				await CategoryService.createCategory({
					title: rest.officeName,
					type: CATEGORY_TYPE.QR,
					managerId: newManager.managerId,
					isProgram: true,
				});
			}
		}
		return res.status(RESPONSE_SUCCESS).send({ succeed: true });
	} catch (e) {
		next(e);
	}
};

export const updateManager = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const validatedData = updateManagerSchema.parse({ ...req.body, ...req.params });
		const { username, email, password, emailContact, managerId, ...rest } = validatedData;
		const dataUpdate = rest as unknown as Manager;
		const manager = await ManagerService.getManager(managerId);

		if (!manager) {
			throw new AppError(BAD_REQUEST, `${username} is username_not_found ${CUSTOM_SHOW_MESSAGE}`);
		}

		if (manager?.username !== username) {
			const existsManager = await ManagerService.findManagerByUsername(username);
			if (existsManager) {
				throw new AppError(BAD_REQUEST, `${existsManager.username} username_exists ${CUSTOM_SHOW_MESSAGE}`);
			}
			dataUpdate.username = username;
		}

		if (password) {
			dataUpdate.pwhash = await createHash(password);
		}

		if (email) {
			const managerByEmail = await ManagerService.findManagerByEmail(email);
			if (managerByEmail) {
				return res.status(BAD_REQUEST).send('email is exists.');
			}
			dataUpdate.recoveryMail = email;
		}

		if (emailContact) {
			const errorsData = await checkDuplicateEmailContact(emailContact, manager.managerId);
			if (errorsData.length > 0) {
				return res.status(RESPONSE_SUCCESS).send({ errorsData });
			}
			dataUpdate.emailContact = emailContact;
		}

		await ManagerService.updateManagerById(managerId, dataUpdate);
		if (dataUpdate.pwhash && manager) {
			await removeSessionById(manager.managerId);
		}
		if (rest.officeName && manager.username !== rest.officeName) {
			await CategoryService.updateCategoryByManagerId(manager.managerId, {
				title: rest.officeName,
			});
		}
		return res.status(RESPONSE_SUCCESS).send({ succeed: true });
	} catch (e) {
		next(e);
	}
};

export const deleteManager = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const validatedData = deleteManagerSchema.parse({ ...req.body, ...req.params });
		const { username } = validatedData;
		const manager = await ManagerService.findManagerByUsername(username);
		if (!manager) {
			return res.status(BAD_REQUEST).send('username is not found.');
		}
		await ManagerService.removeManagerByUserName(username);
		if (manager) {
			await removeSessionById(manager.managerId);
		}
		return res.status(RESPONSE_SUCCESS).send({ succeed: true });
	} catch (e) {
		next(e);
	}
};

export const getManagerList = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const validatedQuery = getManagerListSchema.parse(req.query);

		const memberClassList = await ManagerService.getManagerList(validatedQuery);

		return res.status(RESPONSE_SUCCESS).send(memberClassList);
	} catch (error) {
		next(error);
	}
};

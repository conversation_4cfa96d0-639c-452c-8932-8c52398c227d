import { CronExpression } from '../enums';
import { ItemDynamicField } from '../services/memberService';
import * as process from 'node:process';

export const AUTH_LEVELS: authTypes = {
	master: 10,
	manager: 5,
};

export const RICHMENU_HEIGHT = 810;
export const RICHMENU_WIDTH = 1200;
export const RICHMENU_AREA_BIG_HEIGHT = 810;
export const RICHMENU_AREA_BIG_WIDTH = 800;
export const RICHMENU_AREA_SMALL_HEIGHT = 405;
export const RICHMENU_AREA_SMALL_WIDTH = 400;

export enum STUDENT_TYPE {
	ENROLLED_STUDENT = 'enrolledStudent', // 在籍学生
	GENERAL_UNIVERSITY_STUDENT = 'generalUniversityStudent', // 一般大学生
}

export enum RICH_MENU_TYPE {
	DEFAULT = 'defaultRM',
	ENROLLED_STUDENT = 'enrolledStudentRM',
	GENERAL_UNIVERSITY_STUDENT = 'generalUniversityStudentRM',
}

export const RICH_MENU_TYPES = ['defaultRM', 'memberRM'];

export enum RICH_MENU_ACTION_TYPE {
	TEL = 'tel',
	URI = 'uri',
	MESSAGE = 'message',
	MEMBERSHIP = 'membership',
}

export const RICH_MENU_ACTION_TYPES = Object.values(RICH_MENU_ACTION_TYPE);

export const VALUE_KEY_MAP_BY_ACTION_TYPE = {
	[RICH_MENU_ACTION_TYPE.MESSAGE]: 'text',
	[RICH_MENU_ACTION_TYPE.URI]: 'uri',
	[RICH_MENU_ACTION_TYPE.MEMBERSHIP]: 'uri',
	[RICH_MENU_ACTION_TYPE.TEL]: 'uri',
};

export const RESPONSE_SUCCESS = 200;
export const CREATED = 201;
export const NO_CONTENT = 204;
export const BAD_REQUEST = 400;
export const PERMISSION_ERROR = 401; //not enough authorization
export const SESSION_ERROR = 403; //no-session
export const NOT_ACCEPTABLE = 406; //not acceptable
export const CONFLICT_ERROR = 409;
export const SYSTEM_ERROR = 500;

export const MEMBER_WATCH_MESSAGE = '新規登録の通知です！\n会員：[NAME]';
export const REGISTRATION_WATCH_MESSAGE = '新規予約の通知です！\n会員：[NAME]\n予約日付：[DATE]';
export const CAMPAIGN_WATCH_MESSAGE = '新規キャンペーン応募の通知です！\n会員：[NAME]';

export const WATCH_MESSAGE_KEY_MEMBER = 'watchMemberTemplate';
export const WATCH_MESSAGE_KEY_REGISTRATION = 'watchRegistrationTemplate';
export const WATCH_MESSAGE_KEY_REGISTRATION_CANCEL = 'watchRegistrationCancelTemplate';
export const WATCH_MESSAGE_KEY_CAMPAIGN_APPLY = 'watchCampaignApplyTemplate';
export const CUSTOM_SHOW_MESSAGE = 'custom_show_message';

export const TZ_DATABASE = '+09:00';
export const DATE_FORMAT = 'YYYY-MM-DD';
export const TIME_FORMAT = 'HH:mm:ss';
export const DATE_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';
export const DATE_TIME_FORMAT_NO_SEC = 'YYYY-MM-DD HH:mm';

export const DATE_FORMAT_JP = 'YYYY年MM月DD日';
export const TIME_FORMAT_JP = 'HH時mm分ss秒';
export const DATE_TIME_FORMAT_JP_NO_SEC = 'YYYY年MM月DD日HH時mm分';

export const INQUIRY_TYPES: inquiryType[] = ['service', 'achievement', 'company', 'recruit', 'others'];

export enum CUSTOMER_REGISTRATION_FIELD_TYPE {
	TEXT = 'text',
	NUMBER = 'number',
	CHECKBOX = 'checkbox',
	RADIO = 'radio',
	DATE_PICKER = 'datepicker',
	IMAGE = 'image',
	ADDRESS = 'address',
	NOTE = 'note',
}

export enum PAYMENT_TYPE {
	REFUND = 'refund',
	PURCHASE = 'purchase',
}

export enum PAYMENT_STATUS {
	PENDING = 'PENDING',
	REJECTED = 'REJECTED',
	FULFILLED = 'FULFILLED',
}

export const ERROR_MESSAGES = {
	LINE_NOT_VERIFIED: 'LINE_NOT_VERIFIED',
};

export enum DATABASE_TABLE_NAME {
	RICH_MENU = 'RichMenus',
	RICH_MENU_AREA_ACTIONS = 'RichMenuAreaActions',
	RICH_MENU_AREA_BOUNDS = 'RichMenuAreaBounds',
	SYSTEM_SETTINGS = 'systemSettings',
	SURVEYS = 'Surveys',
	CUSTOMER_REGISTRATIONS = 'customerRegistrations',
	MEMBERS = 'members',
	MEMBER_SURVEY_REWARD = 'memberSurveyRewards',
	AUDIENCES = 'audiences',
	SURVEY_TEMPLATE = 'SurveyTemplates',
	MEMBER_FRIEND_ADDED = 'MemberFriendAdded',
	MEMBER_CLASS = 'MemberClasses',
	CLASS_CATEGORY = 'ClassCategories',
	MANAGER = 'managers',
	OCCASION = 'occasions',
	OCCURRENCE = 'occurrences',
	OCCURRENCE_DETAIL = 'occurrenceDetails',
	OCCASION_MEMBER = 'occasionMembers',
	REGISTRATIONS = 'registrations',
	REMINDERS = 'reminders',
	BUFFER_EVENT_TIME_SETTINGS = 'bufferEventTimeSettings',
	SPECTATORS = 'spectators',
	NOTIFICATION_SETTING = 'notificationSettings',
	MAINTENANCE_MODE = 'MaintenanceModes',
	AUDIENCE = 'audiences',
	INTERVIEW_SETTINGS = 'interviewSettings',
	INTERVIEW_SETTING_TIMES = 'interviewSettingTimes',
}

export enum SYSTEM_SETTING_KEYS {
	IS_EVENT_BOOKING_TAB_VISIBLE = 'isEventBookingTabVisible',
	STORE_PIC = 'storePic',
}

export const TIME_ZONE_DEFAULT = 'Asia/Tokyo';
export const CRON_JOB_SEND_REMINDER = process.env.CRON_JOB_SEND_REMINDER || CronExpression.EVERY_DAY_AT_NOON;
export const CRON_JOB_SEND_REMINDER_OFFICE =
	process.env.CRON_JOB_SEND_REMINDER_OFFICE || CronExpression.EVERY_DAY_AT_MIDNIGHT;
export const CRON_JOB_CHANGE_INTERVIEW_STATUS =
	process.env.CRON_JOB_CHANGE_INTERVIEW_STATUS || CronExpression.EVERY_MINUTE;
export const CRON_JOB_DECLINED_INTERVIEW =
	process.env.CRON_JOB_DECLINED_INTERVIEW || CronExpression.EVERY_DAY_AT_MIDNIGHT;

export enum MEMBER_ORIGIN {
	NEW = 'new',
	CSV = 'csv',
	SYSTEM = 'system',
}

export enum MEMBER_VIA_TYPE {
	WEBSITE = 'website',
	EXHIBITION = 'exhibition',
	INTRODUCTION = 'introduction',
	OTHERS = 'others',
}

export enum MEMBER_IS_FRIEND_LABEL {
	IS_FRIEND = 'はい',
	NOT_FRIEND = 'いいえ',
}

export enum CUSTOMER_REGISTRATION_FIELD_IMAGE_LABEL {
	EXIST = 'あり',
	NOT_EXIST = 'なし',
}

export const SURVEY_IMAGE_FILE_NAME = 'surveyImage';

export const DAYS_REMINDER = {
	one_day: process.env.ONE_DAY_TEST ? parseInt(process.env.ONE_DAY_TEST) + 1 : 1,
	seven_day: process.env.SEVEN_DAY_TEST ? parseInt(process.env.SEVEN_DAY_TEST) + 7 : 7,
};

export const MYSQL_DATE_FORMAT = 'YYYY-MM-DD HH:mm:ss';

export enum REMINDER_NOTIFY_TYPES {
	one_day = 'isNotified2',
	another_day = 'isNotified1',
}

export enum SORT {
	ASCENDING = 'asc',
	DESCENDING = 'desc',
}

export enum SOCKET_EVENTS {
	AUDIENCE_CREATED = 'audience:created',

	MEMBER_CREATED = 'member:created',
	MEMBER_UPDATED = 'member:updated',
	MEMBER_DELETED = 'member:deleted',

	CHAT_SEEN = 'chat:seen',
	CHAT_CREATED = 'chat:created',

	CUSTOMER_REGISTRATIONS_CHANGED = 'customer_registrations:changed',

	OCCASION_UPDATED = 'occasion:updated',

	OCCURRENCE_CREATED = 'occurrence:created',

	REGISTRATION_CANCELLED = 'registration:cancelled',

	OCCASION_MEMBER_ADD_MEMBER = 'occasion_member:add_member',
	REGISTRATION_EVENT_CREATED = 'registration_event:created',
	OCCASION_MEMBER_CHANGE_STATUS = 'occasion_member:change_status',
	OCCASION_MEMBER_INTERVIEW_SCHEDULED_ADDED = 'occasion_member:interview_scheduled_added',
	OCCASION_MEMBER_UPDATE_MEMBER_ACTION_STATUS = 'occasion_member:update_member_action_status',

	OCCURRENCE_DETAILS_REMOVED = 'occurrence_details:removed',

	MAINTENANCE_MODE_CHANGED = 'maintenance:changed',
}

export enum CUSTOMER_REGISTRATIONS_DEFAULT_FIELD {
	LAST_NAME = 'lastName',
	FIRST_NAME = 'firstName',
	POSTAL_CODE = 'postalCode',
	ADDRESS = 'address',
	SUB_ADDRESS = 'subAddress',
	BUILDING = 'building',
	TELEPHONE = 'telephone',
}

export enum SURVEY_PAGE_TYPE {
	SINGLE_PAGE = 'single',
	MULTIPLE_PAGE = 'multiple',
}

export enum AUDIENCE_TYPE {
	DEFAULT = 'default',
	CAMPAIGN = 'campaign',
	SURVEY = 'survey',
	LOTTERY = 'lottery',
	MEMBER = 'member',
}

export enum AUDIENCE_STATUS {
	IN_PROGRESS = 'IN_PROGRESS',
	READY = 'READY',
	FAILED = 'FAILED',
	EXPIRED = 'EXPIRED',
	INACTIVE = 'INACTIVE',
	ACTIVATING = 'ACTIVATING',
}

export enum SURVEY_NAVIGATION_TYPE {
	NEXT_QUESTION = 'next_question',
	CONDITIONAL_BRANCHING = 'conditional_branching',
	NEXT_SPECIFIED_QUESTION = 'next_specified_question',
}

export const CAMPAIGN_CHOICES_TYPE_OTHER = 'other';

export const CUSTOMER_REGISTRATION_TYPES = {
	TEXT: 'text',
	DATE_PICKER: 'datepicker',
	RADIO: 'radio',
	CHECKBOX: 'checkbox',
	IMAGE: 'image',
};

export const CONTENT_TYPE_SURVEY_OTHER = 'その他（テキスト入力）';

export const SURVEY_IMAGE_QUESTION_FILE_NAME = 'questionImage';

export const SURVEY_TEMPLATE_TYPE = {
	TEXT: 'text',
	DATEPICKER: 'datepicker',
	RADIO: 'radio',
	CHECKBOX: 'checkbox',
	IMAGE: 'image',
};

export const OPTION_SUBMIT_SURVEY_VALUE = -1;

export enum AUDIENCE_PARTICIPATION_STATUS {
	PARTICIPATE = 'participate',
	RESERVE = 'reserve',
	CANCEL = 'cancel',
}

const replacerName = new RegExp(/\[NAME\]/, 'gm');
const replacerDateTime = new RegExp(/\[DATE\]/, 'gm');
const replacerTelephone = new RegExp(/\[TEL\]/, 'gm');
const replacerTelephoneCompany = new RegExp(/\[COMPANY-TEL\]/, 'gm');
const replacerConfirmationUrl = new RegExp(/\[CONFIRM-URL\]/, 'gm');
const replacerBuilding = new RegExp(/\[BUILDING\]/, 'gm');

export const EVENT_REGISTRATIONS_REPLACER_MESSAGE = {
	replacerName,
	replacerDateTime,
	replacerTelephone,
	replacerTelephoneCompany,
	replacerConfirmationUrl,
	replacerBuilding,
};

export const MEMBER_CLASS_CATEGORIES = {
	acting: 'acting',
	vocal: 'vocal',
	junior: 'junior',
	comedian: 'comedian',
};

export const CUSTOMER_REGISTRATION_NAME = {
	CLASS: 'class',
	ROLL_NUMBER: 'rollNumber',
	IMAGE_PROFILE: 'imageProfile',
	FULL_NAME_ENROLLED_STUDENT: 'fullNameEnrolledStudent',
	REPRESENTATIVE_NAME: 'representativeName',
	UNIVERSITY_STUDENT_EMAIL: 'universityStudentEmail',
	ENTRY_CITY: 'entryCity',
	UNIVERSITY_NAME: 'universityName',
	CLASS_UNIVERSITY: 'classUniversity',
	VOCAL_DEMO: 'vocalDemo',
	PARTICIPATION_YEAR: 'participationYear',
};

export const CLASS_CATEGORY_NAME = {
	COMEDIAN: '芸人',
};

export const MESSAGE_SESSION_DOES_NOT_EXISTS = 'SESSION_DOES_NOT_EXISTS';

export enum CATEGORY_TYPE {
	QR = 'qr',
	CHECKIN = 'checkin',
}

export enum SORT_ORDER_DATABASE {
	DESC = 'desc',
	ASC = 'asc',
}

export enum INTERVIEW_TYPE {
	ONLINE = 'online',
	OFFLINE = 'offline',
}

export enum INTERVIEW_STEP {
	FIRST_INTERVIEW = 'firstInterview',
	SECOND_INTERVIEW = 'secondInterview',
	THIRD_INTERVIEW = 'thirdInterview',
	FOURTH_INTERVIEW = 'fourthInterview',
}

export const REGEX_DATE_VALIDATE = /^\d{4}-\d{2}-\d{2}$/;
export const MESSAGE_ZOD_VALIDATE_DATE = 'Invalid date format. Expected YYYY-MM-DD';
export const REGEX_TIME_VALIDATE = /^([01]\d|2[0-3]):([0-5]\d)$/;
export const MESSAGE_ZOD_VALIDATE_TIME = 'Invalid time format. Expected HH:mm (24-hour format)';

export enum MEMBER_INTERVIEW_STATUS {
	INTERVIEW_PROPOSAL = 'interviewProposal',
	SCHEDULE_ADJUSTMENT = 'scheduleAdjustment',
	INTERVIEW_SCHEDULED = 'interviewScheduled',
	WAITING_RESULTS = 'waitingResults',
	INTERVIEW_PASSED = 'interviewPassed',
	ASSIGNED_ACCEPTED = 'assignedAccepted',
	ASSIGNED_CONDITIONALLY_ACCEPTED = 'assignedConditionallyAccepted',
	NOT_ACCEPTED = 'notAccepted',
	DECLINED = 'declined',
	AUTO_DECLINED = 'autoDeclined',
}

export enum MEMBER_ACTIONS_STATUS {
	MEMBER_KEEP_ACCEPT = 'memberKeepAccepted',
	MEMBER_ACCEPTED = 'memberAccepted',
	MEMBER_NOT_ACCEPTED = 'memberNotAccepted',
}

export const INTERVIEWS_STEP_LABELS = {
	[INTERVIEW_STEP.FIRST_INTERVIEW]: '一次面接',
	[INTERVIEW_STEP.SECOND_INTERVIEW]: '二次面接',
	[INTERVIEW_STEP.THIRD_INTERVIEW]: '三次面接',
	[INTERVIEW_STEP.FOURTH_INTERVIEW]: '四次面接',
};

export const LABEL_INTERVIEW_STATUS = {
	[MEMBER_INTERVIEW_STATUS.SCHEDULE_ADJUSTMENT]: '日程調整',
	[MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED]: '面接予定',
	[MEMBER_INTERVIEW_STATUS.WAITING_RESULTS]: '結果回答待ち',
	[MEMBER_INTERVIEW_STATUS.INTERVIEW_PASSED]: '面接通過',
	[MEMBER_INTERVIEW_STATUS.ASSIGNED_ACCEPTED]: '所属合格',
	[MEMBER_INTERVIEW_STATUS.ASSIGNED_CONDITIONALLY_ACCEPTED]: '所属条件付き合格',
	[MEMBER_INTERVIEW_STATUS.NOT_ACCEPTED]: '不合格',
	[MEMBER_INTERVIEW_STATUS.DECLINED]: '自主辞退',
	[MEMBER_INTERVIEW_STATUS.INTERVIEW_PROPOSAL]: '面接候補',
	[MEMBER_INTERVIEW_STATUS.AUTO_DECLINED]: '回答期限切れ辞退',
};

export const LABEL_INTERVIEW_STATUS_EXPORT_CSV = {
	[MEMBER_INTERVIEW_STATUS.SCHEDULE_ADJUSTMENT]: '日程調整',
	[MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED]: '面接予定',
	[MEMBER_INTERVIEW_STATUS.WAITING_RESULTS]: '結果回答待ち',
	[MEMBER_INTERVIEW_STATUS.INTERVIEW_PASSED]: '面接通過',
	[MEMBER_INTERVIEW_STATUS.ASSIGNED_ACCEPTED]: '所属合格',
	[MEMBER_INTERVIEW_STATUS.ASSIGNED_CONDITIONALLY_ACCEPTED]: '所属条件付き合格',
	[MEMBER_INTERVIEW_STATUS.NOT_ACCEPTED]: '不合格',
	[MEMBER_INTERVIEW_STATUS.DECLINED]: '自主辞退',
	[MEMBER_INTERVIEW_STATUS.INTERVIEW_PROPOSAL]: '面接候補',
	[MEMBER_INTERVIEW_STATUS.AUTO_DECLINED]: '回答期限切れ辞退',
};

export const ADMIN_LABEL_INTERVIEW_STATUS_STEP_1 = {
	[MEMBER_INTERVIEW_STATUS.SCHEDULE_ADJUSTMENT]: '学生の面接予約待ち',
	[MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED]: '面接予定',
	[MEMBER_INTERVIEW_STATUS.WAITING_RESULTS]: '面接結果を設定してください',
	[MEMBER_INTERVIEW_STATUS.INTERVIEW_PASSED]: '面接通過',
	[MEMBER_INTERVIEW_STATUS.ASSIGNED_ACCEPTED]: '所属合格',
	[MEMBER_INTERVIEW_STATUS.ASSIGNED_CONDITIONALLY_ACCEPTED]: '所属条件付き合格',
	[MEMBER_INTERVIEW_STATUS.NOT_ACCEPTED]: '不合格',
	[MEMBER_INTERVIEW_STATUS.DECLINED]: '自主辞退',
	[MEMBER_INTERVIEW_STATUS.INTERVIEW_PROPOSAL]: '面接候補',
	[MEMBER_INTERVIEW_STATUS.AUTO_DECLINED]: '回答期限切れ辞退',
};

export const LABEL_MEMBER_ACTIONS_STATUS = {
	[MEMBER_ACTIONS_STATUS.MEMBER_ACCEPTED]: '所属',
	[MEMBER_ACTIONS_STATUS.MEMBER_NOT_ACCEPTED]: '所属辞退',
	[MEMBER_ACTIONS_STATUS.MEMBER_KEEP_ACCEPT]: 'キープ',
};

export const ADMIN_KEY_SEND_NOTIFICATION = {
	CAMPAIGN: 'campaign',
	MEMBER: 'member',
	REGISTRATION: 'registration',
	JOINED_OFFICE: 'memberJoinedOffice',
	KEEP_OFFICE: 'memberKeepOffice',
};

export const SUBJECT_EMAIL_SENDER =
	process.env.SUBJECT_EMAIL_SENDER || '[BUILDING]の[面接ステップ]の日程調整が締切になりました。';

export const OCCASION_DISPLAY_LABEL = {
	1: '進行中',
	0: '完了',
};

export const INTERVIEW_TYPE_NAME = {
	offline: '対面',
	online: 'オンライン',
};

export enum NOTIFICATION_SETTING {
	notificationSetting = 'notificationSettings',
}

export enum PUBLIC_NOTIFICATION_SETTING {
	PUBLISH_AS_NEW_NOTICE = 'publishAsNewNotice',
	UPDATE_CURRENT_NOTICE = 'updateCurrentNotice',
	MAKE_PRIVATE = 'makePrivate',
}

export const cancelHistoryColumn = 'キャンセル履歴';

export const fieldKeysCsv: Record<string, ItemDynamicField> = {
	memberId: { label: '会員ID', value: 'memberId' },
	memberCode: { label: '会員コード', value: 'memberCode' },
	lineName: { label: 'LINE名', value: 'lineName' },
	fullNameEnrolledStudent: { label: '氏名', value: 'fullNameEnrolledStudent' },
	rollNumber: { label: '出場者番号', value: 'rollNumber' },
	participationYear: { label: '出場年度', value: 'participationYear' },
	category: { label: 'カテゴリー', value: 'category' },
	class: { label: 'クラス', value: 'class' },
	interviewRequestCount: { label: '面接依頼事務所数', value: 'interviewRequestCount' },
	minInterviewCount: { label: '最低面接社数', value: 'minInterviewCount' },
	minInterviewDiff: { label: '最低面接社数との差異', value: 'minInterviewDiff' },
	firstInterviewSelfCancel: { label: '一次面接自主辞退数', value: 'firstInterviewSelfCancel' },
	firstInterviewExpired: { label: '一次面接予約期限切れ辞退', value: 'firstInterviewExpired' },
	imageProfile: { label: 'プロフィール写真', value: 'imageProfile' },
	vocalDemo: { label: '履歴書・デモ音源（Voのみ）', value: 'vocalDemo' },
	note: { label: '備考欄', value: 'note' },
	friendRegisterDate: { label: '友だち登録日', value: 'friendRegisterDate' },
	memberRegisterDate: { label: '会員登録日', value: 'memberRegisterDate' },
};

export type FieldKeyCsvType = keyof typeof fieldKeysCsv;

export const maxTimeInterviewOverlap = 3;

export const ADMIN_LABEL_INTERVIEW_CANDIDATE_ACCEPTED_OTHER_OFFICE = '面接候補（他所属決定済）';

export const HEADER_EXPORT_CSV_REQUIRED_UPLOAD_AUDIENCE = '会員コード';

const DEFAULT_MAX_SIZE_MB = 5;
const sizeInMB = parseInt(process.env.MAX_SIZE_UPLOAD_CSV_AUDIENCES || '', 10);
export const MAX_FILE_SIZE_UPLOAD_AUDIENCE = (Number.isNaN(sizeInMB) ? DEFAULT_MAX_SIZE_MB : sizeInMB) * 1024 * 1024;

export const OFFICE_LABEL_CSV_DECLINED_OR_AUTO_DECLINED = '辞退';

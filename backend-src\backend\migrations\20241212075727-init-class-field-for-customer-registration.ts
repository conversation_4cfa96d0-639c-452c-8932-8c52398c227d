import { Migration } from 'sequelize-cli';
import {
	CUSTOMER_REGISTRATION_FIELD_TYPE,
	CUSTOMER_REGISTRATION_NAME,
	DATABASE_TABLE_NAME,
	STUDENT_TYPE,
} from '~config';
import { get } from 'lodash';

module.exports = {
	async up(queryInterface, Sequelize) {
		const transaction = await queryInterface.sequelize.transaction();

		try {
			await queryInterface.bulkUpdate(
				DATABASE_TABLE_NAME.CUSTOMER_REGISTRATIONS,
				{
					showOrder: Sequelize.literal('showOrder + 1'),
				},
				{
					showOrder: { [Sequelize.Op.gte]: 5 },
				},
				{ transaction },
			);

			const registrationCreatedId = await queryInterface.bulkInsert(
				DATABASE_TABLE_NAME.CUSTOMER_REGISTRATIONS,
				[
					{
						required: true,
						isDisplayed: true,
						label: 'クラス',
						type: CUSTOMER_REGISTRATION_FIELD_TYPE.NUMBER,
						name: CUSTOMER_REGISTRATION_NAME.CLASS,
						showOrder: 5,
						studentType: STUDENT_TYPE.ENROLLED_STUDENT,
					},
				],
				{
					transaction,
				},
			);

			await queryInterface.addColumn(
				DATABASE_TABLE_NAME.MEMBERS,
				`customerRegistrationId${registrationCreatedId}`,
				{
					type: Sequelize.DataTypes.STRING,
					defaultValue: null,
					allowNull: true,
				},
				{
					transaction,
				},
			);

			await transaction.commit();
		} catch (error) {
			console.log('🚀 -------------------------------------------------------------------------------------🚀');
			console.log('🚀 ~ file: 20241212075727-init-class-field-for-customer-registration ~ up ~ error:', error);
			console.log('🚀 -------------------------------------------------------------------------------------🚀');
			await transaction.rollback();
		}
	},

	async down(queryInterface, Sequelize) {
		const transaction = await queryInterface.sequelize.transaction();

		try {
			const [[field]] = await queryInterface.sequelize.query(
				`SELECT * FROM ${DATABASE_TABLE_NAME.CUSTOMER_REGISTRATIONS} WHERE label = :label AND showOrder = 5`,
				{
					replacements: { table: DATABASE_TABLE_NAME.CUSTOMER_REGISTRATIONS, label: 'クラス' },
				},
			);

			await queryInterface.bulkDelete(
				DATABASE_TABLE_NAME.CUSTOMER_REGISTRATIONS,
				{
					label: 'クラス',
					showOrder: 5,
				},
				{
					transaction,
				},
			);

			await queryInterface.bulkUpdate(
				DATABASE_TABLE_NAME.CUSTOMER_REGISTRATIONS,
				{
					showOrder: Sequelize.literal('showOrder - 1'),
				},
				{
					showOrder: { [Sequelize.Op.gte]: 5 },
				},
				{ transaction },
			);

			if (get(field, 'customerRegistrationId')) {
				await queryInterface.removeColumn(
					DATABASE_TABLE_NAME.MEMBERS,
					`customerRegistrationId${get(field, 'customerRegistrationId')}`,
				);
			}

			await transaction.commit();
		} catch (error) {
			console.log('🚀 ---------------------------------------------------------------------------------------🚀');
			console.log('🚀 ~ file: 20241212075727-init-class-field-for-customer-registration ~ down ~ error:', error);
			console.log('🚀 ---------------------------------------------------------------------------------------🚀');
			await transaction.rollback();
		}
	},
} satisfies Migration;

import { NextFunction, Request, Response } from 'express';
import { CUSTOM_SHOW_MESSAGE, PERMISSION_ERROR, SYSTEM_ERROR } from '~config';
import { ManagerService } from '~services';
import { AppError, comparePassword } from '~utilities';

export const checkPassMiddleware = async (req: Request, res: Response, next: NextFunction) => {
	try {
		const managerId = req.session.user?.id;

		if (!managerId) {
			throw new AppError(PERMISSION_ERROR, `no session ${CUSTOM_SHOW_MESSAGE}`, false);
		}

		const { password } = req.body;

		if (!password) {
			throw new AppError(PERMISSION_ERROR, `invalid parameters ${CUSTOM_SHOW_MESSAGE}`, false);
		}

		const manager = await ManagerService.getManager(managerId);
		if (manager == null) {
			throw new AppError(PERMISSION_ERROR, `manager ${managerId} does not exist  ${CUSTOM_SHOW_MESSAGE}`, false);
		}

		const isMatch = await comparePassword(password, manager.pwhash);

		if (!isMatch) {
			throw new AppError(PERMISSION_ERROR, `invalid password  ${CUSTOM_SHOW_MESSAGE}`, false);
		}
		next();
	} catch (e) {
		next(e);
	}
};

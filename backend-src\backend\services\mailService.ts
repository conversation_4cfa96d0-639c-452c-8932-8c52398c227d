import nodemailer from 'nodemailer';

import { mailConfig } from '~config';
import { writeLog } from '~utilities';

const transporter = nodemailer.createTransport({
	host: mailConfig.host,
	port: mailConfig.port,
	secure: mailConfig.secure,
	auth: mailConfig.auth,
	pool: true,
});

export const sendMail = async (to: string | string[], subject: string, html: string) => {
	try {
		await transporter.sendMail({
			from: mailConfig.from,
			to,
			subject,
			html,
		});
	} catch (e) {
		writeLog(
			{
				msg: 'sendMail ~ error:',
				e,
				to,
				subject,
			},
			'error',
		);
	}
};

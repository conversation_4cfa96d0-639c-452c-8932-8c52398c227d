import { Migration } from 'sequelize-cli';
import { DATABASE_TABLE_NAME, MEMBER_INTERVIEW_STATUS } from '~config';

module.exports = {
	async up(queryInterface, Sequelize) {
		const enumValues = Object.values(MEMBER_INTERVIEW_STATUS);

		await queryInterface.changeColumn(DATABASE_TABLE_NAME.OCCASION_MEMBER, 'interviewStatus', {
			type: Sequelize.ENUM(...enumValues),
			allowNull: false,
			defaultValue: MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED,
		});

		await queryInterface.changeColumn(DATABASE_TABLE_NAME.REGISTRATIONS, 'interviewStatus', {
			type: Sequelize.ENUM(...enumValues),
			allowNull: false,
			defaultValue: MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED,
		});
	},

	async down(queryInterface, Sequelize) {
		const enumValuesWithoutAutoDeclined = Object.values(MEMBER_INTERVIEW_STATUS).filter(
			(status) => status !== MEMBER_INTERVIEW_STATUS.AUTO_DECLINED,
		);

		await queryInterface.changeColumn(DATABASE_TABLE_NAME.OCCASION_MEMBER, 'interviewStatus', {
			type: Sequelize.ENUM(...enumValuesWithoutAutoDeclined),
			allowNull: false,
			defaultValue: MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED,
		});

		await queryInterface.changeColumn(DATABASE_TABLE_NAME.REGISTRATIONS, 'interviewStatus', {
			type: Sequelize.ENUM(...enumValuesWithoutAutoDeclined),
			allowNull: false,
			defaultValue: MEMBER_INTERVIEW_STATUS.INTERVIEW_SCHEDULED,
		});
	},
} satisfies Migration;

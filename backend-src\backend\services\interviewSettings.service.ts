import { Transaction } from 'sequelize';
import { db } from '~models';

export const createInterviewSettings = async ({
	startDate,
	endDate,
	settingType,
	classCategoryIds,
	transaction,
}: {
	startDate: Date;
	endDate: Date;
	settingType: number;
	classCategoryIds: number[];
	transaction?: Transaction;
}) => {
	return db.interviewSettings.create(
		{
			startDate,
			endDate,
			settingType,
			classCategoryIds,
		},
		{ transaction },
	);
};

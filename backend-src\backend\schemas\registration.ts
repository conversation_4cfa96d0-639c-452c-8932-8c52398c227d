import { z } from 'zod';
import { memberLineSchema } from '~schemas/member';
import { INTERVIEW_STEP, MEMBER_ACTIONS_STATUS } from '~config';
import { interviewStatusSchema } from '~schemas/occasionMember';

export const memberActionStatusSchema = z.enum([
	MEMBER_ACTIONS_STATUS.MEMBER_ACCEPTED,
	MEMBER_ACTIONS_STATUS.MEMBER_KEEP_ACCEPT,
	MEMBER_ACTIONS_STATUS.MEMBER_NOT_ACCEPTED,
]);

export const memberRegistrationForEventSchema = z.object({
	occurrenceDetailId: z.number().min(1),
	memberLine: memberLineSchema,
	timezone: z.string(),
});

export const memberGetRegistrationSchema = z.object({
	occurrenceId: z.string().min(1),
	memberLine: memberLineSchema,
});

export const memberCancelledRegistrationSchema = z.object({
	registrationId: z.string().min(1),
	memberLine: memberLineSchema,
});

export const adminRegistrationByInterviewStepSchema = z.object({
	interviewSteps: z
		.array(
			z.enum([
				INTERVIEW_STEP.FIRST_INTERVIEW,
				INTERVIEW_STEP.SECOND_INTERVIEW,
				INTERVIEW_STEP.THIRD_INTERVIEW,
				INTERVIEW_STEP.FOURTH_INTERVIEW,
			]),
		)
		.min(1),
	page: z.string().optional().default('1'),
	pageSize: z.string().optional().default('4'),
	occasionId: z.string().min(1),
});

export const memberGetRegistrationByInterviewStatusesSchema = z.object({
	interviewStatuses: z.array(interviewStatusSchema).min(1),
	memberLine: memberLineSchema,
	page: z.string().optional().default('1'),
	pageSize: z.string().optional().default('4'),
	attended: z.string().optional(),
});

export const adminUpdateInterviewStatusSchema = z.object({
	interviewStatus: interviewStatusSchema,
	memberIds: z.array(z.number()).min(1),
	responseDeadlineDatePassInterview: z.string().optional().nullable().default(null),
	occasionId: z.number().min(1),
});

export const getRegistrationByIdSchema = z.object({
	registrationId: z.string().min(1),
});

export const confirmInterviewNextStepSchema = z.object({
	registrationId: z.number().min(1),
});

export const memberChangeMemberActionStatusSchema = z.object({
	memberActionStatus: z.enum([MEMBER_ACTIONS_STATUS.MEMBER_KEEP_ACCEPT, MEMBER_ACTIONS_STATUS.MEMBER_NOT_ACCEPTED]),
	registrationId: z.number().min(1),
});

export const memberConfirmOfficeAttendSchema = z.object({
	registrationId: z.number().min(1),
});

export type MemberRegistrationForEventSchema = z.infer<typeof memberRegistrationForEventSchema>;
export type MemberGetRegistration = z.infer<typeof memberGetRegistrationSchema>;
export type MemberCancelledRegistrationSchema = z.infer<typeof memberCancelledRegistrationSchema>;
export type MemberGetRegistrationByInterviewStatusesSchema = z.infer<
	typeof memberGetRegistrationByInterviewStatusesSchema
>;
export type AdminUpdateInterviewStatusSchema = z.infer<typeof adminUpdateInterviewStatusSchema>;
export type AdminRegistrationByInterviewStepSchema = z.infer<typeof adminRegistrationByInterviewStepSchema>;
export type GetRegistrationByIdSchema = z.infer<typeof getRegistrationByIdSchema>;
export type ConfirmInterviewNextStepSchema = z.infer<typeof confirmInterviewNextStepSchema>;
export type MemberChangeMemberActionStatusSchema = z.infer<typeof memberChangeMemberActionStatusSchema>;
export type MemberConfirmOfficeAttendSchema = z.infer<typeof memberConfirmOfficeAttendSchema>;
